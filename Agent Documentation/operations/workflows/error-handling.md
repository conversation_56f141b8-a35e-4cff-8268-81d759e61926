# Workflow Error Handling - Implementation Guide

> **📖 Primary Reference**: See [RUST-SS Error Handling Patterns](../../architectural-concerns/error-handling.md) for the complete unified error handling framework.

## Overview

This document provides workflow-specific error handling patterns and implementation details. For comprehensive error handling strategies, circuit breakers, retry policies, and recovery mechanisms, refer to the [centralized error handling documentation](../../architectural-concerns/error-handling.md).

## Core Error Handling Patterns

### Workflow-Specific Error Patterns

For core circuit breaker and error classification patterns, see the [centralized error handling documentation](../../architectural-concerns/error-handling.md).

Workflow-specific considerations:

```typescript
// Workflow-specific error handling extending base patterns
class WorkflowErrorHandler extends BaseErrorHandler {
  private workflowContext: WorkflowContext;
  
  async handleWorkflowError(
    error: WorkflowError,
    context: WorkflowExecutionContext
  ): Promise<WorkflowRecoveryResult> {
    // Apply workflow-specific error classification
    const classification = this.classifyWorkflowError(error);
    
    switch (classification.category) {
      case 'task-dependency':
        return await this.handleDependencyFailure(error, context);
      case 'resource-contention':
        return await this.handleResourceContention(error, context);
      case 'state-corruption':
        return await this.handleStateCorruption(error, context);
      default:
        // Delegate to base error handler
        return await super.handleError(error, context);
    }
  }
  
  private async handleDependencyFailure(
    error: WorkflowError,
    context: WorkflowExecutionContext
  ): Promise<WorkflowRecoveryResult> {
    // Attempt to reschedule dependent tasks
    const dependentTasks = await this.identifyDependentTasks(error);
    return await this.rescheduleWithDelay(dependentTasks, context);
  }
}
```

## Workflow Error Handling Configuration

### Comprehensive Error Handling Strategy (JSON)
```json
{
  "errorHandling": {
    "globalStrategy": "continue-on-non-critical",
    "retryPolicy": {
      "maxRetries": 3,
      "backoff": "exponential",
      "initialDelay": 1000,
      "maxDelay": 30000,
      "jitter": true
    },
    "circuitBreaker": {
      "enabled": true,
      "failureThreshold": 5,
      "successThreshold": 2,
      "timeout": 60000,
      "resetTimeout": 30000
    },
    "taskSpecificHandling": [
      {
        "taskType": "implementation",
        "strategy": "retry-with-fallback",
        "maxRetries": 2,
        "fallbackAction": "reassign-to-backup-team",
        "criticalityLevel": "medium"
      },
      {
        "taskType": "deployment",
        "strategy": "immediate-rollback",
        "rollbackTimeout": 120,
        "healthCheckRequired": true,
        "criticalityLevel": "critical"
      },
      {
        "taskType": "testing",
        "strategy": "continue-partial",
        "partialSuccessThreshold": 80,
        "reportPartialResults": true,
        "criticalityLevel": "low"
      }
    ],
    "escalation": {
      "levels": [
        {
          "trigger": "single-task-failure",
          "notify": ["assigned-agent", "team-lead"],
          "timeout": 300
        },
        {
          "trigger": "multiple-task-failures",
          "notify": ["team-lead", "project-manager"],
          "timeout": 600
        },
        {
          "trigger": "critical-task-failure",
          "notify": ["project-manager", "technical-lead", "stakeholders"],
          "timeout": 60,
          "escalateImmediately": true
        }
      ]
    },
    "rollback": {
      "enabled": true,
      "triggers": [
        "critical-failure",
        "security-breach",
        "data-corruption",
        "system-instability"
      ],
      "strategy": "previous-stable-state",
      "preserveData": true,
      "notificationRequired": true
    },
    "gracefulDegradation": {
      "enabled": true,
      "fallbackModes": [
        {
          "condition": "high-error-rate",
          "action": "reduce-parallelism",
          "parameters": {"maxConcurrent": 2}
        },
        {
          "condition": "resource-exhaustion", 
          "action": "queue-and-wait",
          "parameters": {"maxQueueSize": 100}
        },
        {
          "condition": "external-service-unavailable",
          "action": "offline-mode",
          "parameters": {"useCache": true}
        }
      ]
    }
  }
}
```

### Task-Level Error Configuration (JSON)
```json
{
  "tasks": [
    {
      "id": "critical-deployment",
      "type": "deployment",
      "errorHandling": {
        "strategy": "fail-fast",
        "maxRetries": 1,
        "timeout": 300000,
        "healthChecks": {
          "enabled": true,
          "interval": 10000,
          "failureThreshold": 3
        },
        "rollback": {
          "automatic": true,
          "timeout": 120000,
          "strategy": "blue-green-switch"
        },
        "notifications": {
          "onError": ["ops-team", "technical-lead"],
          "onRollback": ["stakeholders", "management"]
        }
      }
    },
    {
      "id": "data-processing",
      "type": "analysis",
      "errorHandling": {
        "strategy": "retry-with-checkpoint",
        "maxRetries": 5,
        "checkpointFrequency": "per-batch",
        "partialProcessing": {
          "enabled": true,
          "minSuccessRate": 70
        },
        "dataValidation": {
          "enabled": true,
          "schema": "data-schema.json",
          "corruption": "quarantine-and-continue"
        }
      }
    },
    {
      "id": "external-api-integration",
      "type": "integration",
      "errorHandling": {
        "strategy": "circuit-breaker-with-fallback",
        "circuitBreaker": {
          "failureThreshold": 3,
          "timeout": 30000,
          "resetTimeout": 60000
        },
        "fallback": {
          "strategy": "cached-response",
          "cacheTimeout": 3600000
        },
        "rateLimiting": {
          "enabled": true,
          "requestsPerMinute": 100,
          "burstLimit": 10
        }
      }
    }
  ]
}
```

## Retry Policies and Backoff Strategies

### Advanced Retry Configuration (TypeScript)
```typescript
class RetryManager {
  private policies: Map<string, RetryPolicy>;
  private backoffStrategies: Map<string, BackoffStrategy>;

  constructor() {
    this.setupPolicies();
    this.setupBackoffStrategies();
  }

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    policyName: string,
    context: RetryContext
  ): Promise<T> {
    const policy = this.policies.get(policyName);
    if (!policy) {
      throw new Error(`Retry policy '${policyName}' not found`);
    }

    const backoffStrategy = this.backoffStrategies.get(policy.backoffType);
    let lastError: Error;
    
    for (let attempt = 1; attempt <= policy.maxRetries; attempt++) {
      try {
        const result = await operation();
        
        // Log successful retry if not first attempt
        if (attempt > 1) {
          this.logRetrySuccess(attempt, context);
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        // Check if error is retryable
        if (!this.isRetryableError(error, policy)) {
          throw error;
        }
        
        // Check if we've exhausted retries
        if (attempt >= policy.maxRetries) {
          break;
        }
        
        // Calculate and apply backoff delay
        const delay = backoffStrategy.calculateDelay(attempt, policy.baseDelay);
        this.logRetryAttempt(attempt, delay, error, context);
        
        await this.sleep(delay);
      }
    }
    
    // All retries exhausted
    throw new MaxRetriesExceededError(
      `Operation failed after ${policy.maxRetries} attempts`,
      lastError,
      context
    );
  }

  private setupPolicies(): void {
    this.policies.set('standard', {
      maxRetries: 3,
      baseDelay: 1000,
      backoffType: 'exponential',
      retryableErrors: ['timeout', 'network', 'resource'],
      jitter: true
    });

    this.policies.set('aggressive', {
      maxRetries: 5,
      baseDelay: 500,
      backoffType: 'exponential-with-jitter',
      retryableErrors: ['timeout', 'network', 'resource', 'temporary'],
      jitter: true
    });

    this.policies.set('conservative', {
      maxRetries: 2,
      baseDelay: 5000,
      backoffType: 'linear',
      retryableErrors: ['timeout', 'network'],
      jitter: false
    });
  }

  private setupBackoffStrategies(): void {
    this.backoffStrategies.set('exponential', {
      calculateDelay: (attempt: number, baseDelay: number) => 
        Math.min(baseDelay * Math.pow(2, attempt - 1), 30000)
    });

    this.backoffStrategies.set('linear', {
      calculateDelay: (attempt: number, baseDelay: number) => 
        baseDelay * attempt
    });

    this.backoffStrategies.set('exponential-with-jitter', {
      calculateDelay: (attempt: number, baseDelay: number) => {
        const exponentialDelay = Math.min(baseDelay * Math.pow(2, attempt - 1), 30000);
        const jitter = Math.random() * exponentialDelay * 0.1; // 10% jitter
        return exponentialDelay + jitter;
      }
    });
  }
}
```

### CLI Retry Configuration
```bash
# Configure task with retry policy
claude-flow task create implementation "External API integration" \
  --retry-policy "aggressive" \
  --max-retries 5 \
  --backoff-strategy "exponential-with-jitter" \
  --timeout 30s \
  --retryable-errors "timeout,network,rate-limit"

# Configure workflow-level retry settings
claude-flow workflow configure-retry workflow-456 \
  --global-policy "standard" \
  --critical-task-policy "conservative" \
  --external-service-policy "aggressive" \
  --circuit-breaker-enabled
```

## Rollback Mechanisms

### Automatic Rollback Implementation (TypeScript)
```typescript
class RollbackManager {
  private checkpoints: Map<string, Checkpoint>;
  private rollbackStrategies: Map<string, RollbackStrategy>;
  private stateManager: StateManager;

  constructor(stateManager: StateManager) {
    this.checkpoints = new Map();
    this.rollbackStrategies = new Map();
    this.stateManager = stateManager;
    this.setupRollbackStrategies();
  }

  async createCheckpoint(
    workflowId: string,
    checkpointId: string,
    metadata?: CheckpointMetadata
  ): Promise<void> {
    const checkpoint: Checkpoint = {
      id: checkpointId,
      workflowId,
      createdAt: new Date(),
      state: await this.stateManager.captureState(workflowId),
      metadata: metadata || {},
      version: this.generateVersion()
    };

    this.checkpoints.set(`${workflowId}:${checkpointId}`, checkpoint);
    
    // Persist checkpoint for recovery
    await this.stateManager.persistCheckpoint(checkpoint);
    
    this.logCheckpointCreated(checkpoint);
  }

  async rollbackToCheckpoint(
    workflowId: string,
    checkpointId: string,
    options: RollbackOptions = {}
  ): Promise<RollbackResult> {
    const checkpointKey = `${workflowId}:${checkpointId}`;
    const checkpoint = this.checkpoints.get(checkpointKey);
    
    if (!checkpoint) {
      throw new Error(`Checkpoint '${checkpointId}' not found for workflow '${workflowId}'`);
    }

    const strategy = this.rollbackStrategies.get(options.strategy || 'default');
    if (!strategy) {
      throw new Error(`Rollback strategy '${options.strategy}' not found`);
    }

    try {
      // Execute rollback strategy
      const result = await strategy.execute(checkpoint, options);
      
      // Restore workflow state
      await this.stateManager.restoreState(workflowId, checkpoint.state);
      
      // Notify stakeholders
      if (options.notifyStakeholders) {
        await this.notifyRollback(workflowId, checkpointId, result);
      }
      
      this.logRollbackSuccess(workflowId, checkpointId, result);
      
      return result;
    } catch (error) {
      this.logRollbackFailure(workflowId, checkpointId, error);
      throw new RollbackFailedError(
        `Failed to rollback workflow '${workflowId}' to checkpoint '${checkpointId}'`,
        error
      );
    }
  }

  async autoRollbackOnFailure(
    workflowId: string,
    error: Error,
    context: ExecutionContext
  ): Promise<RollbackResult | null> {
    const rollbackTriggers = await this.getRollbackTriggers(workflowId);
    
    for (const trigger of rollbackTriggers) {
      if (this.shouldTriggerRollback(error, trigger)) {
        const checkpoint = await this.findSuitableCheckpoint(workflowId, trigger);
        if (checkpoint) {
          return await this.rollbackToCheckpoint(workflowId, checkpoint.id, {
            strategy: trigger.strategy,
            reason: `Auto-rollback triggered by: ${error.message}`,
            preserveData: trigger.preserveData,
            notifyStakeholders: true
          });
        }
      }
    }
    
    return null;
  }

  private setupRollbackStrategies(): void {
    this.rollbackStrategies.set('default', new DefaultRollbackStrategy());
    this.rollbackStrategies.set('blue-green', new BlueGreenRollbackStrategy());
    this.rollbackStrategies.set('canary-rollback', new CanaryRollbackStrategy());
    this.rollbackStrategies.set('data-preserving', new DataPreservingRollbackStrategy());
  }
}
```

### Rollback Configuration (JSON)
```json
{
  "rollbackConfiguration": {
    "enabled": true,
    "automaticTriggers": [
      {
        "name": "critical-failure",
        "conditions": [
          "error.severity == 'critical'",
          "error.category == 'security'",
          "error.category == 'data-corruption'"
        ],
        "strategy": "immediate-rollback",
        "checkpointSelection": "latest-stable",
        "preserveData": false,
        "notificationRequired": true
      },
      {
        "name": "performance-degradation", 
        "conditions": [
          "metrics.response_time > 5000",
          "metrics.error_rate > 0.1"
        ],
        "strategy": "gradual-rollback",
        "checkpointSelection": "pre-deployment",
        "preserveData": true,
        "gracePeriod": 300000
      }
    ],
    "checkpointStrategy": {
      "frequency": "per-stage",
      "retention": "7d",
      "compression": true,
      "validation": true
    },
    "rollbackStrategies": {
      "immediate-rollback": {
        "timeout": 120000,
        "healthChecks": true,
        "verification": "required"
      },
      "gradual-rollback": {
        "phaseTimeout": 300000,
        "rollbackPercentage": [25, 50, 75, 100],
        "healthCheckBetweenPhases": true
      }
    }
  }
}
```

## CLI Error Handling Commands

### Error Recovery Operations
```bash
# Monitor workflow errors in real-time
claude-flow workflow monitor-errors workflow-456 \
  --real-time \
  --error-threshold "critical:0,high:5,medium:20" \
  --auto-escalate \
  --dashboard-alerts

# Manually trigger rollback
claude-flow workflow rollback workflow-456 \
  --to-checkpoint "pre-deployment-stable" \
  --strategy "blue-green" \
  --preserve-data \
  --notify-stakeholders \
  --reason "performance-regression-detected"

# Configure error handling for running workflow
claude-flow workflow configure-error-handling workflow-456 \
  --retry-policy "aggressive" \
  --circuit-breaker-enabled \
  --auto-rollback-triggers "critical-failure,security-breach" \
  --escalation-enabled
```

### Error Analysis and Debugging
```bash
# Analyze workflow errors and patterns
claude-flow workflow analyze-errors workflow-456 \
  --time-range "24h" \
  --group-by "error-type,task-type,agent" \
  --trend-analysis \
  --root-cause-suggestions \
  --export-report error-analysis.json

# Debug specific task failures
claude-flow task debug-error task-789 \
  --show-stack-trace \
  --show-context \
  --suggest-fixes \
  --check-dependencies \
  --validate-resources

# Recovery assistance
claude-flow workflow recover workflow-456 \
  --analyze-failure-point \
  --suggest-recovery-actions \
  --estimate-recovery-time \
  --check-data-integrity
```

## Graceful Degradation Patterns

### Adaptive Workflow Execution (TypeScript)
```typescript
class GracefulDegradationManager {
  private degradationPolicies: Map<string, DegradationPolicy>;
  private systemMonitor: SystemMonitor;
  private performanceMetrics: PerformanceMetrics;

  constructor() {
    this.degradationPolicies = new Map();
    this.systemMonitor = new SystemMonitor();
    this.performanceMetrics = new PerformanceMetrics();
    this.setupDegradationPolicies();
  }

  async evaluateAndApplyDegradation(
    workflowId: string,
    context: ExecutionContext
  ): Promise<DegradationDecision> {
    const systemHealth = await this.systemMonitor.getHealth();
    const workflowMetrics = await this.performanceMetrics.getWorkflowMetrics(workflowId);
    
    const degradationNeeded = this.assessDegradationNeed(systemHealth, workflowMetrics);
    
    if (degradationNeeded.required) {
      const policy = this.selectDegradationPolicy(degradationNeeded.triggers);
      const degradationPlan = await this.createDegradationPlan(policy, context);
      
      await this.applyDegradation(workflowId, degradationPlan);
      
      return {
        applied: true,
        policy: policy.name,
        plan: degradationPlan,
        reason: degradationNeeded.reason,
        expectedImpact: policy.impact,
        recoveryTime: policy.estimatedRecoveryTime
      };
    }

    return {
      applied: false,
      reason: 'System operating within normal parameters'
    };
  }

  private setupDegradationPolicies(): void {
    this.degradationPolicies.set('reduce-parallelism', {
      name: 'reduce-parallelism',
      triggers: ['high-cpu-usage', 'memory-pressure'],
      actions: [
        {
          type: 'limit-concurrent-tasks',
          parameters: { maxConcurrent: 2 }
        },
        {
          type: 'increase-task-timeout',
          parameters: { multiplier: 1.5 }
        }
      ],
      impact: 'Reduced throughput, maintained stability',
      estimatedRecoveryTime: 300000
    });

    this.degradationPolicies.set('offline-mode', {
      name: 'offline-mode',
      triggers: ['external-service-unavailable', 'network-issues'],
      actions: [
        {
          type: 'enable-cache-mode',
          parameters: { useStaleData: true, maxAge: 3600000 }
        },
        {
          type: 'disable-external-calls',
          parameters: { exemptions: ['critical-services'] }
        }
      ],
      impact: 'Limited functionality, cached data only',
      estimatedRecoveryTime: 1800000
    });

    this.degradationPolicies.set('safe-mode', {
      name: 'safe-mode',
      triggers: ['high-error-rate', 'data-corruption-risk'],
      actions: [
        {
          type: 'disable-write-operations',
          parameters: { readOnlyMode: true }
        },
        {
          type: 'enable-extra-validation',
          parameters: { strict: true }
        },
        {
          type: 'reduce-batch-sizes',
          parameters: { maxBatchSize: 10 }
        }
      ],
      impact: 'Read-only operations, enhanced safety',
      estimatedRecoveryTime: 600000
    });
  }
}
```

### Degradation Configuration (JSON)
```json
{
  "gracefulDegradation": {
    "enabled": true,
    "monitoringInterval": 30000,
    "policies": [
      {
        "name": "resource-conservation",
        "triggers": {
          "cpu_usage": "> 80%",
          "memory_usage": "> 85%",
          "disk_usage": "> 90%"
        },
        "actions": [
          {
            "type": "reduce_parallelism",
            "parameters": {
              "max_concurrent_tasks": 2,
              "queue_overflow_strategy": "wait"
            }
          },
          {
            "type": "increase_timeouts",
            "parameters": {
              "timeout_multiplier": 2.0,
              "max_timeout": 600000
            }
          }
        ],
        "recovery": {
          "conditions": {
            "cpu_usage": "< 60%",
            "memory_usage": "< 70%"
          },
          "gradual": true,
          "steps": 5
        }
      },
      {
        "name": "external-dependency-failure",
        "triggers": {
          "external_service_availability": "< 50%",
          "network_error_rate": "> 20%"
        },
        "actions": [
          {
            "type": "enable_cache_fallback",
            "parameters": {
              "max_cache_age": 7200000,
              "accept_stale_data": true
            }
          },
          {
            "type": "disable_non_critical_integrations",
            "parameters": {
              "critical_services": ["user-auth", "payment-processing"]
            }
          }
        ]
      }
    ]
  }
}
```

## State Recovery and Data Integrity

### State Recovery Manager (TypeScript)
```typescript
class StateRecoveryManager {
  private stateStore: StateStore;
  private integrityChecker: DataIntegrityChecker;
  private backupManager: BackupManager;

  constructor(stateStore: StateStore) {
    this.stateStore = stateStore;
    this.integrityChecker = new DataIntegrityChecker();
    this.backupManager = new BackupManager();
  }

  async recoverWorkflowState(
    workflowId: string,
    recoveryOptions: RecoveryOptions
  ): Promise<RecoveryResult> {
    try {
      // Attempt to recover from latest state
      const latestState = await this.stateStore.getLatestState(workflowId);
      
      if (latestState && await this.integrityChecker.validate(latestState)) {
        return await this.recoverFromState(latestState, recoveryOptions);
      }

      // Fall back to checkpoint recovery
      const checkpoints = await this.stateStore.getCheckpoints(workflowId);
      for (const checkpoint of checkpoints.reverse()) {
        if (await this.integrityChecker.validate(checkpoint.state)) {
          return await this.recoverFromCheckpoint(checkpoint, recoveryOptions);
        }
      }

      // Last resort: backup recovery
      const backup = await this.backupManager.getLatestBackup(workflowId);
      if (backup) {
        return await this.recoverFromBackup(backup, recoveryOptions);
      }

      throw new Error('No valid recovery point found');
    } catch (error) {
      return {
        success: false,
        error: error.message,
        recoveryPoint: null,
        dataLoss: 'unknown'
      };
    }
  }

  async validateAndRepairData(
    workflowId: string,
    validationRules: ValidationRule[]
  ): Promise<ValidationResult> {
    const state = await this.stateStore.getCurrentState(workflowId);
    const issues: ValidationIssue[] = [];
    const repairs: DataRepair[] = [];

    for (const rule of validationRules) {
      const ruleResult = await this.integrityChecker.applyRule(state, rule);
      
      if (!ruleResult.passed) {
        issues.push({
          rule: rule.name,
          severity: rule.severity,
          description: ruleResult.description,
          affectedData: ruleResult.affectedData
        });

        // Attempt automatic repair if possible
        if (rule.autoRepair) {
          const repair = await this.attemptRepair(state, rule, ruleResult);
          if (repair.success) {
            repairs.push(repair);
          }
        }
      }
    }

    return {
      workflowId,
      validatedAt: new Date(),
      issues,
      repairs,
      overallIntegrity: issues.length === 0 ? 'intact' : 'compromised',
      recommendedActions: this.generateRecommendations(issues)
    };
  }
}
```

## Monitoring and Alerting

### Error Monitoring Configuration (JSON)
```json
{
  "errorMonitoring": {
    "enabled": true,
    "realTimeAlerts": true,
    "alertChannels": ["slack", "email", "pagerduty"],
    "thresholds": {
      "error_rate": {
        "warning": 0.05,
        "critical": 0.1,
        "window": 300
      },
      "failure_count": {
        "warning": 5,
        "critical": 10,
        "window": 600
      },
      "response_time": {
        "warning": 5000,
        "critical": 10000,
        "window": 60
      }
    },
    "escalation": {
      "levels": [
        {
          "threshold": "warning",
          "recipients": ["dev-team"],
          "delay": 0
        },
        {
          "threshold": "critical",
          "recipients": ["dev-team", "ops-team", "management"],
          "delay": 300
        }
      ]
    },
    "patterns": {
      "cascade_failure": {
        "detection": "multiple_dependent_failures",
        "window": 600,
        "threshold": 3,
        "action": "auto_rollback"
      },
      "performance_degradation": {
        "detection": "increasing_response_times",
        "trend_window": 1800,
        "threshold_increase": 0.5,
        "action": "graceful_degradation"
      }
    }
  }
}
```

### Real-time Error Monitoring (CLI)
```bash
# Start comprehensive error monitoring
claude-flow monitor errors \
  --workflows "all" \
  --real-time \
  --alert-thresholds "critical:0,high:5,medium:20" \
  --escalation-enabled \
  --dashboard-port 8080 \
  --export-metrics

# Set up automated error response
claude-flow configure auto-response \
  --error-patterns "cascade-failure,performance-degradation" \
  --actions "auto-rollback,graceful-degradation,scale-resources" \
  --approval-required "critical-actions" \
  --dry-run-mode false
```

## Best Practices for Error Handling

1. **Implement Comprehensive Error Classification**: Categorize errors by type, severity, and recoverability
2. **Use Circuit Breakers**: Protect against cascade failures and system overload
3. **Design for Graceful Degradation**: Maintain partial functionality during failures
4. **Implement Automatic Rollback**: Enable quick recovery from critical failures
5. **Create Regular Checkpoints**: Enable recovery from known good states
6. **Monitor Error Patterns**: Detect and respond to systemic issues early
7. **Provide Clear Error Messages**: Help users and operators understand and resolve issues
8. **Test Recovery Procedures**: Regularly validate rollback and recovery mechanisms
9. **Implement Retry Policies**: Use intelligent retry strategies with backoff
10. **Maintain Data Integrity**: Ensure data consistency during error recovery

## Advanced Error Handling Patterns

### Compensating Transaction Pattern (TypeScript)
```typescript
class CompensatingTransactionManager {
  private transactions: Map<string, Transaction>;
  private compensationHandlers: Map<string, CompensationHandler>;

  async executeWithCompensation<T>(
    workflowId: string,
    operations: Operation[]
  ): Promise<T> {
    const transaction = new Transaction(workflowId);
    
    try {
      for (const operation of operations) {
        const result = await operation.execute();
        transaction.addCompensation(operation.createCompensation(result));
      }
      
      await transaction.commit();
      return transaction.getResult();
    } catch (error) {
      await this.compensate(transaction, error);
      throw error;
    }
  }

  private async compensate(transaction: Transaction, error: Error): Promise<void> {
    const compensations = transaction.getCompensations().reverse();
    
    for (const compensation of compensations) {
      try {
        await compensation.execute();
      } catch (compensationError) {
        // Log compensation failure but continue with others
        this.logCompensationFailure(compensation, compensationError);
      }
    }
  }
}
```

This workflow-specific error handling integrates with the [unified RUST-SS error handling framework](../../architectural-concerns/error-handling.md) to ensure robust workflow execution with automatic recovery, intelligent retry policies, and graceful degradation capabilities.