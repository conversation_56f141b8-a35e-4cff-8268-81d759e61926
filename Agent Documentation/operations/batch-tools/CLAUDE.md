# Batch Tools Documentation - RUST-SS Implementation Guide

## Overview

This directory contains comprehensive documentation for claude-code-flow batch operations and coordination patterns. All documentation is based on actual TypeScript/Python implementation patterns found in the claude-code-flow codebase.

**CRITICAL IMPLEMENTATION NOTE**: All code examples and patterns documented here are extracted directly from the claude-code-flow source code and must be implemented exactly as shown for compatibility with future agent/LLM systems.

## Directory Structure

```
operations/batch-tools/
├── CLAUDE.md               # This overview document
├── todowrite-patterns.md   # TodoWrite coordination and tracking
├── task-spawning.md        # Task creation and parallel execution  
├── coordination.md         # Agent coordination and synchronization
└── optimization.md         # Performance and resource management
```

## Core Batch Operation Concepts

### 1. TodoWrite Coordination Pattern
- Complex task breakdown with dependencies
- Status tracking (pending, in_progress, completed)
- Priority management and agent assignment
- Estimated time and dependency resolution

### 2. Task Spawning and Parallel Execution
- Multi-agent coordination with parallel execution
- Load balancing strategies (round-robin, weighted, adaptive)
- Resource-aware scheduling
- Batch optimization patterns

### 3. Coordination Mechanisms
- Event-driven coordination via message bus
- Circuit breaker patterns for fault tolerance
- Conflict resolution algorithms
- Work stealing for dynamic load balancing

### 4. Performance Optimization
- Batch processing with configurable concurrency
- Connection pooling and resource management
- Predictive load balancing
- Memory-efficient bounded collections

## Key Implementation Patterns

### TodoWrite Structure (JavaScript/TypeScript)
```javascript
TodoWrite([
  {
    id: "architecture_design",
    content: "Design system architecture and component interfaces",
    status: "pending",
    priority: "high",
    dependencies: [],
    estimatedTime: "60min",
    assignedAgent: "architect"
  },
  {
    id: "frontend_development", 
    content: "Develop React components and user interface",
    status: "pending",
    priority: "medium",
    dependencies: ["architecture_design"],
    estimatedTime: "120min",
    assignedAgent: "frontend_team"
  }
]);
```

### Task Coordination (TypeScript)
```typescript
// Launch coordinated agents using Task tool pattern
const agentIds = await coordinator.launchParallelAgents([
  {
    agentType: 'researcher',
    objective: 'Research microservices patterns',
    mode: 'researcher',
    memoryKey: 'microservices_research',
    batchOptimized: true
  },
  {
    agentType: 'architect',
    objective: 'Design system architecture',
    mode: 'architect', 
    memoryKey: 'system_architecture',
    batchOptimized: true
  }
], context);
```

### Batch Operations (TypeScript)
```typescript
const results = await coordinator.coordinateBatchOperations([
  {
    type: 'read',
    targets: ['src/**/*.ts'],
    configuration: { pattern: 'class.*{' }
  },
  {
    type: 'search',
    targets: ['docs/**/*.md'],
    configuration: { term: 'API documentation' }
  },
  {
    type: 'analyze',
    targets: ['package.json', 'tsconfig.json'],
    configuration: { focus: 'dependencies' }
  }
], context);
```

## Rust Implementation Patterns

### TodoWrite Structure in Rust
```rust
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use std::collections::HashSet;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Todo {
    pub id: String,
    pub content: String,
    pub status: TodoStatus,
    pub priority: Priority,
    pub dependencies: Vec<String>,
    pub estimated_time: String,
    pub assigned_agent: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum TodoStatus {
    Pending,
    InProgress,
    Completed,
    Blocked,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum Priority {
    High,
    Medium,
    Low,
}

// TodoWrite coordinator
pub struct TodoCoordinator {
    todos: Vec<Todo>,
    dependency_graph: petgraph::Graph<String, ()>,
}

impl TodoCoordinator {
    pub fn new() -> Self {
        Self {
            todos: Vec::new(),
            dependency_graph: petgraph::Graph::new(),
        }
    }
    
    pub fn write_todos(&mut self, todos: Vec<Todo>) -> Result<(), TodoError> {
        // Validate dependencies
        self.validate_dependencies(&todos)?;
        
        // Build dependency graph
        self.build_dependency_graph(&todos)?;
        
        // Check for cycles
        if self.has_cycles() {
            return Err(TodoError::CyclicDependency);
        }
        
        self.todos = todos;
        Ok(())
    }
    
    pub fn get_ready_tasks(&self) -> Vec<&Todo> {
        self.todos
            .iter()
            .filter(|todo| {
                todo.status == TodoStatus::Pending
                    && self.dependencies_satisfied(todo)
            })
            .collect()
    }
    
    fn dependencies_satisfied(&self, todo: &Todo) -> bool {
        todo.dependencies
            .iter()
            .all(|dep_id| {
                self.todos
                    .iter()
                    .find(|t| t.id == *dep_id)
                    .map_or(false, |t| t.status == TodoStatus::Completed)
            })
    }
}

// Example usage
fn create_development_todos() -> Vec<Todo> {
    vec![
        Todo {
            id: "architecture_design".to_string(),
            content: "Design system architecture and component interfaces".to_string(),
            status: TodoStatus::Pending,
            priority: Priority::High,
            dependencies: vec![],
            estimated_time: "60min".to_string(),
            assigned_agent: "architect".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        },
        Todo {
            id: "frontend_development".to_string(),
            content: "Develop React components and user interface".to_string(),
            status: TodoStatus::Pending,
            priority: Priority::Medium,
            dependencies: vec!["architecture_design".to_string()],
            estimated_time: "120min".to_string(),
            assigned_agent: "frontend_team".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        },
    ]
}
```

### Task Coordination with Async Patterns
```rust
use tokio::task::JoinSet;
use futures::stream::{self, StreamExt};
use std::sync::Arc;
use tokio::sync::{RwLock, Semaphore};

#[derive(Debug, Clone)]
pub struct AgentConfig {
    pub agent_type: AgentType,
    pub objective: String,
    pub mode: String,
    pub memory_key: String,
    pub batch_optimized: bool,
}

#[derive(Debug, Clone, Copy)]
pub enum AgentType {
    Researcher,
    Architect,
    Developer,
    Tester,
}

pub struct BatchCoordinator {
    agent_pool: Arc<RwLock<AgentPool>>,
    concurrency_limit: Arc<Semaphore>,
    memory_store: Arc<MemoryStore>,
}

impl BatchCoordinator {
    pub fn new(max_concurrent_agents: usize) -> Self {
        Self {
            agent_pool: Arc::new(RwLock::new(AgentPool::new())),
            concurrency_limit: Arc::new(Semaphore::new(max_concurrent_agents)),
            memory_store: Arc::new(MemoryStore::new()),
        }
    }
    
    pub async fn launch_parallel_agents(
        &self,
        configs: Vec<AgentConfig>,
    ) -> Result<Vec<String>, CoordinatorError> {
        let mut join_set = JoinSet::new();
        let mut agent_ids = Vec::with_capacity(configs.len());
        
        for config in configs {
            let permit = self.concurrency_limit.clone().acquire_owned().await?;
            let agent_pool = self.agent_pool.clone();
            let memory_store = self.memory_store.clone();
            
            let agent_id = uuid::Uuid::new_v4().to_string();
            agent_ids.push(agent_id.clone());
            
            join_set.spawn(async move {
                let _permit = permit; // Hold permit until task completes
                
                let result = Self::spawn_agent(
                    agent_id.clone(),
                    config,
                    agent_pool,
                    memory_store,
                ).await;
                
                (agent_id, result)
            });
        }
        
        // Wait for all agents to spawn
        while let Some(result) = join_set.join_next().await {
            match result {
                Ok((agent_id, Ok(()))) => {
                    tracing::info!("Agent {} spawned successfully", agent_id);
                }
                Ok((agent_id, Err(e))) => {
                    tracing::error!("Failed to spawn agent {}: {:?}", agent_id, e);
                    return Err(e);
                }
                Err(e) => {
                    tracing::error!("Join error: {:?}", e);
                    return Err(CoordinatorError::JoinError(e.to_string()));
                }
            }
        }
        
        Ok(agent_ids)
    }
    
    async fn spawn_agent(
        agent_id: String,
        config: AgentConfig,
        agent_pool: Arc<RwLock<AgentPool>>,
        memory_store: Arc<MemoryStore>,
    ) -> Result<(), CoordinatorError> {
        // Create agent based on type
        let agent = match config.agent_type {
            AgentType::Researcher => {
                Box::new(ResearcherAgent::new(agent_id.clone(), config.objective))
            }
            AgentType::Architect => {
                Box::new(ArchitectAgent::new(agent_id.clone(), config.objective))
            }
            _ => {
                return Err(CoordinatorError::UnsupportedAgentType);
            }
        };
        
        // Register agent in pool
        {
            let mut pool = agent_pool.write().await;
            pool.register(agent_id.clone(), agent)?;
        }
        
        // Store initial context in memory
        memory_store.set(&config.memory_key, serde_json::json!({
            "agent_id": agent_id,
            "objective": config.objective,
            "status": "initialized",
            "created_at": chrono::Utc::now(),
        })).await?;
        
        Ok(())
    }
}

// Batch operations with iterators
pub struct BatchOperationExecutor {
    file_reader: Arc<FileReader>,
    search_engine: Arc<SearchEngine>,
    analyzer: Arc<Analyzer>,
}

#[derive(Debug, Clone)]
pub struct BatchOperation {
    pub operation_type: OperationType,
    pub targets: Vec<String>,
    pub configuration: serde_json::Value,
}

#[derive(Debug, Clone)]
pub enum OperationType {
    Read,
    Search,
    Analyze,
}

impl BatchOperationExecutor {
    pub async fn coordinate_batch_operations(
        &self,
        operations: Vec<BatchOperation>,
    ) -> Result<Vec<OperationResult>, BatchError> {
        // Use Rust iterators with async stream processing
        let results = stream::iter(operations)
            .map(|op| self.execute_operation(op))
            .buffer_unordered(4) // Process up to 4 operations concurrently
            .collect::<Vec<_>>()
            .await;
        
        // Collect results and handle errors
        results.into_iter().collect()
    }
    
    async fn execute_operation(
        &self,
        operation: BatchOperation,
    ) -> Result<OperationResult, BatchError> {
        match operation.operation_type {
            OperationType::Read => {
                self.execute_read_batch(operation).await
            }
            OperationType::Search => {
                self.execute_search_batch(operation).await
            }
            OperationType::Analyze => {
                self.execute_analyze_batch(operation).await
            }
        }
    }
    
    async fn execute_read_batch(
        &self,
        operation: BatchOperation,
    ) -> Result<OperationResult, BatchError> {
        let pattern = operation.configuration
            .get("pattern")
            .and_then(|v| v.as_str())
            .ok_or(BatchError::InvalidConfiguration)?;
        
        // Use parallel iterator for file reading
        let results: Vec<_> = operation.targets
            .into_par_iter()
            .filter_map(|target| {
                match glob::glob(&target) {
                    Ok(paths) => Some(paths),
                    Err(_) => None,
                }
            })
            .flatten()
            .filter_map(|path_result| path_result.ok())
            .map(|path| {
                self.file_reader.read_with_pattern(&path, pattern)
            })
            .collect();
        
        Ok(OperationResult::Read(results))
    }
}

// Work stealing for dynamic load balancing
pub struct WorkStealingExecutor {
    workers: Vec<Worker>,
    global_queue: Arc<SegQueue<Task>>,
}

impl WorkStealingExecutor {
    pub fn new(num_workers: usize) -> Self {
        let global_queue = Arc::new(SegQueue::new());
        
        let workers = (0..num_workers)
            .map(|id| Worker::new(id, global_queue.clone()))
            .collect();
        
        Self {
            workers,
            global_queue,
        }
    }
    
    pub async fn execute_tasks(&self, tasks: Vec<Task>) {
        // Add all tasks to global queue
        for task in tasks {
            self.global_queue.push(task);
        }
        
        // Start all workers
        let handles: Vec<_> = self.workers
            .iter()
            .map(|worker| {
                let worker = worker.clone();
                tokio::spawn(async move {
                    worker.run().await;
                })
            })
            .collect();
        
        // Wait for all workers to complete
        for handle in handles {
            let _ = handle.await;
        }
    }
}
```

## CLI Integration Patterns

### Task Creation Commands (Bash)
```bash
# Create research task with high priority
claude-flow task create research "Analyze competitor AI development tools" \
  --priority high \
  --estimated-duration 2h \
  --required-capabilities "web-research,analysis"

# Create implementation task with dependencies
claude-flow task create implementation "Develop user authentication API" \
  --language "python" \
  --framework "fastapi" \
  --testing-required true \
  --dependencies "database-design,security-requirements"

# Create coordination task for multi-team sprint
claude-flow task create coordination "Coordinate multi-team sprint execution" \
  --teams "frontend,backend,qa,devops" \
  --sprint-duration "2-weeks" \
  --synchronization-points "daily-standup,weekly-review,retrospective"
```

### Batch Task Management (Bash)
```bash
# Batch pause tasks by criteria
claude-flow task batch-pause \
  --type "research" \
  --assigned-to "researcher-team" \
  --reason "team-meeting"

# Batch update task properties
claude-flow task batch-update \
  --filter "status:pending,created_after:2024-12-01" \
  --set "priority:high,deadline:2024-12-25"

# Bulk reassign tasks with load balancing
claude-flow task bulk-reassign \
  --from-agent "overloaded-agent" \
  --to-agents "backup-agent-1,backup-agent-2" \
  --strategy "load-balance"
```

## Dependencies and Prerequisites

### Required Components
- **Event Bus**: For real-time coordination and messaging
- **Memory System**: For persistent state and context sharing
- **Task Scheduler**: For priority-based task assignment
- **Resource Manager**: For computational resource allocation
- **Circuit Breaker**: For fault tolerance and error handling

### Integration Points
- **Claude API**: For LLM-based task execution
- **File System**: For persistent task state and results
- **Process Management**: For subprocess spawning and control
- **Network Communication**: For distributed coordination

## Usage Guidelines for Future Agents/LLMs

1. **Always use TodoWrite** for complex task coordination with multiple dependencies
2. **Implement batch optimization** when processing multiple similar tasks
3. **Enable parallel execution** with `--parallel` flags for maximum efficiency  
4. **Store coordination state** in memory for cross-agent communication
5. **Monitor progress** with TodoRead during long-running operations
6. **Handle errors gracefully** with circuit breaker patterns and retry logic

## Performance Considerations

- **Batch Size**: Optimal batch sizes of 5-10 tasks for memory efficiency
- **Concurrency Limits**: Maximum 8 parallel agents for resource management
- **Timeout Handling**: 300 second default timeouts with exponential backoff
- **Memory Usage**: Bounded collections to prevent memory leaks
- **Connection Pooling**: Reuse connections for improved performance

This documentation serves as the foundational reference for implementing batch operations in RUST-SS, ensuring compatibility with claude-code-flow patterns and future AI agent systems.