# External Identity Provider Integration

## Overview

This document outlines integration patterns for external identity providers with claude-code-flow enterprise RBAC systems, providing seamless authentication, authorization synchronization, and federated identity management.

## Identity Provider Integration Architecture

### SAML Integration Framework

Based on claude-code-flow's enterprise security policy and authentication methods:

```typescript
// SAML integration from claude-code-flow enterprise security
class SAMLIdentityProvider implements IdentityProvider {
  constructor(
    private samlConfig: SAMLConfiguration,
    private roleMapper: RoleMapper,
    private auditLogger: AuditLogger
  ) {}

  async authenticate(samlResponse: SAMLResponse): Promise<AuthenticationResult> {
    try {
      // Validate SAML response
      const validation = await this.validateSAMLResponse(samlResponse);
      if (!validation.valid) {
        throw new AuthenticationError(`SAML validation failed: ${validation.error}`);
      }

      // Extract user attributes
      const userAttributes = this.extractUserAttributes(samlResponse);
      
      // Map to internal identity
      const identity = await this.mapToInternalIdentity(userAttributes);
      
      // Synchronize roles and permissions
      await this.synchronizeRoles(identity, userAttributes);
      
      // Generate session token
      const sessionToken = await this.generateSessionToken(identity);
      
      // Log authentication event
      await this.auditLogger.logAuthentication({
        provider: 'saml',
        identity: identity.id,
        tenantId: identity.tenantId,
        success: true,
        attributes: userAttributes
      });

      return {
        success: true,
        identity,
        sessionToken,
        expiresAt: new Date(Date.now() + this.samlConfig.sessionTimeout)
      };

    } catch (error) {
      await this.auditLogger.logAuthentication({
        provider: 'saml',
        success: false,
        error: error.message,
        samlIssuer: samlResponse.issuer
      });
      
      throw error;
    }
  }

  private async validateSAMLResponse(response: SAMLResponse): Promise<SAMLValidation> {
    // Verify signature
    const signatureValid = await this.verifySignature(
      response.signature,
      response.assertion,
      this.samlConfig.idpCertificate
    );
    
    if (!signatureValid) {
      return { valid: false, error: 'Invalid signature' };
    }

    // Check assertion validity
    const now = new Date();
    if (response.assertion.notBefore > now || response.assertion.notOnOrAfter < now) {
      return { valid: false, error: 'Assertion time window invalid' };
    }

    // Verify issuer
    if (response.issuer !== this.samlConfig.expectedIssuer) {
      return { valid: false, error: 'Unexpected issuer' };
    }

    // Verify audience
    if (!response.assertion.audience.includes(this.samlConfig.spEntityId)) {
      return { valid: false, error: 'Invalid audience' };
    }

    return { valid: true };
  }

  private async synchronizeRoles(
    identity: InternalIdentity,
    attributes: SAMLAttributes
  ): Promise<void> {
    
    // Map SAML groups/roles to internal roles
    const mappedRoles = await this.roleMapper.mapSAMLRoles(
      attributes.groups || [],
      identity.tenantId
    );

    // Get current role assignments
    const currentRoles = await this.roleManager.getRoleAssignments(identity.id);
    
    // Calculate role changes
    const rolesToAdd = mappedRoles.filter(r => !currentRoles.includes(r));
    const rolesToRemove = currentRoles.filter(r => !mappedRoles.includes(r));

    // Apply role changes
    for (const role of rolesToAdd) {
      await this.roleManager.assignRole(identity.id, 'user', role, {
        source: 'saml-sync',
        tenantId: identity.tenantId
      });
    }

    for (const role of rolesToRemove) {
      await this.roleManager.revokeRole(identity.id, role, 'saml-sync-removal');
    }

    // Log synchronization
    if (rolesToAdd.length > 0 || rolesToRemove.length > 0) {
      await this.auditLogger.logRoleSynchronization({
        identity: identity.id,
        provider: 'saml',
        added: rolesToAdd,
        removed: rolesToRemove
      });
    }
  }
}
```

### OAuth 2.0 / OpenID Connect Integration

```typescript
// OAuth/OIDC integration for modern identity providers
class OIDCIdentityProvider implements IdentityProvider {
  constructor(
    private oidcConfig: OIDCConfiguration,
    private httpClient: HttpClient,
    private roleMapper: RoleMapper
  ) {}

  async authenticate(authorizationCode: string): Promise<AuthenticationResult> {
    // Exchange authorization code for tokens
    const tokenResponse = await this.exchangeCodeForTokens(authorizationCode);
    
    // Validate and decode ID token
    const idToken = await this.validateIDToken(tokenResponse.id_token);
    
    // Get user info from userinfo endpoint
    const userInfo = await this.getUserInfo(tokenResponse.access_token);
    
    // Map to internal identity
    const identity = await this.mapOIDCToInternalIdentity(idToken, userInfo);
    
    // Synchronize roles based on claims
    await this.synchronizeRolesFromClaims(identity, idToken.claims, userInfo);
    
    return {
      success: true,
      identity,
      sessionToken: await this.generateSessionToken(identity),
      refreshToken: tokenResponse.refresh_token,
      expiresAt: new Date(Date.now() + tokenResponse.expires_in * 1000)
    };
  }

  private async exchangeCodeForTokens(code: string): Promise<TokenResponse> {
    const tokenEndpoint = this.oidcConfig.tokenEndpoint;
    
    const response = await this.httpClient.post(tokenEndpoint, {
      grant_type: 'authorization_code',
      code,
      client_id: this.oidcConfig.clientId,
      client_secret: this.oidcConfig.clientSecret,
      redirect_uri: this.oidcConfig.redirectUri
    });

    if (!response.ok) {
      throw new AuthenticationError('Token exchange failed');
    }

    return response.data;
  }

  private async validateIDToken(idToken: string): Promise<IDToken> {
    // Get JWKS from provider
    const jwks = await this.getJWKS();
    
    // Verify token signature
    const decoded = await this.verifyJWT(idToken, jwks);
    
    // Validate claims
    if (decoded.aud !== this.oidcConfig.clientId) {
      throw new AuthenticationError('Invalid audience');
    }
    
    if (decoded.iss !== this.oidcConfig.issuer) {
      throw new AuthenticationError('Invalid issuer');
    }
    
    if (decoded.exp < Date.now() / 1000) {
      throw new AuthenticationError('Token expired');
    }

    return decoded;
  }

  private async synchronizeRolesFromClaims(
    identity: InternalIdentity,
    claims: JWTClaims,
    userInfo: UserInfo
  ): Promise<void> {
    
    // Extract role information from various sources
    const rolesSources = [
      claims.roles || [],
      claims.groups || [],
      userInfo.roles || [],
      userInfo.groups || []
    ].flat();

    // Map external roles to internal roles
    const mappedRoles = await this.roleMapper.mapOIDCRoles(
      rolesSources,
      identity.tenantId,
      claims
    );

    await this.applyRoleMappings(identity.id, mappedRoles, 'oidc-sync');
  }
}
```

## Role Mapping and Synchronization

### Dynamic Role Mapping Engine

```typescript
// Role mapping engine for external identity providers
class RoleMapper {
  constructor(
    private mappingStore: RoleMappingStore,
    private tenantManager: TenantManager
  ) {}

  async mapSAMLRoles(
    samlGroups: string[],
    tenantId: string
  ): Promise<string[]> {
    
    const mappingRules = await this.mappingStore.getSAMLMappings(tenantId);
    const mappedRoles: string[] = [];

    for (const group of samlGroups) {
      const matchingRules = mappingRules.filter(rule => 
        this.matchesPattern(group, rule.pattern)
      );

      for (const rule of matchingRules) {
        // Apply transformation if specified
        const transformedRole = rule.transformation 
          ? this.applyTransformation(group, rule.transformation)
          : rule.targetRole;
        
        mappedRoles.push(transformedRole);
      }
    }

    // Apply default roles if configured
    const defaultRoles = await this.getDefaultRoles(tenantId, 'saml');
    mappedRoles.push(...defaultRoles);

    return [...new Set(mappedRoles)]; // Remove duplicates
  }

  async mapOIDCRoles(
    oidcRoles: string[],
    tenantId: string,
    claims: JWTClaims
  ): Promise<string[]> {
    
    const mappingRules = await this.mappingStore.getOIDCMappings(tenantId);
    const mappedRoles: string[] = [];

    for (const rule of mappingRules) {
      const matches = await this.evaluateOIDCRule(rule, oidcRoles, claims);
      
      if (matches) {
        mappedRoles.push(...rule.targetRoles);
      }
    }

    return [...new Set(mappedRoles)];
  }

  private async evaluateOIDCRule(
    rule: OIDCMappingRule,
    roles: string[],
    claims: JWTClaims
  ): Promise<boolean> {
    
    // Evaluate conditions
    for (const condition of rule.conditions) {
      const conditionMet = await this.evaluateCondition(condition, roles, claims);
      
      if (!conditionMet) {
        return false;
      }
    }

    return true;
  }

  private async evaluateCondition(
    condition: MappingCondition,
    roles: string[],
    claims: JWTClaims
  ): Promise<boolean> {
    
    switch (condition.type) {
      case 'role-contains':
        return roles.some(role => role.includes(condition.value));
      
      case 'claim-equals':
        return claims[condition.field] === condition.value;
      
      case 'claim-contains':
        const claimValue = claims[condition.field];
        return Array.isArray(claimValue) 
          ? claimValue.includes(condition.value)
          : String(claimValue).includes(condition.value);
      
      case 'regex-match':
        return roles.some(role => new RegExp(condition.pattern).test(role));
      
      default:
        return false;
    }
  }
}

interface RoleMappingRule {
  id: string;
  tenantId: string;
  provider: 'saml' | 'oidc' | 'ldap';
  priority: number;
  pattern?: string;           // For pattern-based matching
  conditions?: MappingCondition[]; // For complex conditions
  targetRole?: string;        // Single target role
  targetRoles?: string[];     // Multiple target roles
  transformation?: RoleTransformation;
  active: boolean;
}

interface MappingCondition {
  type: 'role-contains' | 'claim-equals' | 'claim-contains' | 'regex-match';
  field?: string;             // For claim-based conditions
  value?: string;             // Expected value
  pattern?: string;           // For regex conditions
}
```

### Just-In-Time (JIT) Provisioning

```typescript
// JIT user provisioning for external identity providers
class JITProvisioningService {
  async provisionUser(
    externalIdentity: ExternalIdentity,
    provider: string
  ): Promise<InternalIdentity> {
    
    // Check if user already exists
    const existingUser = await this.findExistingUser(externalIdentity, provider);
    
    if (existingUser) {
      // Update existing user
      return await this.updateExistingUser(existingUser, externalIdentity, provider);
    }

    // Create new user
    return await this.createNewUser(externalIdentity, provider);
  }

  private async createNewUser(
    externalIdentity: ExternalIdentity,
    provider: string
  ): Promise<InternalIdentity> {
    
    const tenantId = await this.determineTenantId(externalIdentity, provider);
    
    const internalIdentity: InternalIdentity = {
      id: this.generateUserId(),
      tenantId,
      externalId: externalIdentity.id,
      provider,
      email: externalIdentity.email,
      displayName: externalIdentity.displayName,
      firstName: externalIdentity.firstName,
      lastName: externalIdentity.lastName,
      createdAt: new Date(),
      lastLoginAt: new Date(),
      status: 'active',
      
      // JIT provisioning metadata
      provisioningMethod: 'jit',
      provisionedAt: new Date(),
      lastSyncAt: new Date(),
      
      // Map attributes
      attributes: await this.mapUserAttributes(externalIdentity, tenantId)
    };

    // Store user
    await this.userStore.create(internalIdentity);
    
    // Provision default roles
    await this.provisionDefaultRoles(internalIdentity, provider);
    
    // Log provisioning event
    await this.auditLogger.logUserProvisioning({
      userId: internalIdentity.id,
      tenantId,
      provider,
      method: 'jit',
      externalId: externalIdentity.id
    });

    return internalIdentity;
  }

  private async updateExistingUser(
    existingUser: InternalIdentity,
    externalIdentity: ExternalIdentity,
    provider: string
  ): Promise<InternalIdentity> {
    
    // Update user attributes from external source
    const updatedUser = {
      ...existingUser,
      email: externalIdentity.email,
      displayName: externalIdentity.displayName,
      firstName: externalIdentity.firstName,
      lastName: externalIdentity.lastName,
      lastLoginAt: new Date(),
      lastSyncAt: new Date(),
      attributes: await this.mapUserAttributes(externalIdentity, existingUser.tenantId)
    };

    await this.userStore.update(updatedUser);
    
    // Sync roles if configured
    const tenantConfig = await this.tenantManager.getTenantConfig(existingUser.tenantId);
    if (tenantConfig.autoRoleSync) {
      await this.synchronizeUserRoles(updatedUser, externalIdentity, provider);
    }

    return updatedUser;
  }

  private async determineTenantId(
    externalIdentity: ExternalIdentity,
    provider: string
  ): Promise<string> {
    
    // Try email domain mapping first
    const emailDomain = externalIdentity.email.split('@')[1];
    const domainMapping = await this.tenantMappingStore.getByEmailDomain(emailDomain);
    
    if (domainMapping) {
      return domainMapping.tenantId;
    }

    // Try provider-specific mapping
    const providerMapping = await this.tenantMappingStore.getByProvider(provider);
    if (providerMapping) {
      return providerMapping.defaultTenantId;
    }

    // Use system default tenant
    return this.getSystemDefaultTenant();
  }
}
```

## Federation and Single Sign-On

### Federation Hub Implementation

```typescript
// Identity federation hub for multiple providers
class IdentityFederationHub {
  private providers = new Map<string, IdentityProvider>();

  constructor(
    private sessionManager: SessionManager,
    private auditLogger: AuditLogger
  ) {}

  async registerProvider(
    providerId: string,
    provider: IdentityProvider,
    config: ProviderConfiguration
  ): Promise<void> {
    
    // Validate provider configuration
    await this.validateProviderConfig(provider, config);
    
    // Register provider
    this.providers.set(providerId, provider);
    
    // Store configuration
    await this.providerConfigStore.store(providerId, config);
    
    // Log provider registration
    await this.auditLogger.logProviderRegistration({
      providerId,
      type: config.type,
      tenantId: config.tenantId
    });
  }

  async authenticateWithProvider(
    providerId: string,
    credentials: any
  ): Promise<FederatedAuthenticationResult> {
    
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    try {
      // Authenticate with external provider
      const authResult = await provider.authenticate(credentials);
      
      if (!authResult.success) {
        return {
          success: false,
          provider: providerId,
          error: authResult.error
        };
      }

      // Create federated session
      const federatedSession = await this.sessionManager.createFederatedSession({
        identity: authResult.identity,
        provider: providerId,
        externalSession: authResult.sessionToken,
        expiresAt: authResult.expiresAt
      });

      // Log successful federation
      await this.auditLogger.logFederatedAuthentication({
        userId: authResult.identity.id,
        provider: providerId,
        success: true,
        sessionId: federatedSession.id
      });

      return {
        success: true,
        provider: providerId,
        identity: authResult.identity,
        session: federatedSession
      };

    } catch (error) {
      await this.auditLogger.logFederatedAuthentication({
        provider: providerId,
        success: false,
        error: error.message
      });
      
      throw error;
    }
  }
}
```

### Single Sign-On (SSO) Flow

```typescript
// SSO flow implementation for multiple applications
class SSOManager {
  async initiateSSOFlow(
    request: SSORequest
  ): Promise<SSOFlowResult> {
    
    // Generate SSO session
    const ssoSession = await this.createSSOSession(request);
    
    // Determine appropriate identity provider
    const provider = await this.determineProvider(request);
    
    // Create authentication request
    const authRequest = await this.createAuthenticationRequest(
      provider,
      ssoSession,
      request
    );

    return {
      ssoSessionId: ssoSession.id,
      redirectUrl: authRequest.redirectUrl,
      provider: provider.id,
      expiresAt: authRequest.expiresAt
    };
  }

  async completeSSOFlow(
    ssoSessionId: string,
    authResponse: AuthenticationResponse
  ): Promise<SSOCompletionResult> {
    
    const ssoSession = await this.getSSOSession(ssoSessionId);
    if (!ssoSession) {
      throw new Error('SSO session not found');
    }

    // Validate authentication response
    const validation = await this.validateAuthResponse(authResponse, ssoSession);
    if (!validation.valid) {
      throw new Error(`SSO validation failed: ${validation.error}`);
    }

    // Complete authentication with provider
    const authResult = await this.federationHub.authenticateWithProvider(
      ssoSession.providerId,
      authResponse
    );

    if (!authResult.success) {
      throw new Error(`Authentication failed: ${authResult.error}`);
    }

    // Update SSO session
    await this.updateSSOSession(ssoSession.id, {
      status: 'completed',
      identity: authResult.identity,
      completedAt: new Date()
    });

    // Generate application tokens for requested applications
    const applicationTokens = await this.generateApplicationTokens(
      authResult.identity,
      ssoSession.requestedApplications
    );

    return {
      success: true,
      identity: authResult.identity,
      applicationTokens,
      ssoSession: ssoSession.id
    };
  }

  private async generateApplicationTokens(
    identity: InternalIdentity,
    applications: string[]
  ): Promise<ApplicationToken[]> {
    
    const tokens: ApplicationToken[] = [];
    
    for (const appId of applications) {
      const app = await this.applicationStore.get(appId);
      if (!app) continue;

      // Check if user has access to application
      const hasAccess = await this.authorizationEngine.authorize(
        { ...identity, principalType: 'user' },
        `application:${appId}`,
        'access'
      );

      if (!hasAccess.granted) continue;

      // Generate application-specific token
      const token = await this.tokenManager.generateApplicationToken({
        userId: identity.id,
        applicationId: appId,
        scope: app.defaultScope,
        expiresIn: app.tokenTTL
      });

      tokens.push({
        applicationId: appId,
        token,
        expiresAt: new Date(Date.now() + app.tokenTTL * 1000),
        scope: app.defaultScope
      });
    }

    return tokens;
  }
}
```

## Active Directory / LDAP Integration

### LDAP Identity Provider

```typescript
// LDAP/Active Directory integration
class LDAPIdentityProvider implements IdentityProvider {
  constructor(
    private ldapConfig: LDAPConfiguration,
    private ldapClient: LDAPClient
  ) {}

  async authenticate(credentials: LDAPCredentials): Promise<AuthenticationResult> {
    try {
      // Bind to LDAP server with user credentials
      await this.ldapClient.bind(credentials.username, credentials.password);
      
      // Search for user in directory
      const userEntry = await this.searchUser(credentials.username);
      if (!userEntry) {
        throw new AuthenticationError('User not found in directory');
      }

      // Extract user attributes
      const userAttributes = this.extractLDAPAttributes(userEntry);
      
      // Map to internal identity
      const identity = await this.mapLDAPToInternalIdentity(userAttributes);
      
      // Synchronize group memberships
      await this.synchronizeGroupMemberships(identity, userAttributes);
      
      return {
        success: true,
        identity,
        sessionToken: await this.generateSessionToken(identity)
      };

    } catch (error) {
      await this.auditLogger.logAuthentication({
        provider: 'ldap',
        username: credentials.username,
        success: false,
        error: error.message
      });
      
      throw error;
    } finally {
      // Always unbind from LDAP
      await this.ldapClient.unbind();
    }
  }

  private async searchUser(username: string): Promise<LDAPEntry | null> {
    const searchBase = this.ldapConfig.userSearchBase;
    const searchFilter = this.ldapConfig.userSearchFilter.replace('{username}', username);
    
    const searchResults = await this.ldapClient.search(searchBase, {
      filter: searchFilter,
      scope: 'sub',
      attributes: this.ldapConfig.userAttributes
    });

    return searchResults.length > 0 ? searchResults[0] : null;
  }

  private async synchronizeGroupMemberships(
    identity: InternalIdentity,
    userAttributes: LDAPAttributes
  ): Promise<void> {
    
    // Get user's group memberships from LDAP
    const groups = await this.getUserGroups(userAttributes.dn);
    
    // Map LDAP groups to internal roles
    const mappedRoles = await this.roleMapper.mapLDAPGroups(
      groups,
      identity.tenantId
    );

    // Apply role mappings
    await this.applyRoleMappings(identity.id, mappedRoles, 'ldap-sync');
  }

  private async getUserGroups(userDN: string): Promise<string[]> {
    const groupSearchBase = this.ldapConfig.groupSearchBase;
    const groupSearchFilter = this.ldapConfig.groupSearchFilter.replace('{userDN}', userDN);
    
    const groupResults = await this.ldapClient.search(groupSearchBase, {
      filter: groupSearchFilter,
      scope: 'sub',
      attributes: ['cn', 'distinguishedName']
    });

    return groupResults.map(group => group.cn);
  }
}
```

### Directory Synchronization Service

```typescript
// Continuous directory synchronization
class DirectorySynchronizationService {
  async synchronizeDirectory(
    providerId: string,
    syncConfig: DirectorySyncConfig
  ): Promise<SynchronizationResult> {
    
    const provider = await this.getProvider(providerId);
    const result: SynchronizationResult = {
      usersProcessed: 0,
      usersCreated: 0,
      usersUpdated: 0,
      usersDisabled: 0,
      groupsProcessed: 0,
      errors: []
    };

    try {
      // Synchronize users
      if (syncConfig.syncUsers) {
        const userSyncResult = await this.synchronizeUsers(provider, syncConfig);
        Object.assign(result, userSyncResult);
      }

      // Synchronize groups
      if (syncConfig.syncGroups) {
        const groupSyncResult = await this.synchronizeGroups(provider, syncConfig);
        result.groupsProcessed = groupSyncResult.groupsProcessed;
      }

      // Clean up orphaned accounts
      if (syncConfig.cleanupOrphaned) {
        await this.cleanupOrphanedAccounts(provider, syncConfig);
      }

    } catch (error) {
      result.errors.push({
        type: 'sync-error',
        message: error.message
      });
    }

    // Log synchronization results
    await this.auditLogger.logDirectorySync({
      providerId,
      result,
      timestamp: new Date()
    });

    return result;
  }

  private async synchronizeUsers(
    provider: DirectoryProvider,
    config: DirectorySyncConfig
  ): Promise<UserSyncResult> {
    
    const result: UserSyncResult = {
      usersProcessed: 0,
      usersCreated: 0,
      usersUpdated: 0,
      usersDisabled: 0
    };

    // Get users from directory
    const directoryUsers = await provider.getUsers(config.userFilter);
    
    for (const directoryUser of directoryUsers) {
      try {
        result.usersProcessed++;
        
        // Check if user exists internally
        const existingUser = await this.userStore.findByExternalId(
          directoryUser.id,
          provider.id
        );

        if (existingUser) {
          // Update existing user
          if (await this.shouldUpdateUser(existingUser, directoryUser)) {
            await this.updateUserFromDirectory(existingUser, directoryUser);
            result.usersUpdated++;
          }
        } else {
          // Create new user
          await this.createUserFromDirectory(directoryUser, provider.id);
          result.usersCreated++;
        }

      } catch (error) {
        // Continue processing other users
        console.error(`Error syncing user ${directoryUser.id}:`, error);
      }
    }

    return result;
  }
}
```

## Best Practices

### 1. Security Considerations

- **Certificate Management**: Properly manage and rotate certificates
- **Token Validation**: Thoroughly validate all external tokens
- **Session Security**: Implement secure session management across providers

### 2. Performance Optimization

- **Connection Pooling**: Use connection pooling for LDAP/database connections
- **Caching**: Cache role mappings and user attributes appropriately
- **Async Processing**: Use asynchronous processing for bulk operations

### 3. Operational Excellence

- **Monitoring**: Monitor identity provider health and performance
- **Fallback Mechanisms**: Implement fallback authentication methods
- **Error Handling**: Provide meaningful error messages and logging

### 4. Compliance and Governance

- **Audit Trails**: Maintain comprehensive audit logs for all identity operations
- **Data Privacy**: Ensure compliance with data protection regulations
- **Access Reviews**: Implement regular access reviews and certifications

## Integration Points

- **RBAC System**: Role assignment and permission synchronization
- **Multi-Tenancy**: Tenant-aware identity mapping and isolation
- **Security Framework**: Authentication and session management
- **Audit System**: Identity event logging and compliance tracking

---

*External identity provider integration patterns derived from claude-code-flow enterprise security policy and authentication implementation*