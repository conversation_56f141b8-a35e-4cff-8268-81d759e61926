# RUST-SS Security Architecture

## Overview

This document serves as the single source of truth for all security patterns, authentication mechanisms, encryption strategies, and validation approaches across the RUST-SS framework. It adopts a zero-trust security model where every agent interaction must be authenticated and authorized, treating the network as inherently hostile.

## Core Security Principles

### Zero-Trust Architecture

```rust
#[derive(Debug, Clone)]
pub enum SecurityPrinciple {
    NeverTrustAlwaysVerify,      // Every request must be authenticated
    LeastPrivilege,              // Minimal permissions by default
    DefenseInDepth,             // Multiple security layers
    FailSecureClosed,           // Deny by default on errors
    ImmutableIdentity,          // Cryptographic agent identities
    CapabilityBasedAccess,      // Fine-grained permissions
    ShortLivedCredentials,      // Minimize exposure window
}
```

## Agent Identity Management

### SPIFFE/SPIRE Integration

```rust
use spiffe::{WorkloadApiClient, X509Context, X509Svid};
use std::sync::Arc;
use tokio::sync::RwLock;

#[derive(Clone)]
pub struct AgentIdentity {
    workload_client: Arc<WorkloadApiClient>,
    x509_context: Arc<RwLock<Option<X509Context>>>,
    agent_id: String,
    attestation_data: AttestationData,
}

#[derive(Debug, Clone)]
pub struct AttestationData {
    pub process_id: u32,
    pub binary_hash: String,
    pub launch_time: chrono::DateTime<chrono::Utc>,
    pub parent_agent_id: Option<String>,
    pub deployment_environment: String,
}

impl AgentIdentity {
    pub async fn bootstrap() -> Result<Self, SecurityError> {
        // Connect to local SPIRE agent via Unix domain socket
        let workload_client = WorkloadApiClient::new_from_env()
            .await
            .map_err(|e| SecurityError::IdentityBootstrapFailure(e.to_string()))?;
        
        // Fetch initial X.509 SVID
        let x509_context = workload_client
            .fetch_x509_context()
            .await
            .map_err(|e| SecurityError::SvidFetchFailure(e.to_string()))?;
        
        let svid = x509_context
            .default_svid()
            .ok_or_else(|| SecurityError::NoDefaultSvid)?;
        
        let agent_id = Self::extract_agent_id_from_svid(&svid)?;
        
        let identity = Self {
            workload_client: Arc::new(workload_client),
            x509_context: Arc::new(RwLock::new(Some(x509_context))),
            agent_id,
            attestation_data: Self::collect_attestation_data()?,
        };
        
        // Start background SVID renewal
        identity.start_svid_renewal().await;
        
        Ok(identity)
    }
    
    async fn start_svid_renewal(&self) {
        let client = self.workload_client.clone();
        let context = self.x509_context.clone();
        
        tokio::spawn(async move {
            let mut stream = match client.stream_x509_contexts().await {
                Ok(s) => s,
                Err(e) => {
                    tracing::error!("Failed to start SVID stream: {}", e);
                    return;
                }
            };
            
            while let Some(result) = stream.next().await {
                match result {
                    Ok(new_context) => {
                        let mut ctx = context.write().await;
                        *ctx = Some(new_context);
                        tracing::info!("SVID renewed successfully");
                    }
                    Err(e) => {
                        tracing::error!("SVID renewal error: {}", e);
                    }
                }
            }
        });
    }
    
    pub async fn create_mtls_config(&self) -> Result<rustls::ClientConfig, SecurityError> {
        let context = self.x509_context.read().await;
        let ctx = context.as_ref().ok_or(SecurityError::NoActiveContext)?;
        
        let svid = ctx.default_svid().ok_or(SecurityError::NoDefaultSvid)?;
        
        // Extract certificate and private key
        let cert_chain = svid
            .cert_chain()
            .iter()
            .map(|cert| rustls::Certificate(cert.to_der()))
            .collect::<Vec<_>>();
        
        let private_key = rustls::PrivateKey(svid.private_key().to_der());
        
        // Build mTLS configuration
        let config = rustls::ClientConfig::builder()
            .with_safe_defaults()
            .with_root_certificates(self.build_trust_bundle(ctx)?)
            .with_client_auth_cert(cert_chain, private_key)
            .map_err(|e| SecurityError::TlsConfigError(e.to_string()))?;
        
        Ok(config)
    }
}
```

### mTLS Communication Layer

```rust
use rustls::{ClientConfig, ServerConfig};
use tokio_rustls::{TlsAcceptor, TlsConnector};
use std::sync::Arc;

pub struct SecureTransport {
    identity: Arc<AgentIdentity>,
    tls_connector: Option<TlsConnector>,
    tls_acceptor: Option<TlsAcceptor>,
    peer_verifier: Arc<PeerVerifier>,
}

impl SecureTransport {
    pub async fn new(identity: Arc<AgentIdentity>) -> Result<Self, SecurityError> {
        let client_config = identity.create_mtls_config().await?;
        let server_config = identity.create_mtls_server_config().await?;
        
        Ok(Self {
            identity,
            tls_connector: Some(TlsConnector::from(Arc::new(client_config))),
            tls_acceptor: Some(TlsAcceptor::from(Arc::new(server_config))),
            peer_verifier: Arc::new(PeerVerifier::new()),
        })
    }
    
    pub async fn connect_to_agent(
        &self,
        target_agent_id: &str,
        address: std::net::SocketAddr,
    ) -> Result<SecureConnection, SecurityError> {
        let connector = self.tls_connector.as_ref()
            .ok_or(SecurityError::TransportNotInitialized)?;
        
        // Establish TCP connection
        let tcp_stream = tokio::net::TcpStream::connect(address)
            .await
            .map_err(|e| SecurityError::ConnectionFailed(e.to_string()))?;
        
        // Perform TLS handshake
        let domain = rustls::ServerName::try_from(target_agent_id)
            .map_err(|e| SecurityError::InvalidPeerIdentity(e.to_string()))?;
        
        let tls_stream = connector
            .connect(domain, tcp_stream)
            .await
            .map_err(|e| SecurityError::TlsHandshakeFailed(e.to_string()))?;
        
        // Verify peer's SPIFFE ID
        let peer_certificates = tls_stream
            .get_ref()
            .1
            .peer_certificates()
            .ok_or(SecurityError::NoPeerCertificate)?;
        
        let peer_spiffe_id = self.peer_verifier
            .verify_peer_identity(peer_certificates, target_agent_id)?;
        
        Ok(SecureConnection {
            stream: tls_stream,
            peer_id: peer_spiffe_id,
            established_at: chrono::Utc::now(),
        })
    }
}
```

## Capability-Based Authorization

### Capability Token System

```rust
use jsonwebtoken::{encode, decode, Header, Algorithm, Validation, EncodingKey, DecodingKey};
use serde::{Deserialize, Serialize};
use std::collections::HashSet;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Capability {
    pub id: String,
    pub issuer: String,                    // Agent ID that issued this capability
    pub audience: String,                  // Target agent ID
    pub resource: ResourceIdentifier,      // Specific resource
    pub permissions: HashSet<Permission>,  // Allowed operations
    pub constraints: CapabilityConstraints,
    pub issued_at: chrono::DateTime<chrono::Utc>,
    pub expires_at: chrono::DateTime<chrono::Utc>,
    pub single_use: bool,
    pub delegation_allowed: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceIdentifier {
    pub resource_type: String,  // e.g., "storage", "compute", "data"
    pub resource_id: String,    // e.g., "s3://bucket/object", "task:12345"
    pub namespace: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Hash, Eq, PartialEq)]
pub enum Permission {
    Read,
    Write,
    Execute,
    Delete,
    Share,
    Delegate,
    Admin,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CapabilityConstraints {
    pub max_uses: Option<u32>,
    pub rate_limit: Option<RateLimit>,
    pub time_window: Option<TimeWindow>,
    pub data_filters: Option<Vec<DataFilter>>,
    pub network_restrictions: Option<NetworkRestrictions>,
}

pub struct CapabilityManager {
    signing_key: EncodingKey,
    validation_key: DecodingKey,
    capability_store: Arc<dyn CapabilityStore>,
    usage_tracker: Arc<UsageTracker>,
    revocation_list: Arc<RevocationList>,
}

impl CapabilityManager {
    pub async fn issue_capability(
        &self,
        request: CapabilityRequest,
        issuer_context: &SecurityContext,
    ) -> Result<String, AuthorizationError> {
        // Verify issuer has permission to issue this capability
        self.verify_issuer_authority(&request, issuer_context)?;
        
        // Validate resource exists and is accessible
        self.validate_resource_access(&request.resource).await?;
        
        // Apply policy constraints
        let constraints = self.apply_policy_constraints(
            &request.constraints,
            issuer_context,
        )?;
        
        let capability = Capability {
            id: uuid::Uuid::new_v4().to_string(),
            issuer: issuer_context.agent_id.clone(),
            audience: request.audience,
            resource: request.resource,
            permissions: request.permissions,
            constraints,
            issued_at: chrono::Utc::now(),
            expires_at: request.expires_at,
            single_use: request.single_use,
            delegation_allowed: request.delegation_allowed && 
                issuer_context.can_delegate(),
        };
        
        // Sign the capability token
        let token = encode(
            &Header::new(Algorithm::ES256),
            &capability,
            &self.signing_key,
        ).map_err(|e| AuthorizationError::TokenSigningFailed(e.to_string()))?;
        
        // Store capability for tracking
        self.capability_store.store(&capability).await?;
        
        // Emit audit event
        self.emit_capability_issued_event(&capability, issuer_context).await;
        
        Ok(token)
    }
    
    pub async fn verify_capability(
        &self,
        token: &str,
        operation_context: &OperationContext,
    ) -> Result<Capability, AuthorizationError> {
        // Check revocation list first
        if self.revocation_list.is_revoked(token).await? {
            return Err(AuthorizationError::CapabilityRevoked);
        }
        
        // Decode and validate token
        let token_data = decode::<Capability>(
            token,
            &self.validation_key,
            &Validation::new(Algorithm::ES256),
        ).map_err(|e| AuthorizationError::InvalidToken(e.to_string()))?;
        
        let capability = token_data.claims;
        
        // Verify temporal validity
        let now = chrono::Utc::now();
        if now < capability.issued_at || now > capability.expires_at {
            return Err(AuthorizationError::TokenExpired);
        }
        
        // Verify audience matches current agent
        if capability.audience != operation_context.agent_id {
            return Err(AuthorizationError::InvalidAudience);
        }
        
        // Verify requested operation is permitted
        if !capability.permissions.contains(&operation_context.requested_permission) {
            return Err(AuthorizationError::InsufficientPermissions);
        }
        
        // Check usage constraints
        self.verify_constraints(&capability, operation_context).await?;
        
        // Track usage if single-use
        if capability.single_use {
            self.usage_tracker.mark_used(&capability.id).await?;
        }
        
        Ok(capability)
    }
    
    async fn verify_constraints(
        &self,
        capability: &Capability,
        context: &OperationContext,
    ) -> Result<(), AuthorizationError> {
        let constraints = &capability.constraints;
        
        // Check usage count
        if let Some(max_uses) = constraints.max_uses {
            let current_uses = self.usage_tracker
                .get_usage_count(&capability.id)
                .await?;
            if current_uses >= max_uses {
                return Err(AuthorizationError::UsageLimitExceeded);
            }
        }
        
        // Check rate limits
        if let Some(rate_limit) = &constraints.rate_limit {
            self.verify_rate_limit(&capability.id, rate_limit).await?;
        }
        
        // Verify network restrictions
        if let Some(network_restrictions) = &constraints.network_restrictions {
            self.verify_network_restrictions(context, network_restrictions)?;
        }
        
        Ok(())
    }
}
```

## Encryption and Key Management

### Vault Integration for Key Management

```rust
use vaultrs::{
    client::{VaultClient, VaultClientSettingsBuilder},
    api::transit::requests::{EncryptDataRequest, DecryptDataRequest},
};
use std::sync::Arc;
use tokio::sync::RwLock;

pub struct KeyManagementService {
    vault_client: Arc<VaultClient>,
    key_cache: Arc<RwLock<KeyCache>>,
    key_rotation_scheduler: Arc<KeyRotationScheduler>,
}

impl KeyManagementService {
    pub async fn new(
        vault_addr: &str,
        identity: &AgentIdentity,
    ) -> Result<Self, SecurityError> {
        // Use SPIFFE SVID to authenticate to Vault
        let auth_token = identity.authenticate_to_vault().await?;
        
        let client = VaultClient::new(
            VaultClientSettingsBuilder::default()
                .address(vault_addr)
                .token(auth_token)
                .build()
                .map_err(|e| SecurityError::VaultConfigError(e.to_string()))?
        ).map_err(|e| SecurityError::VaultConnectionError(e.to_string()))?;
        
        let service = Self {
            vault_client: Arc::new(client),
            key_cache: Arc::new(RwLock::new(KeyCache::new())),
            key_rotation_scheduler: Arc::new(KeyRotationScheduler::new()),
        };
        
        // Start key rotation monitoring
        service.start_key_rotation_monitor().await;
        
        Ok(service)
    }
    
    pub async fn encrypt_data(
        &self,
        plaintext: &[u8],
        key_name: &str,
        context: Option<EncryptionContext>,
    ) -> Result<EncryptedData, SecurityError> {
        let base64_plaintext = base64::encode(plaintext);
        
        let mut request = EncryptDataRequest::new(key_name, &base64_plaintext);
        
        if let Some(ctx) = context {
            request = request.context(base64::encode(ctx.to_bytes()));
        }
        
        let response = self.vault_client
            .transit()
            .encrypt(&request)
            .await
            .map_err(|e| SecurityError::EncryptionFailed(e.to_string()))?;
        
        Ok(EncryptedData {
            ciphertext: response.ciphertext,
            key_version: response.key_version,
            key_name: key_name.to_string(),
            encrypted_at: chrono::Utc::now(),
            context_hash: context.map(|c| c.hash()),
        })
    }
    
    pub async fn decrypt_data(
        &self,
        encrypted: &EncryptedData,
        context: Option<EncryptionContext>,
    ) -> Result<Vec<u8>, SecurityError> {
        let mut request = DecryptDataRequest::new(
            &encrypted.key_name,
            &encrypted.ciphertext,
        );
        
        if let Some(ctx) = context {
            // Verify context hash matches
            if let Some(stored_hash) = &encrypted.context_hash {
                if stored_hash != &ctx.hash() {
                    return Err(SecurityError::ContextMismatch);
                }
            }
            request = request.context(base64::encode(ctx.to_bytes()));
        }
        
        let response = self.vault_client
            .transit()
            .decrypt(&request)
            .await
            .map_err(|e| SecurityError::DecryptionFailed(e.to_string()))?;
        
        base64::decode(&response.plaintext)
            .map_err(|e| SecurityError::InvalidPlaintext(e.to_string()))
    }
    
    pub async fn create_encryption_key(
        &self,
        key_name: &str,
        key_config: KeyConfiguration,
    ) -> Result<(), SecurityError> {
        let request = vaultrs::api::transit::requests::CreateKeyRequest {
            convergent_encryption: key_config.convergent_encryption,
            derived: key_config.derived,
            exportable: false, // Never allow key export
            allow_plaintext_backup: false,
            key_type: key_config.key_type,
        };
        
        self.vault_client
            .transit()
            .create_key(key_name, Some(&request))
            .await
            .map_err(|e| SecurityError::KeyCreationFailed(e.to_string()))?;
        
        // Schedule automatic rotation
        if let Some(rotation_period) = key_config.auto_rotation_period {
            self.key_rotation_scheduler
                .schedule_rotation(key_name, rotation_period)
                .await?;
        }
        
        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct EncryptionContext {
    pub operation_id: String,
    pub agent_id: String,
    pub resource_id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub additional_data: std::collections::HashMap<String, String>,
}

impl EncryptionContext {
    pub fn to_bytes(&self) -> Vec<u8> {
        // Deterministic serialization for context
        let mut hasher = sha2::Sha256::new();
        hasher.update(&self.operation_id);
        hasher.update(&self.agent_id);
        hasher.update(&self.resource_id);
        hasher.update(self.timestamp.to_rfc3339());
        
        for (k, v) in &self.additional_data {
            hasher.update(k);
            hasher.update(v);
        }
        
        hasher.finalize().to_vec()
    }
    
    pub fn hash(&self) -> String {
        hex::encode(self.to_bytes())
    }
}
```

## Input Validation and Sanitization

### Comprehensive Validation Framework

```rust
use validator::{Validate, ValidationError};
use regex::Regex;
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub struct ValidationRule {
    pub field_name: String,
    pub validators: Vec<Box<dyn Validator>>,
    pub sanitizers: Vec<Box<dyn Sanitizer>>,
    pub required: bool,
}

pub trait Validator: Send + Sync {
    fn validate(&self, value: &serde_json::Value) -> Result<(), ValidationError>;
    fn name(&self) -> &str;
}

pub trait Sanitizer: Send + Sync {
    fn sanitize(&self, value: serde_json::Value) -> serde_json::Value;
    fn name(&self) -> &str;
}

pub struct InputValidator {
    rules: HashMap<String, Vec<ValidationRule>>,
    custom_validators: HashMap<String, Box<dyn Validator>>,
    rate_limiter: Arc<RateLimiter>,
}

impl InputValidator {
    pub async fn validate_request<T: Validate + serde::de::DeserializeOwned>(
        &self,
        input: &serde_json::Value,
        context: &SecurityContext,
    ) -> Result<T, ValidationError> {
        // Rate limit validation requests per agent
        self.rate_limiter
            .check_rate_limit(&context.agent_id, "validation")
            .await
            .map_err(|_| ValidationError::new("rate_limit_exceeded"))?;
        
        // Apply sanitization rules
        let sanitized = self.sanitize_input(input)?;
        
        // Deserialize with validation
        let parsed: T = serde_json::from_value(sanitized)
            .map_err(|e| ValidationError::new("invalid_json"))?;
        
        // Run built-in validation
        parsed.validate()?;
        
        // Apply custom validation rules
        self.apply_custom_rules(&parsed, context)?;
        
        Ok(parsed)
    }
    
    fn sanitize_input(&self, input: &serde_json::Value) -> Result<serde_json::Value, ValidationError> {
        let mut sanitized = input.clone();
        
        // Common sanitization patterns
        self.strip_null_bytes(&mut sanitized);
        self.normalize_unicode(&mut sanitized);
        self.escape_html_entities(&mut sanitized);
        self.validate_json_depth(&sanitized, 10)?; // Max nesting depth
        
        Ok(sanitized)
    }
    
    fn validate_json_depth(&self, value: &serde_json::Value, max_depth: usize) -> Result<(), ValidationError> {
        fn check_depth(value: &serde_json::Value, current_depth: usize, max: usize) -> bool {
            if current_depth > max {
                return false;
            }
            
            match value {
                serde_json::Value::Object(map) => {
                    map.values().all(|v| check_depth(v, current_depth + 1, max))
                }
                serde_json::Value::Array(arr) => {
                    arr.iter().all(|v| check_depth(v, current_depth + 1, max))
                }
                _ => true,
            }
        }
        
        if !check_depth(value, 0, max_depth) {
            return Err(ValidationError::new("excessive_nesting"));
        }
        
        Ok(())
    }
}

// Common validators
pub struct RegexValidator {
    pattern: Regex,
    error_message: String,
}

impl Validator for RegexValidator {
    fn validate(&self, value: &serde_json::Value) -> Result<(), ValidationError> {
        if let Some(s) = value.as_str() {
            if !self.pattern.is_match(s) {
                return Err(ValidationError::new(&self.error_message));
            }
        }
        Ok(())
    }
    
    fn name(&self) -> &str {
        "regex"
    }
}

pub struct LengthValidator {
    min: Option<usize>,
    max: Option<usize>,
}

impl Validator for LengthValidator {
    fn validate(&self, value: &serde_json::Value) -> Result<(), ValidationError> {
        let len = match value {
            serde_json::Value::String(s) => s.len(),
            serde_json::Value::Array(a) => a.len(),
            serde_json::Value::Object(o) => o.len(),
            _ => return Ok(()),
        };
        
        if let Some(min) = self.min {
            if len < min {
                return Err(ValidationError::new("too_short"));
            }
        }
        
        if let Some(max) = self.max {
            if len > max {
                return Err(ValidationError::new("too_long"));
            }
        }
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        "length"
    }
}

// SQL injection prevention
pub struct SqlInjectionValidator;

impl Validator for SqlInjectionValidator {
    fn validate(&self, value: &serde_json::Value) -> Result<(), ValidationError> {
        if let Some(s) = value.as_str() {
            let dangerous_patterns = [
                r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|CREATE|ALTER)\b)",
                r"(--|#|/\*|\*/)",
                r"(\bOR\b.*=.*)",
                r"('|(\')|\")",
            ];
            
            for pattern in &dangerous_patterns {
                let re = Regex::new(pattern).unwrap();
                if re.is_match(&s.to_uppercase()) {
                    return Err(ValidationError::new("potential_sql_injection"));
                }
            }
        }
        Ok(())
    }
    
    fn name(&self) -> &str {
        "sql_injection"
    }
}
```

## Security Boundaries and Isolation

### Agent Sandboxing

```rust
use nix::unistd::{Uid, Gid};
use nix::sys::resource::{Resource, setrlimit};
use std::collections::HashSet;

pub struct SecurityBoundary {
    allowed_capabilities: HashSet<Capability>,
    resource_limits: ResourceLimits,
    network_policy: NetworkPolicy,
    filesystem_policy: FilesystemPolicy,
}

#[derive(Debug, Clone)]
pub struct ResourceLimits {
    pub memory_limit_bytes: u64,
    pub cpu_shares: u64,
    pub max_file_descriptors: u64,
    pub max_processes: u64,
    pub max_file_size_bytes: u64,
}

impl SecurityBoundary {
    pub async fn enforce(&self) -> Result<(), SecurityError> {
        // Drop privileges if running as root
        self.drop_privileges()?;
        
        // Set resource limits
        self.apply_resource_limits()?;
        
        // Configure seccomp filters
        self.apply_seccomp_filter()?;
        
        // Set up network namespace if required
        if self.network_policy.isolated {
            self.create_network_namespace()?;
        }
        
        // Apply filesystem restrictions
        self.apply_filesystem_policy()?;
        
        Ok(())
    }
    
    fn drop_privileges(&self) -> Result<(), SecurityError> {
        let current_uid = Uid::current();
        
        if current_uid.is_root() {
            // Switch to unprivileged user
            let target_uid = Uid::from_raw(1000); // Configurable
            let target_gid = Gid::from_raw(1000);
            
            nix::unistd::setgid(target_gid)
                .map_err(|e| SecurityError::PrivilegeDropFailed(e.to_string()))?;
            nix::unistd::setuid(target_uid)
                .map_err(|e| SecurityError::PrivilegeDropFailed(e.to_string()))?;
        }
        
        Ok(())
    }
    
    fn apply_resource_limits(&self) -> Result<(), SecurityError> {
        use nix::sys::resource::rlim_t;
        
        // Memory limit
        setrlimit(
            Resource::RLIMIT_AS,
            self.resource_limits.memory_limit_bytes as rlim_t,
            self.resource_limits.memory_limit_bytes as rlim_t,
        ).map_err(|e| SecurityError::ResourceLimitFailed(e.to_string()))?;
        
        // File descriptor limit
        setrlimit(
            Resource::RLIMIT_NOFILE,
            self.resource_limits.max_file_descriptors as rlim_t,
            self.resource_limits.max_file_descriptors as rlim_t,
        ).map_err(|e| SecurityError::ResourceLimitFailed(e.to_string()))?;
        
        // Process limit
        setrlimit(
            Resource::RLIMIT_NPROC,
            self.resource_limits.max_processes as rlim_t,
            self.resource_limits.max_processes as rlim_t,
        ).map_err(|e| SecurityError::ResourceLimitFailed(e.to_string()))?;
        
        Ok(())
    }
    
    fn apply_seccomp_filter(&self) -> Result<(), SecurityError> {
        // Use seccomp to restrict system calls
        // This is a simplified example - real implementation would be more comprehensive
        let filter = seccomp::Filter::new(
            vec![
                // Allow essential system calls
                seccomp::Rule::new(libc::SYS_read),
                seccomp::Rule::new(libc::SYS_write),
                seccomp::Rule::new(libc::SYS_close),
                seccomp::Rule::new(libc::SYS_openat),
                seccomp::Rule::new(libc::SYS_mmap),
                seccomp::Rule::new(libc::SYS_munmap),
                seccomp::Rule::new(libc::SYS_brk),
                seccomp::Rule::new(libc::SYS_rt_sigaction),
                seccomp::Rule::new(libc::SYS_rt_sigprocmask),
                seccomp::Rule::new(libc::SYS_ioctl),
                seccomp::Rule::new(libc::SYS_nanosleep),
                seccomp::Rule::new(libc::SYS_clone),
                seccomp::Rule::new(libc::SYS_execve),
                // Network calls (if allowed)
                seccomp::Rule::new(libc::SYS_socket),
                seccomp::Rule::new(libc::SYS_connect),
                seccomp::Rule::new(libc::SYS_sendto),
                seccomp::Rule::new(libc::SYS_recvfrom),
            ],
            seccomp::Action::Kill, // Kill on any other syscall
        ).map_err(|e| SecurityError::SeccompFilterFailed(e.to_string()))?;
        
        filter.load()
            .map_err(|e| SecurityError::SeccompFilterFailed(e.to_string()))?;
        
        Ok(())
    }
}
```

## Configuration Templates

### Security Configuration

```json
{
  "security": {
    "identity": {
      "provider": "spiffe",
      "spire_agent_socket": "/run/spire/sockets/agent.sock",
      "svid_renewal_interval": "30m",
      "trust_domain": "rust-ss.local"
    },
    "transport": {
      "mtls_required": true,
      "tls_version": "1.3",
      "cipher_suites": [
        "TLS_AES_256_GCM_SHA384",
        "TLS_CHACHA20_POLY1305_SHA256"
      ],
      "certificate_verification": "strict"
    },
    "authorization": {
      "model": "capability_based",
      "default_token_ttl": "1h",
      "max_token_ttl": "24h",
      "delegation_max_depth": 3,
      "audit_all_capability_usage": true
    },
    "encryption": {
      "vault_address": "https://vault.rust-ss.local:8200",
      "key_rotation_interval": "90d",
      "data_classification": {
        "pii": {
          "encryption_required": true,
          "key_type": "aes256-gcm96"
        },
        "sensitive": {
          "encryption_required": true,
          "key_type": "aes256-gcm96"
        },
        "internal": {
          "encryption_required": false
        }
      }
    },
    "validation": {
      "max_request_size_bytes": 10485760,
      "max_json_depth": 10,
      "rate_limits": {
        "validation_per_agent": "1000/minute",
        "authentication_attempts": "10/minute"
      },
      "sql_injection_protection": true,
      "xss_protection": true
    },
    "boundaries": {
      "enable_sandboxing": true,
      "default_memory_limit": "512MB",
      "default_cpu_shares": 1024,
      "network_isolation": false,
      "filesystem_restrictions": {
        "allowed_paths": ["/tmp", "/var/lib/rust-ss"],
        "readonly_paths": ["/etc/rust-ss"],
        "denied_paths": ["/proc", "/sys"]
      }
    },
    "audit": {
      "log_all_authentication": true,
      "log_all_authorization": true,
      "log_failed_validations": true,
      "log_security_events": true,
      "event_retention_days": 90
    }
  }
}
```

## Implementation Guidelines

### For Agents

1. **Identity Bootstrap**: Always initialize `AgentIdentity` at startup:
   ```rust
   let identity = AgentIdentity::bootstrap().await?;
   let secure_transport = SecureTransport::new(Arc::new(identity)).await?;
   ```

2. **mTLS for All Communication**: Never use plain TCP/HTTP:
   ```rust
   let connection = secure_transport
       .connect_to_agent(target_id, address)
       .await?;
   ```

3. **Capability-Based Access**: Request minimal capabilities:
   ```rust
   let capability = capability_manager
       .request_capability(CapabilityRequest {
           resource: ResourceIdentifier {
               resource_type: "storage".to_string(),
               resource_id: "s3://bucket/data.txt".to_string(),
               namespace: None,
           },
           permissions: hashset![Permission::Read],
           expires_at: chrono::Utc::now() + chrono::Duration::minutes(5),
           single_use: true,
           ..Default::default()
       })
       .await?;
   ```

4. **Encrypt Sensitive Data**: Always use KMS for encryption:
   ```rust
   let encrypted = kms
       .encrypt_data(
           sensitive_data.as_bytes(),
           "pii-encryption-key",
           Some(encryption_context),
       )
       .await?;
   ```

5. **Validate All Inputs**: Never trust external data:
   ```rust
   let validated_request: MyRequest = input_validator
       .validate_request(&raw_input, &security_context)
       .await?;
   ```

6. **Enforce Security Boundaries**: Apply sandboxing:
   ```rust
   let boundary = SecurityBoundary::default();
   boundary.enforce().await?;
   ```

### Security Best Practices

1. **Zero Trust**: Authenticate and authorize every interaction
2. **Least Privilege**: Request only necessary permissions
3. **Defense in Depth**: Layer multiple security controls
4. **Fail Secure**: Deny by default on errors
5. **Audit Everything**: Log all security-relevant events
6. **Rotate Credentials**: Use short-lived tokens and certificates
7. **Encrypt at Rest**: Protect sensitive data in storage
8. **Validate Inputs**: Sanitize and validate all external data
9. **Isolate Agents**: Use sandboxing and resource limits
10. **Monitor Anomalies**: Detect and respond to unusual behavior

## References

This document consolidates security patterns from:
- [SPIFFE Specification](https://spiffe.io/docs/latest/)
- [Rust Security Guidelines](https://anssi-fr.github.io/rust-guide/)
- [OWASP Security Practices](https://owasp.org/www-project-secure-coding-practices-quick-reference-guide/)
- [Zero Trust Architecture](https://www.nist.gov/publications/zero-trust-architecture)

For implementation details, refer to:
- `spiffe` crate documentation
- `rustls` for TLS implementation
- `vaultrs` for HashiCorp Vault integration
- `jsonwebtoken` for JWT handling