# MCP Integration Service Configuration

## Core Configuration Structure

```json
{
  "mcp_integration": {
    "enabled": true,
    "server": {
      "host": "0.0.0.0",
      "port": 3100,
      "protocol": "http",
      "base_path": "/mcp"
    },
    "connection_pool": {
      "min_connections": 10,
      "max_connections": 50,
      "idle_timeout_ms": 300000,
      "connection_timeout_ms": 5000
    },
    "mcp_servers": [
      {
        "name": "filesystem-tools",
        "command": "npx",
        "args": ["@modelcontextprotocol/server-filesystem"],
        "env": {
          "MCP_ALLOWED_PATHS": "/workspace"
        },
        "auto_start": true,
        "restart_on_failure": true,
        "max_restarts": 3
      },
      {
        "name": "github-tools",
        "command": "mcp-server-github",
        "args": ["--token", "${GITHUB_TOKEN}"],
        "auto_start": false
      }
    ],
    "tool_discovery": {
      "cache_ttl_seconds": 300,
      "refresh_interval_seconds": 60,
      "max_tools_per_server": 100
    },
    "security": {
      "require_authentication": true,
      "api_key_header": "X-MCP-API-Key",
      "allowed_tool_patterns": ["*"],
      "blocked_tool_patterns": ["system.*", "admin.*"]
    },
    "monitoring": {
      "metrics_enabled": true,
      "health_check_interval_ms": 30000,
      "log_level": "info"
    }
  }
}
```

## Environment-Specific Configurations

### Development Configuration
```json
{
  "mcp_integration": {
    "server": {
      "port": 3100,
      "protocol": "http"
    },
    "connection_pool": {
      "min_connections": 2,
      "max_connections": 10
    },
    "security": {
      "require_authentication": false
    },
    "monitoring": {
      "log_level": "debug"
    }
  }
}
```

### Production Configuration
```json
{
  "mcp_integration": {
    "server": {
      "port": 443,
      "protocol": "https",
      "tls": {
        "cert_file": "/etc/ssl/certs/mcp.crt",
        "key_file": "/etc/ssl/private/mcp.key"
      }
    },
    "connection_pool": {
      "min_connections": 20,
      "max_connections": 100
    },
    "security": {
      "require_authentication": true,
      "rate_limiting": {
        "enabled": true,
        "requests_per_minute": 1000
      }
    },
    "monitoring": {
      "log_level": "warn",
      "metrics_endpoint": "http://metrics-server:9090"
    }
  }
}
```

## Configuration Validation Rules

1. **Required Fields**
   - `server.host` and `server.port` must be specified
   - At least one MCP server must be configured
   - `security.api_key_header` required if authentication enabled

2. **Value Constraints**
   - `connection_pool.max_connections` must be >= `min_connections`
   - `server.port` must be between 1-65535
   - `tool_discovery.cache_ttl_seconds` must be >= 60
   - `monitoring.health_check_interval_ms` must be >= 10000

3. **Security Requirements**
   - In production, `security.require_authentication` must be true
   - TLS configuration required for HTTPS protocol
   - API keys must be at least 32 characters

## Default Values

| Parameter | Default | Description |
|-----------|---------|-------------|
| `enabled` | true | Enable MCP integration |
| `server.protocol` | "http" | Server protocol |
| `connection_pool.idle_timeout_ms` | 300000 | 5 minutes |
| `tool_discovery.max_tools_per_server` | 100 | Max tools per server |
| `security.require_authentication` | true | Require auth |
| `monitoring.metrics_enabled` | true | Enable metrics |