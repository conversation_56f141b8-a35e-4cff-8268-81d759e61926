# API Gateway Service - Usage Patterns and Examples

## Common API Gateway Patterns

### 1. Request Authentication and Authorization

```typescript
// Multi-factor authentication flow
class AuthFlow {
  async authenticateRequest(req: Request): Promise<AuthResult> {
    // Extract token from multiple sources
    const token = this.extractToken(req) || 
                  this.extractAPIKey(req) || 
                  this.extractSessionCookie(req);
    
    if (!token) {
      if (this.isPublicEndpoint(req.path)) {
        return { authenticated: false, allowAccess: true };
      }
      return { authenticated: false, allowAccess: false };
    }
    
    // Validate token and get user context
    const user = await this.validateToken(token);
    if (!user) {
      return { authenticated: false, allowAccess: false };
    }
    
    // Check permissions for the specific endpoint
    const hasPermission = await this.checkPermissions(user, req.method, req.path);
    
    return {
      authenticated: true,
      allowAccess: hasPermission,
      user
    };
  }
}

// Rate limiting per client
class RateLimitingMiddleware {
  async checkRateLimit(req: Request): Promise<RateLimitResult> {
    const clientId = this.getClientId(req);
    const limit = await this.getClientLimit(clientId);
    
    const usage = await this.redis.get(`rate_limit:${clientId}`);
    const currentCount = usage ? parseInt(usage) : 0;
    
    if (currentCount >= limit.maxRequests) {
      const resetTime = await this.redis.ttl(`rate_limit:${clientId}`);
      
      return {
        allowed: false,
        limit: limit.maxRequests,
        remaining: 0,
        resetTime: Date.now() + (resetTime * 1000)
      };
    }
    
    // Increment counter
    await this.redis.incr(`rate_limit:${clientId}`);
    await this.redis.expire(`rate_limit:${clientId}`, limit.windowSeconds);
    
    return {
      allowed: true,
      limit: limit.maxRequests,
      remaining: limit.maxRequests - currentCount - 1,
      resetTime: Date.now() + (limit.windowSeconds * 1000)
    };
  }
}
```

### 2. Load Balancing and Service Discovery

```typescript
// Intelligent service routing
class ServiceRouter {
  async routeToService(serviceName: string, request: APIRequest): Promise<ServiceResponse> {
    const servicePool = this.getServicePool(serviceName);
    const strategy = this.getLoadBalancingStrategy(serviceName);
    
    // Health-aware instance selection
    const healthyInstances = servicePool.instances.filter(instance => 
      instance.status === 'healthy' && instance.responseTime < 1000
    );
    
    if (healthyInstances.length === 0) {
      throw new ServiceUnavailableError(`No healthy instances for ${serviceName}`);
    }
    
    const selectedInstance = await strategy.selectInstance(healthyInstances);
    
    try {
      const response = await this.forwardRequest(selectedInstance, request);
      
      // Update instance metrics
      this.updateInstanceHealth(selectedInstance, true, response.time);
      
      return response;
    } catch (error) {
      this.updateInstanceHealth(selectedInstance, false, 0);
      
      // Implement retry with different instance
      if (healthyInstances.length > 1) {
        return this.retryWithDifferentInstance(serviceName, request, selectedInstance);
      }
      
      throw error;
    }
  }
  
  // Circuit breaker pattern
  async executeWithCircuitBreaker(serviceName: string, operation: () => Promise<any>): Promise<any> {
    const circuitBreaker = this.getCircuitBreaker(serviceName);
    
    if (circuitBreaker.isOpen()) {
      // Use fallback or cached response
      return this.getFallbackResponse(serviceName);
    }
    
    try {
      const result = await operation();
      circuitBreaker.recordSuccess();
      return result;
    } catch (error) {
      circuitBreaker.recordFailure();
      
      if (circuitBreaker.shouldTrip()) {
        circuitBreaker.open();
        this.notifyCircuitBreakerTripped(serviceName);
      }
      
      throw error;
    }
  }
}
```

### 3. Request/Response Transformation

```typescript
// API versioning and backwards compatibility
class APIVersionManager {
  async transformRequest(req: Request): Promise<TransformedRequest> {
    const version = this.extractVersion(req);
    const latestVersion = this.getLatestVersion();
    
    if (version === latestVersion) {
      return { originalRequest: req, transformed: req };
    }
    
    // Apply version-specific transformations
    const transformer = this.getRequestTransformer(version, latestVersion);
    const transformedReq = await transformer.transform(req);
    
    return {
      originalRequest: req,
      transformed: transformedReq,
      version,
      transformations: transformer.getAppliedTransformations()
    };
  }
  
  async transformResponse(response: any, originalVersion: string): Promise<any> {
    const latestVersion = this.getLatestVersion();
    
    if (originalVersion === latestVersion) {
      return response;
    }
    
    // Apply backwards-compatible transformations
    const transformer = this.getResponseTransformer(latestVersion, originalVersion);
    return transformer.transform(response);
  }
}

// Request aggregation for efficient client operations
class RequestAggregator {
  async aggregateRequests(requests: BatchRequest[]): Promise<BatchResponse> {
    const groupedRequests = this.groupRequestsByService(requests);
    const results = new Map<string, any>();
    
    // Execute requests in parallel per service
    const servicePromises = Array.from(groupedRequests.entries()).map(
      async ([serviceName, serviceRequests]) => {
        const serviceResults = await this.executeBatchForService(serviceName, serviceRequests);
        serviceResults.forEach((result, requestId) => {
          results.set(requestId, result);
        });
      }
    );
    
    await Promise.all(servicePromises);
    
    // Return results in original request order
    return {
      responses: requests.map(req => ({
        id: req.id,
        status: results.get(req.id)?.status || 'error',
        data: results.get(req.id)?.data,
        error: results.get(req.id)?.error
      }))
    };
  }
}
```

### 4. WebSocket Real-time Communication

```typescript
// Real-time event streaming
class WebSocketEventStreamer {
  async handleWebSocketConnection(ws: WebSocket, req: IncomingMessage): Promise<void> {
    const connection = this.createConnection(ws, req);
    
    // Authenticate WebSocket connection
    const authResult = await this.authenticateWebSocket(connection);
    if (!authResult.success) {
      ws.close(1008, 'Authentication failed');
      return;
    }
    
    connection.user = authResult.user;
    this.connections.set(connection.id, connection);
    
    // Set up event subscriptions
    ws.on('message', async (data: Buffer) => {
      try {
        const message = JSON.parse(data.toString()) as WSMessage;
        await this.handleWebSocketMessage(connection, message);
      } catch (error) {
        this.sendError(connection, 'Invalid message format');
      }
    });
    
    // Send initial state
    await this.sendInitialState(connection);
  }
  
  async handleSubscription(connection: WSConnection, subscription: Subscription): Promise<void> {
    // Validate subscription permissions
    if (!this.canSubscribeToTopic(connection.user, subscription.topic)) {
      this.sendError(connection, 'Permission denied for topic');
      return;
    }
    
    connection.subscriptions.add(subscription.topic);
    
    // Subscribe to internal event stream
    await this.communicationHub.subscribe(subscription.topic, (event) => {
      this.sendEventToConnection(connection, event);
    });
    
    this.sendAck(connection, subscription.id);
  }
  
  async broadcastToSubscribers(topic: string, event: any): Promise<void> {
    const message = JSON.stringify({
      type: 'event',
      topic,
      data: event,
      timestamp: Date.now()
    });
    
    for (const [id, connection] of this.connections) {
      if (connection.subscriptions.has(topic) && 
          connection.ws.readyState === WebSocket.OPEN) {
        connection.ws.send(message);
      }
    }
  }
}
```

### 5. Caching and Performance Optimization

```typescript
// Intelligent response caching
class ResponseCacheManager {
  async getCachedResponse(req: Request): Promise<CachedResponse | null> {
    if (!this.isCacheable(req)) {
      return null;
    }
    
    const cacheKey = this.generateCacheKey(req);
    const cached = await this.cache.get(cacheKey);
    
    if (!cached) {
      return null;
    }
    
    // Check ETag for conditional requests
    const ifNoneMatch = req.headers['if-none-match'];
    if (ifNoneMatch && ifNoneMatch === cached.etag) {
      return {
        status: 304,
        headers: { 'etag': cached.etag },
        body: null
      };
    }
    
    return {
      status: 200,
      headers: {
        'content-type': cached.contentType,
        'etag': cached.etag,
        'x-cache': 'HIT'
      },
      body: cached.data
    };
  }
  
  async cacheResponse(req: Request, response: any): Promise<void> {
    if (!this.shouldCache(req, response)) {
      return;
    }
    
    const cacheKey = this.generateCacheKey(req);
    const etag = this.generateETag(response);
    const ttl = this.calculateTTL(req, response);
    
    await this.cache.set(cacheKey, {
      data: response,
      etag,
      contentType: response.headers?.['content-type'],
      cachedAt: Date.now()
    }, ttl);
  }
}

// Request compression and optimization
class RequestOptimizer {
  async optimizeResponse(response: any, req: Request): Promise<OptimizedResponse> {
    let optimizedResponse = response;
    
    // Apply compression if supported
    const acceptEncoding = req.headers['accept-encoding'];
    if (acceptEncoding?.includes('gzip') && this.shouldCompress(response)) {
      optimizedResponse = await this.compressResponse(response, 'gzip');
    }
    
    // Minimize response payload
    if (req.query.fields) {
      optimizedResponse = this.filterFields(optimizedResponse, req.query.fields);
    }
    
    // Set performance headers
    const headers = {
      'x-response-time': `${Date.now() - req.startTime}ms`,
      'x-cache-status': response.fromCache ? 'HIT' : 'MISS'
    };
    
    return {
      body: optimizedResponse,
      headers
    };
  }
}
```

### 6. Error Handling and Monitoring

```typescript
// Comprehensive error handling
class ErrorHandler {
  handleError(error: Error, req: Request, res: Response): void {
    const errorId = this.generateErrorId();
    
    // Log error with context
    this.logger.error('API Gateway Error', {
      errorId,
      error: error.message,
      stack: error.stack,
      request: {
        method: req.method,
        url: req.url,
        headers: req.headers,
        user: req.user?.id
      }
    });
    
    // Determine error type and response
    const errorResponse = this.formatErrorResponse(error, errorId);
    
    // Set appropriate status code
    const statusCode = this.getStatusCode(error);
    
    // Send error response
    res.status(statusCode).json(errorResponse);
    
    // Update error metrics
    this.metrics.incrementErrorCount(statusCode, req.route?.path);
  }
  
  private formatErrorResponse(error: Error, errorId: string): ErrorResponse {
    if (error instanceof ValidationError) {
      return {
        error: 'Validation Failed',
        details: error.details,
        errorId
      };
    }
    
    if (error instanceof AuthenticationError) {
      return {
        error: 'Authentication Required',
        errorId
      };
    }
    
    if (error instanceof ServiceUnavailableError) {
      return {
        error: 'Service Temporarily Unavailable',
        retryAfter: error.retryAfter,
        errorId
      };
    }
    
    // Generic error response (hide internal details)
    return {
      error: 'Internal Server Error',
      errorId
    };
  }
}
```

These patterns demonstrate comprehensive API Gateway functionality including authentication, load balancing, caching, real-time communication, and error handling for the RUST-SS system.