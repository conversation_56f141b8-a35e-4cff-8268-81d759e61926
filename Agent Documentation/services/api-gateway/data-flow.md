# API Gateway Service - Data Flow and Structures

## Data Flow Architecture

### Request Processing Pipeline
```mermaid
graph TB
    A[Incoming Request] --> B[TLS Termination]
    B --> C[Rate Limiting]
    C --> D[Authentication]
    D --> E[Authorization]
    E --> F[Request Validation]
    F --> G[Cache Check]
    G --> H[Load Balancer]
    H --> I[Service Routing]
    I --> J[Response Transform]
    J --> K[Cache Store]
    K --> L[Client Response]
```

### WebSocket Connection Flow
```mermaid
graph LR
    A[WebSocket Handshake] --> B[Authentication]
    B --> C[Connection Pool]
    C --> D[Event Subscription]
    D --> E[Real-time Events]
    E --> F[Client Broadcast]
```

## Core Data Structures

### Request/Response Models
```typescript
interface APIRequest {
  id: string;
  method: string;
  path: string;
  headers: Record<string, string>;
  body?: any;
  query: Record<string, string>;
  user?: UserContext;
  timestamp: Date;
  correlationId: string;
}

interface APIResponse {
  status: number;
  headers: Record<string, string>;
  body: any;
  cached: boolean;
  processingTime: number;
  serviceName: string;
}

interface UserContext {
  id: string;
  email: string;
  roles: string[];
  permissions: string[];
  sessionId?: string;
}
```

### Load Balancing Data
```typescript
interface ServicePool {
  name: string;
  instances: ServiceInstance[];
  strategy: LoadBalancingStrategy;
  healthCheck: HealthCheckConfig;
}

interface ServiceInstance {
  id: string;
  url: string;
  status: 'healthy' | 'unhealthy' | 'draining';
  weight: number;
  connections: number;
  responseTime: number;
  lastHealthCheck: Date;
}
```

### Cache Entry Model
```typescript
interface CacheEntry {
  key: string;
  data: any;
  headers: Record<string, string>;
  etag: string;
  createdAt: Date;
  expiresAt: Date;
  accessCount: number;
  lastAccessed: Date;
}
```

### WebSocket Connection Data
```typescript
interface WebSocketConnection {
  id: string;
  user: UserContext;
  subscriptions: Set<string>;
  lastActivity: Date;
  connectionTime: Date;
  messageCount: number;
}

interface WebSocketMessage {
  type: 'subscribe' | 'unsubscribe' | 'request' | 'event';
  id?: string;
  topic?: string;
  data: any;
  timestamp: Date;
}
```

## Data Flow Patterns

### Authentication Flow
```typescript
interface AuthenticationFlow {
  tokenExtraction: {
    source: 'header' | 'query' | 'cookie';
    value: string;
    type: 'jwt' | 'api-key' | 'oauth';
  };
  
  validation: {
    valid: boolean;
    user?: UserContext;
    error?: string;
    expiresAt?: Date;
  };
  
  authorization: {
    endpoint: string;
    requiredPermissions: string[];
    hasAccess: boolean;
    reason?: string;
  };
}
```

### Load Balancing Flow
```typescript
interface LoadBalancingFlow {
  serviceSelection: {
    serviceName: string;
    availableInstances: ServiceInstance[];
    selectedInstance: ServiceInstance;
    selectionReason: string;
  };
  
  requestForwarding: {
    targetUrl: string;
    timeout: number;
    retryCount: number;
    circuitBreakerState: 'closed' | 'open' | 'half-open';
  };
  
  responseHandling: {
    success: boolean;
    responseTime: number;
    statusCode: number;
    error?: string;
  };
}
```

This data flow documentation provides the essential structures for API gateway operations, request routing, and client communication in the RUST-SS system.