# State Management Service - Data Flow and Structures

## Data Flow Architecture

### State Lifecycle Flow

```mermaid
graph TB
    A[State Creation] --> B[Validation]
    B --> C[L1 Cache]
    C --> D[L2 Cache]
    D --> E[L3 Storage]
    E --> F[Replication]
    F --> G[Archive]
    
    H[State Read] --> I{Cache Hit?}
    I -->|Yes| J[Return from Cache]
    I -->|No| K[Load from Storage]
    K --> L[Update Cache]
    L --> J
    
    M[State Update] --> N[Transaction Begin]
    N --> O[Apply Changes]
    O --> P[Validation]
    P --> Q[Commit/Rollback]
    Q --> R[Event Emission]
    R --> S[Cache Invalidation]
```

### Multi-Tier Storage Flow

```mermaid
graph LR
    subgraph "Application Layer"
        A[Agent Request]
        B[Session Manager]
        C[State Manager]
    end
    
    subgraph "Cache Layer"
        D[L1 Memory Cache]
        E[L2 Redis Cache]
    end
    
    subgraph "Storage Layer"
        F[L3 PostgreSQL]
        G[Archive S3]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    
    C -.->|Read Miss| E
    C -.->|Read Miss| F
```

## Core Data Structures

### Session State Structure

```typescript
interface SessionData {
  // Identity and metadata
  id: string;
  userId: string;
  name: string;
  createdAt: Date;
  lastActivity: Date;
  
  // Execution context
  workingDirectory: string;
  environment: Record<string, string>;
  
  // Agent management
  agents: AgentReference[];
  activeAgents: number;
  maxAgents: number;
  
  // Memory management
  memoryNamespace: string;
  memoryUsage: number;
  memoryLimit: number;
  
  // Session state
  status: SessionStatus;
  checkpoints: CheckpointReference[];
  
  // Associated resources
  attachments: AttachedFile[];
  webClients: WebClientInfo[];
  cliProcess?: ProcessInfo;
}

type SessionStatus = 'initializing' | 'active' | 'paused' | 'completed' | 'failed' | 'archived';

interface AgentReference {
  id: string;
  name: string;
  type: string;
  status: AgentStatus;
  assignedTasks: string[];
  memoryAllocation: number;
  lastActivity: Date;
}

interface CheckpointReference {
  id: string;
  timestamp: Date;
  type: 'manual' | 'automatic' | 'failure';
  size: number;
  location: string;
  metadata: Record<string, any>;
}
```

### CLI State Synchronization

```typescript
interface CLIStateData {
  // Process information
  processId: string;
  sessionId: string;
  startTime: Date;
  lastUpdate: Date;
  status: ProcessStatus;
  
  // Working context
  workingDirectory: string;
  environment: Record<string, string>;
  
  // Command history
  commandHistory: CommandEntry[];
  currentCommand?: string;
  
  // Agent states
  agents: CLIAgentState[];
  
  // Task queue and execution
  taskQueue: TaskEntry[];
  activeTasks: TaskEntry[];
  completedTasks: TaskResult[];
  
  // Memory and resources
  memoryBank: MemoryBankSnapshot;
  resourceUsage: ResourceMetrics;
  
  // Configuration
  config: CLIConfiguration;
}

interface CLIAgentState {
  id: string;
  name: string;
  type: string;
  status: 'idle' | 'busy' | 'completed' | 'failed' | 'paused';
  currentTask?: string;
  progress?: number;
  lastActivity: Date;
  outputBuffer: string[];
  errorLog: string[];
}

interface CommandEntry {
  command: string;
  timestamp: Date;
  duration: number;
  exitCode: number;
  output?: string;
  error?: string;
}
```

### Event Store Data Model

```typescript
interface EventRecord {
  // Event identity
  id: string;
  streamId: string;
  eventNumber: number;
  
  // Event metadata
  eventType: string;
  timestamp: number;
  correlationId?: string;
  causationId?: string;
  
  // Event data
  data: any;
  metadata: EventMetadata;
  
  // Storage metadata
  created: Date;
  version: number;
}

interface EventMetadata {
  source: string;
  contentType: string;
  dataSchema: string;
  userId?: string;
  sessionId?: string;
  agentId?: string;
  causedBy?: string;
}

interface StreamInfo {
  streamId: string;
  version: number;
  created: Date;
  lastModified: Date;
  eventCount: number;
  truncatedBefore?: number;
}
```

### Workflow State Model

```typescript
interface WorkflowStateData {
  // Workflow identity
  id: string;
  name: string;
  version: string;
  
  // Execution state
  status: WorkflowStatus;
  currentPhase: string;
  progress: number;
  
  // Phase tracking
  phases: Record<string, PhaseState>;
  phaseHistory: PhaseTransition[];
  
  // Task management
  tasks: TaskState[];
  dependencies: DependencyGraph;
  
  // Variables and context
  variables: Record<string, any>;
  context: WorkflowContext;
  
  // Timing information
  startedAt: Date;
  estimatedCompletion?: Date;
  completedAt?: Date;
  
  // Error handling
  errors: WorkflowError[];
  retryCount: number;
  lastRetry?: Date;
}

interface PhaseState {
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  progress: number;
  startedAt?: Date;
  completedAt?: Date;
  assignedAgents: string[];
  results: PhaseResult[];
  errors: string[];
}

interface TaskState {
  id: string;
  type: string;
  status: TaskStatus;
  assignedTo?: string;
  dependencies: string[];
  progress: number;
  result?: TaskResult;
  error?: string;
  retryCount: number;
}
```

## Data Flow Patterns

### State Update Flow

```typescript
// State update with transaction support
interface StateUpdateFlow {
  // 1. Initiate update
  request: StateUpdateRequest;
  
  // 2. Validation phase
  validation: {
    schemaValidation: boolean;
    permissionCheck: boolean;
    constraintValidation: boolean;
    conflictDetection: boolean;
  };
  
  // 3. Transaction management
  transaction: {
    id: string;
    participants: string[];
    coordinatorId: string;
    state: 'preparing' | 'prepared' | 'committed' | 'aborted';
  };
  
  // 4. Storage updates
  storageUpdates: {
    l1Cache: CacheOperation[];
    l2Cache: CacheOperation[];
    persistentStorage: StorageOperation[];
  };
  
  // 5. Event emission
  events: StateChangeEvent[];
  
  // 6. Notification distribution
  notifications: NotificationTarget[];
}

interface StateUpdateRequest {
  key: string;
  operation: 'create' | 'update' | 'delete' | 'merge';
  value: any;
  conditions?: UpdateCondition[];
  metadata?: UpdateMetadata;
}

interface UpdateCondition {
  type: 'version' | 'timestamp' | 'value' | 'exists';
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'in' | 'exists';
  value: any;
}
```

### Session Synchronization Flow

```typescript
// Session state synchronization between CLI and web clients
interface SessionSyncFlow {
  // 1. Change detection
  changeDetection: {
    source: 'cli' | 'web' | 'agent' | 'system';
    changes: StateChange[];
    timestamp: Date;
  };
  
  // 2. State merging
  stateMerging: {
    strategy: 'last-writer-wins' | 'merge-recursive' | 'conflict-resolution';
    conflicts: StateConflict[];
    resolution: ConflictResolution[];
  };
  
  // 3. Propagation
  propagation: {
    targets: SyncTarget[];
    priority: 'immediate' | 'batch' | 'background';
    reliability: 'best-effort' | 'guaranteed';
  };
  
  // 4. Consistency verification
  verification: {
    checksums: Record<string, string>;
    verification: boolean;
    inconsistencies: string[];
  };
}

interface StateChange {
  path: string;
  operation: 'set' | 'delete' | 'append' | 'merge';
  oldValue?: any;
  newValue?: any;
  timestamp: Date;
  source: string;
}

interface SyncTarget {
  type: 'cli' | 'web-client' | 'agent' | 'storage';
  id: string;
  priority: number;
  reliability: 'best-effort' | 'guaranteed';
}
```

### Event Sourcing Flow

```typescript
// Event sourcing pattern for state reconstruction
interface EventSourcingFlow {
  // 1. Event appending
  eventAppend: {
    event: EventRecord;
    streamId: string;
    expectedVersion: number;
    timestamp: Date;
  };
  
  // 2. Projection updates
  projectionUpdates: {
    projections: string[];
    updateType: 'sync' | 'async';
    priority: number;
  };
  
  // 3. Snapshot management
  snapshotManagement: {
    shouldSnapshot: boolean;
    snapshotData?: any;
    retentionPolicy: SnapshotRetention;
  };
  
  // 4. State reconstruction
  stateReconstruction: {
    fromSnapshot?: string;
    fromEvent?: string;
    toEvent?: string;
    events: EventRecord[];
    finalState: any;
  };
}

interface SnapshotRetention {
  maxSnapshots: number;
  retentionDays: number;
  compressionEnabled: boolean;
  archiveAfterDays: number;
}
```

## Performance Optimization Data Flows

### Cache Warming Flow

```typescript
interface CacheWarmingFlow {
  // 1. Warming triggers
  triggers: {
    startup: boolean;
    scheduled: string; // cron expression
    onDemand: boolean;
    predictive: boolean;
  };
  
  // 2. Key identification
  keyIdentification: {
    patterns: string[];
    priorities: KeyPriority[];
    exclusions: string[];
  };
  
  // 3. Loading strategy
  loadingStrategy: {
    concurrency: number;
    batchSize: number;
    rateLimiting: RateLimit;
    fallbackBehavior: 'skip' | 'retry' | 'degrade';
  };
  
  // 4. Verification
  verification: {
    successRate: number;
    loadTime: number;
    cacheHitImprovement: number;
  };
}

interface KeyPriority {
  pattern: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  warningThreshold: number;
}
```

### Replication Data Flow

```typescript
interface ReplicationFlow {
  // 1. Change capture
  changeCapture: {
    source: string;
    changes: ReplicationChange[];
    sequenceNumber: number;
    timestamp: Date;
  };
  
  // 2. Conflict detection
  conflictDetection: {
    conflicts: ReplicationConflict[];
    resolutionStrategy: ConflictStrategy;
    manualResolutionRequired: boolean;
  };
  
  // 3. Propagation
  propagation: {
    targets: ReplicationTarget[];
    propagationMode: 'sync' | 'async' | 'hybrid';
    acknowledgments: Acknowledgment[];
  };
  
  // 4. Consistency verification
  consistencyVerification: {
    checksums: NodeChecksum[];
    inconsistencies: InconsistencyReport[];
    reconciliationRequired: boolean;
  };
}

interface ReplicationChange {
  key: string;
  operation: 'insert' | 'update' | 'delete';
  data: any;
  version: number;
  timestamp: Date;
  nodeId: string;
}

interface ReplicationTarget {
  nodeId: string;
  region: string;
  priority: number;
  lagTolerance: number;
  healthStatus: 'healthy' | 'degraded' | 'failed';
}
```

This comprehensive data flow documentation provides the foundation for understanding how state moves through the RUST-SS state management system, enabling efficient implementation and debugging.