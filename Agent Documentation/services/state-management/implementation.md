# State Management Service - Implementation Documentation

## Core Traits and Structs

### StateManager Trait

The `StateManager` trait defines the core interface for state management operations in Rust. It provides async methods for state persistence and retrieval with strong type safety.

```rust
// Example: Core StateManager trait definition
use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

#[async_trait]
pub trait StateManager: Send + Sync {
    type State: serde::Serialize + serde::de::DeserializeOwned + Send + Sync;
    type Error: std::error::Error + Send + Sync + 'static;
    
    /// Save state with versioning support
    async fn save_state(
        &self,
        key: &str,
        value: Self::State,
        metadata: Option<StateMetadata>
    ) -> Result<StateVersion, Self::Error>;
    
    /// Get state by key and optional version
    async fn get_state(
        &self,
        key: &str,
        version: Option<u64>
    ) -> Result<Option<Self::State>, Self::Error>;
    
    /// Atomically update state using a delta function
    async fn update_state<F>(
        &self,
        key: &str,
        updater: F
    ) -> Result<StateVersion, Self::Error>
    where
        F: FnOnce(&mut Self::State) -> Result<(), Self::Error> + Send;
    
    /// Delete state with audit trail
    async fn delete_state(&self, key: &str) -> Result<(), Self::Error>;
    
    /// Query states matching a filter
    async fn query_state(
        &self,
        filter: StateFilter
    ) -> Result<Vec<(String, Self::State)>, Self::Error>;
}
```

### SessionManager Implementation

The `SessionManager` provides session lifecycle management with async Rust patterns:

```rust
// Example: SessionManager struct with async session handling
pub struct SessionManager {
    sessions: Arc<RwLock<HashMap<String, Session>>>,
    persistence: Arc<dyn SessionPersistence>,
    recovery: Arc<dyn SessionRecovery>,
    event_bus: Arc<dyn EventBus>,
}

impl SessionManager {
    pub fn new(
        event_bus: Arc<dyn EventBus>,
        config: SessionConfig
    ) -> Self {
        Self {
            sessions: Arc::new(RwLock::new(HashMap::new())),
            persistence: Arc::new(SessionPersistenceImpl::new(config.persistence)),
            recovery: Arc::new(SessionRecoveryImpl::new(config.recovery)),
            event_bus,
        }
    }
    
    /// Create a new session with initial agents
    pub async fn create_session(
        &self,
        config: SessionConfig
    ) -> Result<Session, SessionError> {
        let session = Session {
            id: generate_id(),
            name: config.name.clone(),
            config: config.clone(),
            agents: Vec::new(),
            memory: MemoryNamespace::new(&config.name),
            start_time: chrono::Utc::now(),
            end_time: None,
            status: SessionStatus::Active,
        };
        
        // Initialize agents concurrently
        let mut agent_futures = Vec::new();
        for _ in 0..config.initial_agents {
            let agent_future = self.spawn_agent(&session, &config.agent_profile);
            agent_futures.push(agent_future);
        }
        
        let agents = futures::future::try_join_all(agent_futures).await?;
        let mut session = session;
        session.agents = agents;
        
        // Store in memory and persist
        self.sessions.write().await.insert(session.id.clone(), session.clone());
        self.persistence.save(&session).await?;
        
        // Emit session created event
        self.event_bus.publish(Event::SessionCreated {
            session_id: session.id.clone(),
            timestamp: chrono::Utc::now(),
        }).await?;
        
        Ok(session)
    }
    
    /// Restore a session from persistent storage
    pub async fn restore_session(
        &self,
        session_id: &str
    ) -> Result<Session, SessionError> {
        let data = self.persistence.load(session_id).await?
            .ok_or_else(|| SessionError::NotFound(session_id.to_string()))?;
        
        let session = self.recovery.restore(data).await?;
        self.sessions.write().await.insert(session.id.clone(), session.clone());
        
        Ok(session)
    }
}
```

### Session Struct

Represents the state and lifecycle of an individual session, including agents, memory, and checkpoint capabilities:

```rust
// Example: Session struct with checkpoint capabilities
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Session {
    pub id: String,
    pub name: String,
    pub config: SessionConfig,
    pub agents: Vec<Agent>,
    pub memory: MemoryNamespace,
    pub start_time: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
    pub status: SessionStatus,
}

impl Session {
    /// Create a checkpoint of the current session state
    pub async fn checkpoint(&self, persistence: &dyn SessionPersistence) -> Result<(), SessionError> {
        let snapshot = SessionSnapshot {
            id: self.id.clone(),
            name: self.name.clone(),
            config: self.config.clone(),
            agents: self.serialize_agents().await?,
            memory: self.memory.export().await?,
            start_time: self.start_time,
            status: self.status.clone(),
            checkpoint_time: Utc::now(),
        };
        
        persistence.save_checkpoint(&snapshot).await?;
        Ok(())
    }
    
    /// Serialize agents for checkpointing
    async fn serialize_agents(&self) -> Result<Vec<SerializedAgent>, SessionError> {
        let mut serialized = Vec::new();
        for agent in &self.agents {
            serialized.push(agent.serialize().await?);
        }
        Ok(serialized)
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum SessionStatus {
    Active,
    Completed,
    Failed(String),
    Stopped,
}
```

### StateSync Trait

Provides CLI state synchronization capabilities between the CLI and web clients:

```rust
// Example: State synchronization trait for CLI/Web coordination
#[async_trait]
pub trait StateSync: Send + Sync {
    /// Sync CLI state with web clients
    async fn sync_state(&self, process_id: &str) -> Result<CLIState, SyncError>;
    
    /// Update state from CLI
    async fn update_from_cli(
        &self,
        process_id: &str,
        state: CLIStateUpdate
    ) -> Result<(), SyncError>;
    
    /// Get current state
    async fn get_current_state(&self, process_id: &str) -> Result<CLIState, SyncError>;
    
    /// State persistence operations
    async fn persist_state(&self, process_id: &str) -> Result<(), SyncError>;
    async fn restore_state(&self, process_id: &str) -> Result<CLIState, SyncError>;
}
```

### StateSyncManager Implementation

```rust
// Example: Concurrent state management with change notification
pub struct StateSyncManager {
    states: Arc<RwLock<HashMap<String, CLIState>>>,
    persistence: Arc<dyn PersistenceManager>,
    listeners: Arc<RwLock<HashMap<String, Vec<Arc<dyn StateChangeListener>>>>>,
    config: StateConfig,
}

impl StateSyncManager {
    pub fn new(config: StateConfig) -> Self {
        Self {
            states: Arc::new(RwLock::new(HashMap::new())),
            persistence: Arc::new(PersistenceManagerImpl::new(config.persistence_options.clone())),
            listeners: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }
}

#[async_trait]
impl StateSync for StateSyncManager {
    async fn sync_state(&self, process_id: &str) -> Result<CLIState, SyncError> {
        let states = self.states.read().await;
        let state = states.get(process_id)
            .ok_or_else(|| SyncError::NotFound(process_id.to_string()))?;
        
        // Fetch latest state from CLI process
        let updated = self.fetch_state_from_cli(process_id).await?;
        
        // Drop read lock before acquiring write lock
        drop(states);
        
        // Merge states with write lock
        let mut states = self.states.write().await;
        let merged = self.merge_states(state.clone(), updated);
        states.insert(process_id.to_string(), merged.clone());
        
        // Notify listeners asynchronously
        let process_id = process_id.to_string();
        let merged_clone = merged.clone();
        let listeners = self.listeners.clone();
        
        tokio::spawn(async move {
            if let Ok(listeners) = listeners.read().await.get(&process_id) {
                for listener in listeners {
                    let _ = listener.on_state_change(&process_id, &merged_clone).await;
                }
            }
        });
        
        Ok(merged)
    }
    
    async fn persist_state(&self, process_id: &str) -> Result<(), SyncError> {
        let states = self.states.read().await;
        if let Some(state) = states.get(process_id) {
            self.persistence.save(process_id, state).await?;
        }
        Ok(())
    }
}

impl StateSyncManager {
    /// Merge two states with conflict resolution
    fn merge_states(&self, current: CLIState, updated: CLIState) -> CLIState {
        CLIState {
            process_id: updated.process_id,
            session_id: updated.session_id,
            working_directory: updated.working_directory,
            environment: updated.environment,
            agents: self.merge_agents(current.agents, updated.agents),
            tasks: self.merge_tasks(current.tasks, updated.tasks),
            memory_bank: updated.memory_bank,
            config: updated.config,
            start_time: current.start_time, // Keep original
            last_update: chrono::Utc::now(),
            status: updated.status,
        }
    }
}
```

### EventStore Implementation

Implements event sourcing patterns for state management with append-only event logging:

```rust
// Example: Event sourcing with snapshots for state reconstruction
use std::collections::BTreeMap;
use uuid::Uuid;

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Event {
    pub id: Uuid,
    pub event_type: String,
    pub timestamp: i64,
    pub payload: serde_json::Value,
    pub aggregate_id: String,
    pub sequence: u64,
}

pub struct EventStore<S: SystemState> {
    events: Arc<RwLock<Vec<Event>>>,
    snapshots: Arc<RwLock<BTreeMap<i64, S>>>,
    projections: Arc<RwLock<HashMap<String, Box<dyn Any + Send + Sync>>>>,
    snapshot_frequency: usize,
}

impl<S: SystemState + Clone + Send + Sync + 'static> EventStore<S> {
    pub fn new(snapshot_frequency: usize) -> Self {
        Self {
            events: Arc::new(RwLock::new(Vec::new())),
            snapshots: Arc::new(RwLock::new(BTreeMap::new())),
            projections: Arc::new(RwLock::new(HashMap::new())),
            snapshot_frequency,
        }
    }
    
    /// Append an event to the store
    pub async fn append(&self, event: Event) -> Result<(), EventStoreError> {
        let mut events = self.events.write().await;
        events.push(event.clone());
        let event_count = events.len();
        drop(events); // Release lock early
        
        // Update projections asynchronously
        self.update_projections(event).await?;
        
        // Create snapshot periodically
        if event_count % self.snapshot_frequency == 0 {
            self.create_snapshot().await?;
        }
        
        Ok(())
    }
    
    /// Replay events from a given timestamp
    pub async fn replay(&self, from_timestamp: Option<i64>) -> Result<S, EventStoreError> {
        let from = from_timestamp.unwrap_or(0);
        
        // Find nearest snapshot
        let snapshots = self.snapshots.read().await;
        let (snapshot_time, mut state) = snapshots
            .range(..=from)
            .last()
            .map(|(&time, state)| (time, state.clone()))
            .unwrap_or_else(|| (0, S::initial_state()));
        drop(snapshots);
        
        // Replay events from snapshot
        let events = self.events.read().await;
        let events_to_replay: Vec<_> = events
            .iter()
            .filter(|e| e.timestamp > snapshot_time)
            .cloned()
            .collect();
        drop(events);
        
        // Apply events sequentially
        for event in events_to_replay {
            state = self.apply_event(state, &event).await?;
        }
        
        Ok(state)
    }
    
    /// Apply a single event to the state
    async fn apply_event(&self, mut state: S, event: &Event) -> Result<S, EventStoreError> {
        state.apply_event(event)?;
        Ok(state)
    }
    
    /// Create a snapshot of the current state
    async fn create_snapshot(&self) -> Result<(), EventStoreError> {
        let current_state = self.replay(None).await?;
        let timestamp = chrono::Utc::now().timestamp();
        
        let mut snapshots = self.snapshots.write().await;
        snapshots.insert(timestamp, current_state);
        
        // Keep only recent snapshots
        let cutoff = timestamp - (86400 * 7); // 7 days
        snapshots.retain(|&time, _| time > cutoff);
        
        Ok(())
    }
}

/// Trait for system state that can be reconstructed from events
pub trait SystemState: Sized {
    fn initial_state() -> Self;
    fn apply_event(&mut self, event: &Event) -> Result<(), EventStoreError>;
}
```

## Data Structures

### CLIState Struct

Represents the synchronized state between CLI and web clients:

```rust
// Example: CLI state representation with rich typing
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct CLIState {
    pub process_id: String,
    pub session_id: String,
    pub working_directory: PathBuf,
    pub environment: HashMap<String, String>,
    
    // Agent states
    pub agents: Vec<AgentState>,
    
    // Task states  
    pub tasks: Vec<TaskState>,
    
    // Memory bank state
    pub memory_bank: MemoryBankState,
    
    // Configuration
    pub config: ConfigState,
    
    // Runtime info
    pub start_time: DateTime<Utc>,
    pub last_update: DateTime<Utc>,
    pub status: ProcessStatus,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct AgentState {
    pub id: Uuid,
    pub name: String,
    pub agent_type: AgentType,
    pub status: AgentStatus,
    pub current_task: Option<String>,
    pub progress: Option<f32>, // 0.0 to 1.0
    pub last_activity: DateTime<Utc>,
    pub capabilities: Vec<String>,
    pub metrics: AgentMetrics,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum AgentStatus {
    Idle,
    Busy { task_id: String },
    Completed { results: serde_json::Value },
    Failed { error: String },
    Suspended,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum ProcessStatus {
    Running,
    Completed { exit_code: i32 },
    Failed { error: String },
    Stopped,
}

/// Builder pattern for creating CLIState
impl CLIState {
    pub fn builder() -> CLIStateBuilder {
        CLIStateBuilder::default()
    }
}

#[derive(Default)]
pub struct CLIStateBuilder {
    process_id: Option<String>,
    session_id: Option<String>,
    working_directory: Option<PathBuf>,
    environment: HashMap<String, String>,
}

impl CLIStateBuilder {
    pub fn process_id(mut self, id: String) -> Self {
        self.process_id = Some(id);
        self
    }
    
    pub fn session_id(mut self, id: String) -> Self {
        self.session_id = Some(id);
        self
    }
    
    pub fn build(self) -> Result<CLIState, BuilderError> {
        Ok(CLIState {
            process_id: self.process_id.ok_or(BuilderError::MissingField("process_id"))?,
            session_id: self.session_id.ok_or(BuilderError::MissingField("session_id"))?,
            working_directory: self.working_directory.unwrap_or_else(|| PathBuf::from(".")),
            environment: self.environment,
            agents: Vec::new(),
            tasks: Vec::new(),
            memory_bank: MemoryBankState::default(),
            config: ConfigState::default(),
            start_time: Utc::now(),
            last_update: Utc::now(),
            status: ProcessStatus::Running,
        })
    }
}
```

### Session Data Structures

```rust
// Example: Rich session data structures with type safety
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct SessionData {
    pub id: Uuid,
    pub user_id: String,
    pub created_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    
    // Working context
    pub working_directory: PathBuf,
    pub environment: HashMap<String, String>,
    
    // Command history
    pub history: Vec<CommandHistoryEntry>,
    
    // File attachments
    pub attachments: Vec<AttachedFile>,
    
    // CLI process info
    pub cli_process: Option<CLIProcessInfo>,
    
    // Web client info
    pub web_clients: Vec<WebClient>,
    
    // Session state
    pub state: SessionState,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct CommandHistoryEntry {
    pub timestamp: DateTime<Utc>,
    pub command: String,
    pub args: Vec<String>,
    pub working_dir: PathBuf,
    pub exit_code: Option<i32>,
    pub duration_ms: Option<u64>,
    pub output_summary: Option<String>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct AttachedFile {
    pub id: Uuid,
    pub filename: String,
    pub path: PathBuf,
    pub mime_type: String,
    pub size_bytes: u64,
    pub hash: String, // SHA256
    pub uploaded_at: DateTime<Utc>,
    pub metadata: HashMap<String, String>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct CLIProcessInfo {
    pub pid: u32,
    pub status: ProcessStatus,
    pub start_time: DateTime<Utc>,
    pub resource_usage: ResourceUsage,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ResourceUsage {
    pub cpu_percent: f32,
    pub memory_mb: u64,
    pub threads: u32,
    pub open_files: u32,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct WebClient {
    pub id: String,
    pub ip_address: IpAddr,
    pub user_agent: String,
    pub connected_at: DateTime<Utc>,
    pub last_ping: DateTime<Utc>,
    pub permissions: Vec<Permission>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct SessionState {
    pub agents: Vec<AgentInfo>,
    pub tasks: Vec<TaskInfo>,
    pub memory: MemoryInfo,
    pub config: ConfigInfo,
}

/// Session state with validation
impl SessionData {
    pub fn validate(&self) -> Result<(), ValidationError> {
        if self.user_id.is_empty() {
            return Err(ValidationError::EmptyField("user_id"));
        }
        
        if self.created_at > self.last_activity {
            return Err(ValidationError::InvalidTimestamp(
                "created_at cannot be after last_activity"
            ));
        }
        
        // Validate working directory exists
        if !self.working_directory.exists() {
            return Err(ValidationError::InvalidPath(
                self.working_directory.clone()
            ));
        }
        
        Ok(())
    }
}
```

## Storage Implementations

### Persistence Layer

The state management service uses a multi-tier storage architecture:

```rust
// Example: Multi-tier storage abstraction
pub struct StorageLayer {
    l1_cache: Arc<dyn L1Cache>,    // In-memory for hot paths
    l2_cache: Arc<dyn L2Cache>,    // Redis for warm data
    l3_storage: Arc<dyn L3Storage>, // PostgreSQL for persistence
    archive: Arc<dyn Archive>,      // S3 for historical data
}

/// L1 Cache trait for in-memory storage
#[async_trait]
pub trait L1Cache: Send + Sync {
    async fn get<T: DeserializeOwned>(&self, key: &str) -> Option<T>;
    async fn set<T: Serialize>(&self, key: &str, value: &T, ttl: Duration);
    async fn invalidate(&self, key: &str);
}

/// L2 Cache trait for Redis
#[async_trait]
pub trait L2Cache: Send + Sync {
    async fn get<T: DeserializeOwned>(&self, key: &str) -> Result<Option<T>, CacheError>;
    async fn set<T: Serialize>(&self, key: &str, value: &T, ttl: Duration) -> Result<(), CacheError>;
    async fn delete(&self, key: &str) -> Result<(), CacheError>;
    async fn exists(&self, key: &str) -> Result<bool, CacheError>;
}

/// L3 Storage trait for PostgreSQL
#[async_trait]
pub trait L3Storage: Send + Sync {
    async fn persist<T: Serialize>(&self, key: &str, value: &T) -> Result<(), StorageError>;
    async fn load<T: DeserializeOwned>(&self, key: &str) -> Result<Option<T>, StorageError>;
    async fn query<T: DeserializeOwned>(&self, query: Query) -> Result<Vec<T>, StorageError>;
    async fn delete(&self, key: &str) -> Result<(), StorageError>;
}

/// Archive trait for S3
#[async_trait]
pub trait Archive: Send + Sync {
    async fn archive<T: Serialize>(&self, key: &str, value: &T) -> Result<String, ArchiveError>;
    async fn retrieve<T: DeserializeOwned>(&self, archive_id: &str) -> Result<T, ArchiveError>;
    async fn list_archives(&self, prefix: &str) -> Result<Vec<ArchiveMetadata>, ArchiveError>;
}

impl StorageLayer {
    /// Get with cache hierarchy
    pub async fn get<T: DeserializeOwned + Serialize + Clone>(&self, key: &str) -> Result<Option<T>, StorageError> {
        // Check L1 cache first
        if let Some(value) = self.l1_cache.get::<T>(key).await {
            return Ok(Some(value));
        }
        
        // Check L2 cache
        if let Ok(Some(value)) = self.l2_cache.get::<T>(key).await {
            // Populate L1
            self.l1_cache.set(key, &value, Duration::from_secs(300)).await;
            return Ok(Some(value));
        }
        
        // Check L3 storage
        if let Some(value) = self.l3_storage.load::<T>(key).await? {
            // Populate L1 and L2
            self.l2_cache.set(key, &value, Duration::from_secs(3600)).await?;
            self.l1_cache.set(key, &value, Duration::from_secs(300)).await;
            return Ok(Some(value));
        }
        
        Ok(None)
    }
}
```

### Transaction Support

```rust
// Example: Transaction API with distributed transaction support
#[async_trait]
pub trait TransactionManager: Send + Sync {
    async fn begin_transaction(&self) -> Result<TransactionId, TransactionError>;
    async fn commit_transaction(&self, tx_id: &TransactionId) -> Result<(), TransactionError>;
    async fn rollback_transaction(&self, tx_id: &TransactionId) -> Result<(), TransactionError>;
    async fn get_transaction_status(&self, tx_id: &TransactionId) -> Result<TransactionStatus, TransactionError>;
}

#[derive(Clone, Debug)]
pub struct TransactionId(String);

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum TransactionStatus {
    Active { started_at: DateTime<Utc> },
    Committed { committed_at: DateTime<Utc> },
    RolledBack { rolled_back_at: DateTime<Utc> },
    Failed { error: String, failed_at: DateTime<Utc> },
}

/// Distributed transaction implementation
pub struct DistributedTransactionManager {
    coordinator: Arc<dyn TransactionCoordinator>,
    participants: Vec<Arc<dyn TransactionParticipant>>,
}

#[async_trait]
impl TransactionManager for DistributedTransactionManager {
    async fn begin_transaction(&self) -> Result<TransactionId, TransactionError> {
        let tx_id = TransactionId(Uuid::new_v4().to_string());
        
        // Two-phase commit: prepare phase
        for participant in &self.participants {
            participant.prepare(&tx_id).await?;
        }
        
        Ok(tx_id)
    }
    
    async fn commit_transaction(&self, tx_id: &TransactionId) -> Result<(), TransactionError> {
        // Two-phase commit: commit phase
        let futures: Vec<_> = self.participants
            .iter()
            .map(|p| p.commit(tx_id))
            .collect();
        
        futures::future::try_join_all(futures).await?;
        Ok(())
    }
}
```

### Snapshot Management

```rust
// Example: Snapshot API for point-in-time state recovery
#[async_trait]
pub trait SnapshotManager: Send + Sync {
    async fn create_snapshot(&self, namespace: &str) -> Result<SnapshotId, SnapshotError>;
    async fn restore_snapshot(&self, snapshot_id: &SnapshotId) -> Result<(), SnapshotError>;
    async fn list_snapshots(&self, filter: Option<SnapshotFilter>) -> Result<Vec<SnapshotInfo>, SnapshotError>;
    async fn compare_snapshots(&self, id1: &SnapshotId, id2: &SnapshotId) -> Result<SnapshotDiff, SnapshotError>;
}

#[derive(Clone, Debug)]
pub struct SnapshotId(String);

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct SnapshotInfo {
    pub id: SnapshotId,
    pub namespace: String,
    pub created_at: DateTime<Utc>,
    pub size_bytes: u64,
    pub metadata: HashMap<String, String>,
    pub state_version: u64,
}

#[derive(Clone, Debug)]
pub struct SnapshotDiff {
    pub added_keys: Vec<String>,
    pub removed_keys: Vec<String>,
    pub modified_keys: Vec<String>,
    pub metadata_changes: HashMap<String, (String, String)>,
}

/// Copy-on-write snapshot implementation
pub struct CowSnapshotManager {
    storage: Arc<dyn L3Storage>,
    archive: Arc<dyn Archive>,
}

impl CowSnapshotManager {
    /// Create snapshot using copy-on-write semantics
    pub async fn create_cow_snapshot(&self, namespace: &str) -> Result<SnapshotId, SnapshotError> {
        let snapshot_id = SnapshotId(format!("snap_{}", Uuid::new_v4()));
        
        // Mark current state as snapshot base
        let metadata = SnapshotMetadata {
            namespace: namespace.to_string(),
            created_at: Utc::now(),
            parent_snapshot: None,
        };
        
        self.storage.persist(&snapshot_id.0, &metadata).await?;
        Ok(snapshot_id)
    }
}
```

## Error Handling

### Recovery Mechanisms

The service implements comprehensive error handling:

```rust
// Example: Resilient error handling with retry and circuit breaker
use backoff::{ExponentialBackoff, retry};

pub struct ResilientStateManager<S: StateManager> {
    inner: S,
    circuit_breaker: Arc<CircuitBreaker>,
    retry_policy: ExponentialBackoff,
}

impl<S: StateManager> ResilientStateManager<S> {
    /// Save state with automatic retry and circuit breaker
    pub async fn save_state_resilient(
        &self,
        key: &str,
        value: S::State,
    ) -> Result<StateVersion, ResilienceError> {
        // Check circuit breaker
        if !self.circuit_breaker.is_closed() {
            return Err(ResilienceError::CircuitOpen);
        }
        
        // Retry with exponential backoff
        let operation = || async {
            match self.inner.save_state(key, value.clone(), None).await {
                Ok(version) => Ok(version),
                Err(e) => {
                    self.circuit_breaker.record_failure();
                    Err(backoff::Error::transient(e))
                }
            }
        };
        
        match retry(self.retry_policy.clone(), operation).await {
            Ok(version) => {
                self.circuit_breaker.record_success();
                Ok(version)
            }
            Err(_) => {
                self.circuit_breaker.trip();
                Err(ResilienceError::MaxRetriesExceeded)
            }
        }
    }
}

/// Circuit breaker implementation
pub struct CircuitBreaker {
    state: Arc<RwLock<CircuitState>>,
    failure_threshold: u32,
    success_threshold: u32,
    timeout: Duration,
}

#[derive(Clone, Debug)]
enum CircuitState {
    Closed { failure_count: u32 },
    Open { opened_at: Instant },
    HalfOpen { success_count: u32 },
}

impl CircuitBreaker {
    pub async fn is_closed(&self) -> bool {
        let state = self.state.read().await;
        matches!(*state, CircuitState::Closed { .. })
    }
    
    pub async fn record_failure(&self) {
        let mut state = self.state.write().await;
        match &mut *state {
            CircuitState::Closed { failure_count } => {
                *failure_count += 1;
                if *failure_count >= self.failure_threshold {
                    *state = CircuitState::Open { 
                        opened_at: Instant::now() 
                    };
                }
            }
            CircuitState::HalfOpen { .. } => {
                *state = CircuitState::Open { 
                    opened_at: Instant::now() 
                };
            }
            _ => {}
        }
    }
}
```

### Consistency Guarantees

Different consistency models for different use cases:

```rust
// Example: Consistency models implementation
pub enum ConsistencyLevel {
    /// Strong consistency - linearizable operations
    Strong,
    /// Eventual consistency - for analytics and reporting
    Eventual { max_lag: Duration },
    /// Read-your-write consistency
    ReadYourWrite { session_id: String },
    /// Causal consistency for workflows
    Causal { vector_clock: VectorClock },
}

pub struct ConsistentStateManager {
    storage: Arc<dyn StorageLayer>,
    consistency_manager: Arc<ConsistencyManager>,
}

impl ConsistentStateManager {
    pub async fn read_with_consistency<T>(
        &self,
        key: &str,
        consistency: ConsistencyLevel,
    ) -> Result<Option<T>, ConsistencyError> 
    where
        T: DeserializeOwned + Clone,
    {
        match consistency {
            ConsistencyLevel::Strong => {
                // Read from primary with quorum
                self.consistency_manager.quorum_read(key).await
            }
            ConsistencyLevel::Eventual { max_lag } => {
                // Read from any replica within lag threshold
                self.consistency_manager.eventual_read(key, max_lag).await
            }
            ConsistencyLevel::ReadYourWrite { session_id } => {
                // Ensure we read our own writes
                self.consistency_manager.session_read(key, &session_id).await
            }
            ConsistencyLevel::Causal { vector_clock } => {
                // Ensure causal ordering
                self.consistency_manager.causal_read(key, &vector_clock).await
            }
        }
    }
}

/// Vector clock for causal consistency
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct VectorClock {
    clocks: HashMap<String, u64>,
}

impl VectorClock {
    pub fn new() -> Self {
        Self {
            clocks: HashMap::new(),
        }
    }
    
    pub fn increment(&mut self, node_id: &str) {
        *self.clocks.entry(node_id.to_string()).or_insert(0) += 1;
    }
    
    pub fn merge(&mut self, other: &VectorClock) {
        for (node, &clock) in &other.clocks {
            let entry = self.clocks.entry(node.clone()).or_insert(0);
            *entry = (*entry).max(clock);
        }
    }
    
    pub fn happens_before(&self, other: &VectorClock) -> bool {
        self.clocks.iter().all(|(node, &clock)| {
            other.clocks.get(node).map_or(false, |&other_clock| clock <= other_clock)
        })
    }
}
```

This implementation provides the foundation for reliable, scalable state management across all RUST-SS components with production-ready error handling and consistency guarantees.