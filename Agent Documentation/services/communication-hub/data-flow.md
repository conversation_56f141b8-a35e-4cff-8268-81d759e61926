# Communication Hub Service - Data Flow and Structures

## Data Flow Architecture

### Message Flow Overview

```mermaid
graph TB
    A[Publisher] --> B[Protocol Bridge]
    B --> C[Message Router]
    C --> D[QoS Manager]
    D --> E[Priority Queue]
    E --> F[Load Balancer]
    F --> G[Circuit Breaker]
    G --> H[Subscriber]
    
    I[Rate Limiter] --> C
    J[Dead Letter Queue] --> <PERSON>[Error Handler]
    G --> J
    
    L[Metrics Collector] --> M[Monitoring]
    C --> L
    D --> L
    F --> L
```

### Protocol Translation Flow

```mermaid
graph LR
    subgraph "Inbound"
        A[WebSocket] --> D[Translation Layer]
        B[gRPC] --> D
        C[HTTP] --> D
    end
    
    subgraph "Core"
        D --> E[Message Router]
        E --> F[NATS Core]
    end
    
    subgraph "Outbound"
        F --> G[Translation Layer]
        G --> H[WebSocket]
        G --> I[gRPC]
        G --> J[HTTP]
    end
```

## Core Data Structures

### Message Data Model

```typescript
interface InternalMessage {
  // Message identity
  id: string;
  correlationId?: string;
  causationId?: string;
  
  // Routing information
  topic: string;
  replyTo?: string;
  
  // Content
  payload: any;
  headers: Record<string, string>;
  
  // Timing
  timestamp: number;
  expiration?: number;
  
  // Quality of service
  priority: MessagePriority;
  qos: QoSLevel;
  deliveryAttempt: number;
  maxRetries: number;
  
  // Source information
  source: string;
  sourceProtocol: string;
  clientId?: string;
  sessionId?: string;
  
  // Metadata
  metadata: MessageMetadata;
  
  // Processing state
  processingState: ProcessingState;
  routingHistory: RoutingStep[];
}

interface MessageMetadata {
  contentType: string;
  encoding: string;
  compressed: boolean;
  encrypted: boolean;
  size: number;
  schema?: string;
  tags: string[];
  traceId?: string;
  spanId?: string;
}

interface ProcessingState {
  status: 'queued' | 'routing' | 'delivering' | 'delivered' | 'failed';
  startedAt: number;
  completedAt?: number;
  errorCount: number;
  lastError?: ProcessingError;
  retryScheduledAt?: number;
}

interface RoutingStep {
  timestamp: number;
  component: string;
  action: string;
  duration: number;
  result: 'success' | 'failure' | 'retry';
  details?: Record<string, any>;
}

enum MessagePriority {
  CRITICAL = 0,
  HIGH = 1,
  NORMAL = 2,
  LOW = 3
}

enum QoSLevel {
  AT_MOST_ONCE = 0,
  AT_LEAST_ONCE = 1,
  EXACTLY_ONCE = 2
}
```

### Subscription Data Model

```typescript
interface Subscription {
  // Identity
  id: string;
  clientId: string;
  sessionId?: string;
  
  // Subscription details
  topic: string;
  pattern: string;
  queueGroup?: string;
  
  // Options
  options: SubscriptionOptions;
  
  // State
  status: SubscriptionStatus;
  createdAt: Date;
  lastMessage: Date;
  
  // Statistics
  messageCount: number;
  bytesReceived: number;
  errorCount: number;
  
  // Handler information
  handler: MessageHandler;
  handlerType: 'sync' | 'async' | 'stream';
  
  // Flow control
  maxPending: number;
  currentPending: number;
  backpressureLevel: number;
}

interface SubscriptionOptions {
  qos: QoSLevel;
  durable: boolean;
  autoAck: boolean;
  ackWait: number;
  maxDeliver: number;
  filterSubject?: string;
  deliverPolicy: 'all' | 'last' | 'new' | 'byStartSequence' | 'byStartTime';
  replayPolicy: 'instant' | 'original';
  flowControl: boolean;
  heartbeat: number;
}

enum SubscriptionStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  DRAINING = 'draining',
  CLOSED = 'closed',
  ERROR = 'error'
}
```

### Routing Table Data Model

```typescript
interface Route {
  // Route identity
  id: string;
  name: string;
  description?: string;
  
  // Matching criteria
  pattern: string;
  patternType: 'exact' | 'wildcard' | 'regex';
  conditions: RouteCondition[];
  
  // Target configuration
  targets: RouteTarget[];
  loadBalancingStrategy: LoadBalancingStrategy;
  
  // Quality settings
  priority: number;
  weight: number;
  timeout: number;
  retryPolicy: RetryPolicy;
  
  // State and metrics
  status: RouteStatus;
  healthScore: number;
  lastUsed: Date;
  
  // Statistics
  messageCount: number;
  successCount: number;
  failureCount: number;
  averageLatency: number;
  
  // Configuration
  createdAt: Date;
  updatedAt: Date;
  version: number;
  tags: string[];
}

interface RouteTarget {
  id: string;
  type: 'service' | 'queue' | 'topic' | 'function';
  endpoint: string;
  weight: number;
  maxConcurrency: number;
  
  // Health monitoring
  healthCheck: HealthCheckConfig;
  status: TargetStatus;
  lastHealthCheck: Date;
  
  // Circuit breaker
  circuitBreaker: CircuitBreakerState;
  
  // Statistics
  activeConnections: number;
  requestCount: number;
  errorRate: number;
  responseTime: number;
}

interface RouteCondition {
  type: 'header' | 'payload' | 'metadata' | 'source';
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'in' | 'contains' | 'regex';
  value: any;
  caseSensitive: boolean;
}

enum LoadBalancingStrategy {
  ROUND_ROBIN = 'round-robin',
  WEIGHTED_ROUND_ROBIN = 'weighted-round-robin',
  LEAST_CONNECTIONS = 'least-connections',
  HASH = 'hash',
  RANDOM = 'random',
  STICKY_SESSION = 'sticky-session',
  BROADCAST = 'broadcast'
}
```

## Data Flow Patterns

### Message Publishing Flow

```typescript
interface PublishFlow {
  // 1. Message ingestion
  ingestion: {
    messageId: string;
    protocol: string;
    sourceClient: string;
    receivedAt: number;
    rawSize: number;
  };
  
  // 2. Protocol translation
  translation: {
    fromProtocol: string;
    toProtocol: string;
    transformations: Transformation[];
    validationResult: ValidationResult;
    translatedAt: number;
  };
  
  // 3. Routing decision
  routing: {
    matchedRoutes: RouteMatch[];
    selectedRoute: string;
    routingStrategy: string;
    routingTime: number;
  };
  
  // 4. QoS processing
  qosProcessing: {
    priorityAssigned: MessagePriority;
    qosLevel: QoSLevel;
    rateLimitCheck: RateLimitResult;
    queuePosition: number;
  };
  
  // 5. Delivery execution
  delivery: {
    targetEndpoints: string[];
    deliveryStrategy: DeliveryStrategy;
    attempts: DeliveryAttempt[];
    finalStatus: DeliveryStatus;
  };
}

interface Transformation {
  type: 'format' | 'compress' | 'encrypt' | 'enrich' | 'filter';
  input: any;
  output: any;
  duration: number;
  success: boolean;
  error?: string;
}

interface ValidationResult {
  valid: boolean;
  schemaValidation: boolean;
  sizeValidation: boolean;
  securityValidation: boolean;
  errors: ValidationError[];
}

interface RouteMatch {
  routeId: string;
  matchType: 'pattern' | 'condition';
  matchScore: number;
  conditions: ConditionResult[];
}

interface DeliveryAttempt {
  attempt: number;
  targetId: string;
  startedAt: number;
  completedAt: number;
  status: 'success' | 'failure' | 'timeout';
  latency: number;
  error?: DeliveryError;
}
```

### Subscription Management Flow

```typescript
interface SubscriptionFlow {
  // 1. Subscription request
  request: {
    clientId: string;
    topic: string;
    options: SubscriptionOptions;
    authentication: AuthenticationResult;
    authorization: AuthorizationResult;
  };
  
  // 2. Topic resolution
  topicResolution: {
    originalTopic: string;
    resolvedPattern: string;
    wildcardExpansion: string[];
    filterConditions: FilterCondition[];
  };
  
  // 3. Queue group management
  queueGroupManagement: {
    queueGroup?: string;
    memberCount: number;
    loadBalancing: QueueGroupBalancing;
    memberPosition: number;
  };
  
  // 4. Message delivery
  messageDelivery: {
    deliveryMode: 'push' | 'pull' | 'stream';
    batchSize: number;
    flowControlEnabled: boolean;
    backpressureThreshold: number;
    currentBacklog: number;
  };
  
  // 5. Acknowledgment handling
  acknowledgment: {
    ackMode: 'auto' | 'manual' | 'none';
    ackWaitTime: number;
    maxRedelivery: number;
    redeliveryAttempts: number;
  };
}

interface FilterCondition {
  field: string;
  operator: string;
  value: any;
  compiled: boolean;
  performance: FilterPerformance;
}

interface QueueGroupBalancing {
  strategy: 'round-robin' | 'random' | 'weighted';
  weights: Record<string, number>;
  affinityRules: AffinityRule[];
}

interface FilterPerformance {
  evaluationTime: number;
  cacheHitRate: number;
  optimizationLevel: string;
}
```

### Circuit Breaker Flow

```typescript
interface CircuitBreakerFlow {
  // 1. Request interception
  interception: {
    targetService: string;
    circuitState: CircuitState;
    requestId: string;
    interceptedAt: number;
  };
  
  // 2. State evaluation
  stateEvaluation: {
    currentFailures: number;
    failureThreshold: number;
    timeSinceLastFailure: number;
    timeoutDuration: number;
    shouldExecute: boolean;
    reason: string;
  };
  
  // 3. Execution decision
  executionDecision: {
    decision: 'execute' | 'reject' | 'fallback';
    executionPath: string;
    fallbackStrategy?: FallbackStrategy;
    estimatedLatency: number;
  };
  
  // 4. Result processing
  resultProcessing: {
    success: boolean;
    latency: number;
    error?: Error;
    newCircuitState: CircuitState;
    stateChangeReason?: string;
  };
  
  // 5. Metrics update
  metricsUpdate: {
    successCount: number;
    failureCount: number;
    totalRequests: number;
    averageLatency: number;
    circuitStateChanges: StateChange[];
  };
}

interface FallbackStrategy {
  type: 'cache' | 'default-value' | 'alternative-service' | 'queue';
  configuration: Record<string, any>;
  timeout: number;
  priority: number;
}

interface StateChange {
  fromState: CircuitState;
  toState: CircuitState;
  timestamp: number;
  trigger: string;
  metadata: Record<string, any>;
}

enum CircuitState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half-open'
}
```

### Rate Limiting Flow

```typescript
interface RateLimitingFlow {
  // 1. Request identification
  identification: {
    clientId: string;
    clientType: string;
    sourceIP: string;
    topic: string;
    requestSize: number;
  };
  
  // 2. Limit lookup
  limitLookup: {
    globalLimits: RateLimit;
    clientLimits: RateLimit;
    topicLimits: RateLimit;
    effectiveLimits: RateLimit;
  };
  
  // 3. Token bucket evaluation
  tokenBucketEvaluation: {
    bucketsChecked: BucketStatus[];
    tokensRequired: number;
    tokensAvailable: number;
    refillRate: number;
    bucketCapacity: number;
  };
  
  // 4. Decision and action
  decisionAndAction: {
    allowed: boolean;
    tokensConsumed: number;
    remainingTokens: number;
    retryAfter?: number;
    throttleAction?: ThrottleAction;
  };
  
  // 5. Metrics and alerting
  metricsAndAlerting: {
    requestCount: number;
    allowedCount: number;
    throttledCount: number;
    bucketUtilization: number;
    alertTriggered: boolean;
  };
}

interface BucketStatus {
  bucketId: string;
  type: 'global' | 'client' | 'topic';
  tokens: number;
  capacity: number;
  lastRefill: number;
  refillRate: number;
}

interface ThrottleAction {
  action: 'drop' | 'delay' | 'queue' | 'degrade';
  parameters: Record<string, any>;
  duration?: number;
  priority?: number;
}

interface RateLimit {
  requestsPerSecond: number;
  burstSize: number;
  windowSize: number;
  algorithm: 'token-bucket' | 'sliding-window' | 'fixed-window';
}
```

## Performance Optimization Data Flows

### Message Batching Flow

```typescript
interface MessageBatchingFlow {
  // 1. Batch accumulation
  accumulation: {
    batchId: string;
    batchSize: number;
    maxBatchSize: number;
    timeWindow: number;
    messages: string[];
    totalSize: number;
  };
  
  // 2. Batch optimization
  optimization: {
    compressionRatio: number;
    serializationMethod: string;
    batchingEfficiency: number;
    resourceSavings: ResourceSavings;
  };
  
  // 3. Batch delivery
  delivery: {
    deliveryMethod: 'parallel' | 'sequential' | 'pipelined';
    concurrency: number;
    deliveryLatency: number;
    successRate: number;
  };
  
  // 4. Result aggregation
  resultAggregation: {
    successfulMessages: number;
    failedMessages: number;
    partialFailures: PartialFailure[];
    retryRequired: string[];
  };
}

interface ResourceSavings {
  networkRoundTrips: number;
  cpuCycles: number;
  memoryUsage: number;
  bandwidthUtilization: number;
}

interface PartialFailure {
  messageId: string;
  error: string;
  retryable: boolean;
  failureTime: number;
}
```

### Connection Pooling Flow

```typescript
interface ConnectionPoolingFlow {
  // 1. Connection request
  connectionRequest: {
    requestId: string;
    clientType: string;
    targetService: string;
    priority: ConnectionPriority;
    timeout: number;
  };
  
  // 2. Pool evaluation
  poolEvaluation: {
    poolId: string;
    poolSize: number;
    activeConnections: number;
    idleConnections: number;
    poolUtilization: number;
  };
  
  // 3. Connection allocation
  connectionAllocation: {
    allocated: boolean;
    connectionId?: string;
    connectionAge: number;
    reuseCount: number;
    allocationTime: number;
  };
  
  // 4. Connection lifecycle
  connectionLifecycle: {
    state: ConnectionState;
    lastActivity: number;
    healthStatus: HealthStatus;
    keepaliveInterval: number;
  };
  
  // 5. Pool optimization
  poolOptimization: {
    scalingDecision: 'scale-up' | 'scale-down' | 'maintain';
    targetPoolSize: number;
    performanceMetrics: PoolMetrics;
  };
}

interface PoolMetrics {
  averageWaitTime: number;
  connectionUtilization: number;
  failureRate: number;
  throughput: number;
  latency: LatencyMetrics;
}

interface LatencyMetrics {
  p50: number;
  p95: number;
  p99: number;
  max: number;
}

enum ConnectionPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

enum ConnectionState {
  IDLE = 'idle',
  ACTIVE = 'active',
  DRAINING = 'draining',
  CLOSED = 'closed'
}

enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy'
}
```

This comprehensive data flow documentation provides the foundation for understanding how messages, connections, and resources flow through the communication hub, enabling efficient and reliable inter-service communication in the RUST-SS system.