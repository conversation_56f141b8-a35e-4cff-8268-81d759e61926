# Communication Hub Service - Usage Patterns and Examples

## Common Communication Patterns

### 1. Event-Driven Architecture Pattern

#### System Event Broadcasting

```typescript
// Publishing system events
const eventPublisher = new SystemEventPublisher(communicationHub);

// Agent lifecycle events
await eventPublisher.publish('agent.spawned', {
  agent: {
    id: 'claude-researcher-01',
    type: 'researcher',
    capabilities: ['market-analysis', 'competitive-research'],
    createdAt: new Date()
  }
});

await eventPublisher.publish('agent.terminated', {
  agentId: 'claude-researcher-01',
  reason: 'task-completed'
});

// Task lifecycle events
await eventPublisher.publish('task.created', {
  task: {
    id: 'market-research-task-001',
    type: 'research',
    description: 'Analyze competitive landscape',
    assignedTo: 'claude-researcher-01'
  }
});

// Workflow progress events
await eventPublisher.publish('workflow.phase.completed', {
  workflowId: 'enterprise-development-001',
  phase: 'requirements-gathering',
  results: ['stakeholder-map.md', 'requirements.md'],
  nextPhase: 'architecture-design'
});
```

#### Event Subscription and Handling

```typescript
// Service subscribing to relevant events
export class AgentCoordinator {
  constructor(private communicationHub: CommunicationHub) {
    this.setupEventSubscriptions();
  }
  
  private setupEventSubscriptions(): void {
    // Monitor agent lifecycle
    this.communicationHub.subscribe('agent.*', this.handleAgentEvent.bind(this));
    
    // Track task completion for coordination
    this.communicationHub.subscribe('task.completed', this.handleTaskCompletion.bind(this));
    
    // React to system errors
    this.communicationHub.subscribe('system.error', this.handleSystemError.bind(this));
  }
  
  private async handleAgentEvent(event: SystemEvent): Promise<void> {
    switch (event.type) {
      case 'agent.spawned':
        await this.registerAgent(event.data.agent);
        break;
      case 'agent.terminated':
        await this.cleanupAgent(event.data.agentId);
        break;
      case 'agent.error':
        await this.handleAgentError(event.data.agentId, event.data.error);
        break;
    }
  }
  
  private async handleTaskCompletion(event: SystemEvent): Promise<void> {
    const { taskId, result } = event.data;
    
    // Update coordination state
    await this.updateTaskStatus(taskId, 'completed', result);
    
    // Check for dependent tasks
    const dependentTasks = await this.findDependentTasks(taskId);
    for (const dependentTask of dependentTasks) {
      await this.evaluateTaskReadiness(dependentTask.id);
    }
    
    // Notify workflow engine
    await this.communicationHub.publish('coordination.task.ready', {
      tasks: dependentTasks.filter(task => task.ready).map(task => task.id)
    });
  }
}
```

### 2. Request-Reply Pattern

#### Synchronous Agent Communication

```typescript
// Agent requesting information from another agent
export class ResearchAgent {
  constructor(private communicationHub: CommunicationHub) {}
  
  async requestMarketData(query: string): Promise<MarketData> {
    try {
      const response = await this.communicationHub.request(
        'agent.data-analyst.market-data',
        {
          query,
          requestId: generateRequestId(),
          requesterAgent: 'claude-researcher-01',
          priority: 'high'
        },
        30000 // 30 second timeout
      );
      
      return response.data;
    } catch (error) {
      if (error instanceof TimeoutError) {
        // Fallback to external data source
        return await this.getMarketDataFromExternalAPI(query);
      }
      throw error;
    }
  }
  
  async requestCompetitorAnalysis(companies: string[]): Promise<CompetitorAnalysis> {
    const analysisRequests = companies.map(company => 
      this.communicationHub.request(
        'agent.analyst.competitor-analysis',
        {
          company,
          requestId: generateRequestId(),
          depth: 'comprehensive'
        },
        60000 // 1 minute timeout per company
      )
    );
    
    const results = await Promise.allSettled(analysisRequests);
    
    return {
      companies: results
        .filter(result => result.status === 'fulfilled')
        .map(result => result.value),
      errors: results
        .filter(result => result.status === 'rejected')
        .map(result => result.reason)
    };
  }
}

// Data analyst agent responding to requests
export class DataAnalystAgent {
  constructor(private communicationHub: CommunicationHub) {
    this.setupRequestHandlers();
  }
  
  private setupRequestHandlers(): void {
    this.communicationHub.subscribe(
      'agent.data-analyst.market-data',
      this.handleMarketDataRequest.bind(this)
    );
    
    this.communicationHub.subscribe(
      'agent.analyst.competitor-analysis',
      this.handleCompetitorAnalysisRequest.bind(this)
    );
  }
  
  private async handleMarketDataRequest(request: MessageEvent): Promise<void> {
    const { query, requestId, replyTo } = request.payload;
    
    try {
      const marketData = await this.analyzeMarketData(query);
      
      await this.communicationHub.publish(replyTo, {
        success: true,
        data: marketData,
        requestId,
        timestamp: new Date()
      });
    } catch (error) {
      await this.communicationHub.publish(replyTo, {
        success: false,
        error: error.message,
        requestId,
        timestamp: new Date()
      });
    }
  }
}
```

### 3. Pub/Sub Pattern for Real-Time Updates

#### Progress Tracking and Status Updates

```typescript
// Workflow engine publishing progress updates
export class WorkflowEngine {
  constructor(private communicationHub: CommunicationHub) {}
  
  async executeWorkflow(workflowId: string): Promise<void> {
    // Publish workflow started
    await this.communicationHub.publish('workflow.started', {
      workflowId,
      startedAt: new Date(),
      estimatedDuration: 3600000 // 1 hour
    });
    
    const tasks = await this.getWorkflowTasks(workflowId);
    
    for (const task of tasks) {
      // Publish task started
      await this.communicationHub.publish('workflow.task.started', {
        workflowId,
        taskId: task.id,
        taskName: task.name,
        startedAt: new Date()
      });
      
      try {
        const result = await this.executeTask(task);
        
        // Publish task completed
        await this.communicationHub.publish('workflow.task.completed', {
          workflowId,
          taskId: task.id,
          result,
          completedAt: new Date(),
          duration: result.executionTime
        });
        
        // Publish progress update
        const progress = await this.calculateProgress(workflowId);
        await this.communicationHub.publish('workflow.progress', {
          workflowId,
          progress: progress.percentage,
          completedTasks: progress.completed,
          totalTasks: progress.total,
          estimatedTimeRemaining: progress.timeRemaining
        });
        
      } catch (error) {
        await this.communicationHub.publish('workflow.task.failed', {
          workflowId,
          taskId: task.id,
          error: error.message,
          failedAt: new Date()
        });
        
        // Handle error recovery
        await this.handleTaskFailure(workflowId, task, error);
      }
    }
    
    // Publish workflow completed
    await this.communicationHub.publish('workflow.completed', {
      workflowId,
      completedAt: new Date(),
      finalResults: await this.getWorkflowResults(workflowId)
    });
  }
}

// Web dashboard subscribing to real-time updates
export class DashboardService {
  private connectedClients: Map<string, WebSocketConnection>;
  
  constructor(private communicationHub: CommunicationHub) {
    this.connectedClients = new Map();
    this.setupSubscriptions();
  }
  
  private setupSubscriptions(): void {
    // Subscribe to all workflow events
    this.communicationHub.subscribe('workflow.*', this.handleWorkflowEvent.bind(this));
    
    // Subscribe to agent status updates
    this.communicationHub.subscribe('agent.status.*', this.handleAgentStatusUpdate.bind(this));
    
    // Subscribe to system health events
    this.communicationHub.subscribe('system.health.*', this.handleSystemHealthUpdate.bind(this));
  }
  
  private async handleWorkflowEvent(event: MessageEvent): Promise<void> {
    const dashboardUpdate = {
      type: 'workflow_update',
      event: event.topic.split('.').pop(),
      data: event.payload,
      timestamp: new Date()
    };
    
    // Broadcast to all connected dashboard clients
    for (const [clientId, connection] of this.connectedClients) {
      try {
        await connection.send(JSON.stringify(dashboardUpdate));
      } catch (error) {
        console.error(`Failed to send update to client ${clientId}:`, error);
        this.connectedClients.delete(clientId);
      }
    }
  }
}
```

### 4. Streaming Pattern for High-Volume Data

#### Log Streaming and Aggregation

```typescript
// Agent streaming logs and metrics
export class AgentLogger {
  constructor(private communicationHub: CommunicationHub, private agentId: string) {}
  
  async startLogStreaming(): Promise<void> {
    const logStream = this.createLogStream();
    
    logStream.on('data', async (logEntry) => {
      await this.communicationHub.publish(`logs.agent.${this.agentId}`, {
        timestamp: logEntry.timestamp,
        level: logEntry.level,
        message: logEntry.message,
        context: logEntry.context,
        agentId: this.agentId
      });
    });
    
    // Stream performance metrics
    const metricsStream = this.createMetricsStream();
    
    metricsStream.on('data', async (metrics) => {
      await this.communicationHub.publish(`metrics.agent.${this.agentId}`, {
        timestamp: metrics.timestamp,
        cpu: metrics.cpu,
        memory: metrics.memory,
        taskCount: metrics.activeTasks,
        responseTime: metrics.averageResponseTime,
        agentId: this.agentId
      });
    });
  }
}

// Log aggregation service
export class LogAggregationService {
  private logBuffer: LogEntry[];
  private metricsBuffer: MetricEntry[];
  
  constructor(private communicationHub: CommunicationHub) {
    this.logBuffer = [];
    this.metricsBuffer = [];
    this.setupSubscriptions();
    this.startBufferFlush();
  }
  
  private setupSubscriptions(): void {
    // Subscribe to all agent logs
    this.communicationHub.subscribe('logs.agent.*', this.handleLogEntry.bind(this));
    
    // Subscribe to all agent metrics
    this.communicationHub.subscribe('metrics.agent.*', this.handleMetricEntry.bind(this));
  }
  
  private async handleLogEntry(event: MessageEvent): Promise<void> {
    const logEntry: LogEntry = {
      ...event.payload,
      receivedAt: new Date(),
      source: event.topic
    };
    
    this.logBuffer.push(logEntry);
    
    // Check for immediate alerts
    if (logEntry.level === 'ERROR' || logEntry.level === 'CRITICAL') {
      await this.triggerAlert(logEntry);
    }
  }
  
  private async handleMetricEntry(event: MessageEvent): Promise<void> {
    const metricEntry: MetricEntry = {
      ...event.payload,
      receivedAt: new Date(),
      source: event.topic
    };
    
    this.metricsBuffer.push(metricEntry);
    
    // Check for performance thresholds
    if (metricEntry.cpu > 0.9 || metricEntry.memory > 0.8) {
      await this.triggerPerformanceAlert(metricEntry);
    }
  }
  
  private startBufferFlush(): void {
    setInterval(async () => {
      if (this.logBuffer.length > 0) {
        await this.flushLogBuffer();
      }
      
      if (this.metricsBuffer.length > 0) {
        await this.flushMetricsBuffer();
      }
    }, 5000); // Flush every 5 seconds
  }
}
```

### 5. MCP Integration Pattern

#### External Tool Integration

```typescript
// MCP server for external tool integration
export class MCPToolIntegration {
  private mcpServer: MCPServer;
  private toolRegistry: Map<string, ExternalTool>;
  
  constructor(private communicationHub: CommunicationHub) {
    this.toolRegistry = new Map();
    this.mcpServer = new MCPServer({
      name: 'claude-flow-tools',
      version: '1.0.0',
      capabilities: ['tools', 'completion']
    });
    
    this.setupMCPHandlers();
  }
  
  private setupMCPHandlers(): void {
    this.mcpServer.onToolCall(async (toolName, input, context) => {
      // Bridge MCP tool calls to internal communication
      const response = await this.communicationHub.request(
        `tools.${toolName}.execute`,
        {
          input,
          context,
          mcpSession: context.sessionId
        },
        30000
      );
      
      return response;
    });
    
    // Handle completion requests
    this.mcpServer.onCompletion(async (params, context) => {
      const completions = await this.communicationHub.request(
        'completion.aggregate',
        {
          prompt: params.prompt,
          context: params.context,
          maxTokens: params.maxTokens
        },
        10000
      );
      
      return completions;
    });
  }
  
  async registerExternalTool(tool: ExternalTool): Promise<void> {
    this.toolRegistry.set(tool.name, tool);
    
    // Subscribe to tool execution requests
    this.communicationHub.subscribe(
      `tools.${tool.name}.execute`,
      this.handleToolExecution.bind(this, tool)
    );
    
    // Register with MCP server
    await this.mcpServer.registerTool({
      name: tool.name,
      description: tool.description,
      inputSchema: tool.inputSchema,
      execute: async (input, context) => {
        return await tool.execute(input, context);
      }
    });
  }
  
  private async handleToolExecution(tool: ExternalTool, event: MessageEvent): Promise<void> {
    const { input, context, replyTo } = event.payload;
    
    try {
      const result = await tool.execute(input, context);
      
      await this.communicationHub.publish(replyTo, {
        success: true,
        result,
        toolName: tool.name,
        executedAt: new Date()
      });
    } catch (error) {
      await this.communicationHub.publish(replyTo, {
        success: false,
        error: error.message,
        toolName: tool.name,
        executedAt: new Date()
      });
    }
  }
}
```

### 6. Circuit Breaker Pattern for Resilience

#### Service-to-Service Communication with Fault Tolerance

```typescript
// Service with circuit breaker protection
export class ResilientService {
  private circuitBreakers: Map<string, CircuitBreaker>;
  
  constructor(private communicationHub: CommunicationHub) {
    this.circuitBreakers = new Map();
    this.initializeCircuitBreakers();
  }
  
  private initializeCircuitBreakers(): void {
    const services = ['agent-management', 'workflow-engine', 'state-management'];
    
    for (const service of services) {
      const circuitBreaker = new CircuitBreaker({
        failureThreshold: 5,
        timeout: 60000,
        halfOpenSuccessThreshold: 3,
        monitoringPeriod: 300000
      });
      
      this.circuitBreakers.set(service, circuitBreaker);
    }
  }
  
  async callService(serviceName: string, topic: string, payload: any): Promise<any> {
    const circuitBreaker = this.circuitBreakers.get(serviceName);
    
    if (!circuitBreaker) {
      // No circuit breaker configured, call directly
      return await this.communicationHub.request(topic, payload);
    }
    
    try {
      return await circuitBreaker.execute(async () => {
        return await this.communicationHub.request(topic, payload, 30000);
      });
    } catch (error) {
      if (error instanceof CircuitBreakerOpenError) {
        // Circuit is open, use fallback
        return await this.handleServiceUnavailable(serviceName, topic, payload);
      }
      throw error;
    }
  }
  
  private async handleServiceUnavailable(serviceName: string, topic: string, payload: any): Promise<any> {
    // Implement fallback strategies
    switch (serviceName) {
      case 'agent-management':
        return await this.handleAgentServiceFallback(topic, payload);
      case 'workflow-engine':
        return await this.handleWorkflowServiceFallback(topic, payload);
      case 'state-management':
        return await this.handleStateServiceFallback(topic, payload);
      default:
        throw new ServiceUnavailableError(`Service ${serviceName} is currently unavailable`);
    }
  }
  
  private async handleAgentServiceFallback(topic: string, payload: any): Promise<any> {
    if (topic.includes('agent.status')) {
      // Return cached status or default
      return { status: 'unknown', lastSeen: new Date() };
    }
    
    if (topic.includes('agent.spawn')) {
      // Queue the request for later processing
      await this.queueForLaterProcessing('agent-management', topic, payload);
      return { queued: true, message: 'Agent spawn request queued' };
    }
    
    throw new ServiceUnavailableError('Agent management service unavailable');
  }
}
```

### 7. Quality of Service Patterns

#### Priority-Based Message Handling

```typescript
// QoS configuration for different message types
export class QoSConfigurationService {
  constructor(private communicationHub: CommunicationHub) {
    this.setupQoSPolicies();
  }
  
  private async setupQoSPolicies(): Promise<void> {
    // Critical system events - highest priority
    await this.communicationHub.qos.configureQoS('system.error.*', {
      priority: MessagePriority.CRITICAL,
      deliveryGuarantee: QoSLevel.EXACTLY_ONCE,
      maxRetries: 5,
      timeout: 5000
    });
    
    // Agent coordination - high priority
    await this.communicationHub.qos.configureQoS('agent.*', {
      priority: MessagePriority.HIGH,
      deliveryGuarantee: QoSLevel.AT_LEAST_ONCE,
      maxRetries: 3,
      timeout: 10000
    });
    
    // Workflow events - normal priority
    await this.communicationHub.qos.configureQoS('workflow.*', {
      priority: MessagePriority.NORMAL,
      deliveryGuarantee: QoSLevel.AT_LEAST_ONCE,
      maxRetries: 2,
      timeout: 30000
    });
    
    // Metrics and logs - low priority
    await this.communicationHub.qos.configureQoS('logs.*', {
      priority: MessagePriority.LOW,
      deliveryGuarantee: QoSLevel.AT_MOST_ONCE,
      maxRetries: 1,
      timeout: 60000
    });
    
    // Rate limiting for different client types
    await this.communicationHub.qos.setRateLimit('dashboard-client', {
      maxRequests: 100,
      refillRate: 10,
      refillPeriod: 1000 // 10 requests per second
    });
    
    await this.communicationHub.qos.setRateLimit('agent-client', {
      maxRequests: 1000,
      refillRate: 100,
      refillPeriod: 1000 // 100 requests per second
    });
  }
}
```

These patterns demonstrate how to effectively use the communication hub for reliable, scalable, and fault-tolerant inter-service communication in the RUST-SS system.