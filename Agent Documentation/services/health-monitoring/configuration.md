# Health Monitoring Service - Configuration Guide

## Core Configuration Structure

```json
{
  "healthMonitoring": {
    "monitoring": {
      "checkInterval": 30000,
      "timeout": 5000,
      "retries": 3,
      "parallelChecks": true,
      "gracePeriod": 60000
    },
    "alerting": {
      "enabled": true,
      "channels": {
        "email": {
          "enabled": true,
          "recipients": ["<EMAIL>"],
          "smtpServer": "${SMTP_SERVER}",
          "smtpPort": 587
        },
        "slack": {
          "enabled": true,
          "webhookUrl": "${SLACK_WEBHOOK_URL}",
          "channel": "#alerts"
        },
        "pagerduty": {
          "enabled": true,
          "integrationKey": "${PAGERDUTY_KEY}"
        }
      },
      "escalation": {
        "levels": [
          { "severity": "warning", "delay": 300000 },
          { "severity": "critical", "delay": 900000 },
          { "severity": "emergency", "delay": 1800000 }
        ]
      }
    },
    "metrics": {
      "collection": {
        "enabled": true,
        "interval": 15000,
        "retention": {
          "raw": "7d",
          "aggregated": "90d",
          "summarized": "1y"
        }
      },
      "storage": {
        "backend": "prometheus",
        "url": "${PROMETHEUS_URL}",
        "pushGateway": "${PROMETHEUS_PUSH_GATEWAY}"
      }
    },
    "thresholds": {
      "cpu": {
        "warning": 70,
        "critical": 85,
        "emergency": 95
      },
      "memory": {
        "warning": 80,
        "critical": 90,
        "emergency": 95
      },
      "disk": {
        "warning": 80,
        "critical": 90,
        "emergency": 95
      },
      "responseTime": {
        "warning": 1000,
        "critical": 3000,
        "emergency": 5000
      }
    }
  }
}
```

## Environment-Specific Configurations

### Development
```json
{
  "environment": "development",
  "healthMonitoring": {
    "monitoring": {
      "checkInterval": 60000,
      "timeout": 10000
    },
    "alerting": {
      "enabled": false
    },
    "thresholds": {
      "cpu": { "warning": 80, "critical": 90 },
      "memory": { "warning": 85, "critical": 95 }
    }
  }
}
```

### Production
```json
{
  "environment": "production",
  "healthMonitoring": {
    "monitoring": {
      "checkInterval": 15000,
      "timeout": 3000,
      "parallelChecks": true
    },
    "alerting": {
      "enabled": true,
      "channels": {
        "email": { "enabled": true },
        "slack": { "enabled": true },
        "pagerduty": { "enabled": true }
      }
    },
    "thresholds": {
      "cpu": { "warning": 60, "critical": 75, "emergency": 90 },
      "memory": { "warning": 70, "critical": 85, "emergency": 95 }
    }
  }
}
```