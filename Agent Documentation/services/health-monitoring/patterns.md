# Health Monitoring Service - Design Patterns

## Core Design Patterns

### Health Check Strategy Pattern

#### Problem
Different services require different types of health checks (HTTP, database, custom business logic) with varying configuration and execution patterns.

#### Solution
```rust
trait HealthCheckStrategy {
    async fn execute(&self) -> HealthCheckResult;
    fn get_check_type(&self) -> HealthCheckType;
    fn get_timeout(&self) -> Duration;
}

struct HttpHealthCheckStrategy {
    endpoint: Url,
    expected_codes: Vec<StatusCode>,
    timeout: Duration,
}

struct DatabaseHealthCheckStrategy {
    connection_pool: Arc<DatabasePool>,
    test_query: String,
    timeout: Duration,
}

struct CustomHealthCheckStrategy {
    check_function: Arc<dyn Fn() -> BoxFuture<'static, HealthCheckResult> + Send + Sync>,
    timeout: Duration,
}

// Context uses appropriate strategy
struct HealthCheckContext {
    strategy: Box<dyn HealthCheckStrategy>,
}

impl HealthCheckContext {
    async fn perform_check(&self) -> HealthCheckResult {
        self.strategy.execute().await
    }
}
```

#### Benefits
- Extensible health check types
- Consistent interface for all check types
- Easy to add new health check strategies

### Observer Pattern for Alert Notifications

#### Problem
Multiple notification channels (email, Slack, PagerDuty) need to be notified when alerts are triggered or resolved.

#### Solution
```rust
trait AlertObserver {
    async fn on_alert_triggered(&self, alert: &Alert);
    async fn on_alert_resolved(&self, alert: &Alert);
    fn get_observer_id(&self) -> &str;
}

struct AlertSubject {
    observers: Vec<Box<dyn AlertObserver>>,
}

impl AlertSubject {
    fn add_observer(&mut self, observer: Box<dyn AlertObserver>) {
        self.observers.push(observer);
    }
    
    async fn notify_alert_triggered(&self, alert: &Alert) {
        for observer in &self.observers {
            if let Err(e) = observer.on_alert_triggered(alert).await {
                log::error!("Failed to notify observer {}: {}", observer.get_observer_id(), e);
            }
        }
    }
    
    async fn notify_alert_resolved(&self, alert: &Alert) {
        for observer in &self.observers {
            observer.on_alert_resolved(alert).await;
        }
    }
}

struct EmailAlertObserver {
    smtp_client: SmtpClient,
    recipients: Vec<String>,
}

struct SlackAlertObserver {
    webhook_url: String,
    channel: String,
}

impl AlertObserver for EmailAlertObserver {
    async fn on_alert_triggered(&self, alert: &Alert) {
        let email = self.format_alert_email(alert);
        self.smtp_client.send_email(email).await?;
    }
}
```

#### Benefits
- Decoupled notification channels
- Easy to add new notification types
- Fault tolerance - one channel failure doesn't affect others

### Circuit Breaker Pattern for Health Checks

#### Problem
Failed external dependencies can cause cascading failures and resource exhaustion in health monitoring.

#### Solution
```rust
struct HealthCheckCircuitBreaker {
    failure_threshold: u32,
    recovery_timeout: Duration,
    state: Arc<RwLock<CircuitBreakerState>>,
    failure_count: Arc<AtomicU32>,
    last_failure_time: Arc<RwLock<Option<Instant>>>,
}

#[derive(Clone, Debug)]
enum CircuitBreakerState {
    Closed,    // Normal operation
    Open,      // Failing fast
    HalfOpen,  // Testing recovery
}

impl HealthCheckCircuitBreaker {
    async fn execute<T>(&self, operation: impl Future<Output = Result<T>>) -> Result<T> {
        match *self.state.read().await {
            CircuitBreakerState::Closed => {
                self.execute_in_closed_state(operation).await
            }
            CircuitBreakerState::Open => {
                if self.should_attempt_reset().await {
                    *self.state.write().await = CircuitBreakerState::HalfOpen;
                    self.execute_in_half_open_state(operation).await
                } else {
                    Err(HealthMonitoringError::CircuitBreakerOpen)
                }
            }
            CircuitBreakerState::HalfOpen => {
                self.execute_in_half_open_state(operation).await
            }
        }
    }
    
    async fn execute_in_closed_state<T>(&self, operation: impl Future<Output = Result<T>>) -> Result<T> {
        match operation.await {
            Ok(result) => {
                self.record_success().await;
                Ok(result)
            }
            Err(error) => {
                self.record_failure().await;
                if self.failure_count.load(Ordering::Relaxed) >= self.failure_threshold {
                    *self.state.write().await = CircuitBreakerState::Open;
                }
                Err(error)
            }
        }
    }
}
```

#### Benefits
- Prevents cascade failures
- Automatic recovery attempts
- Resource protection during outages

### Template Method Pattern for Metric Processing

#### Problem
Different metric types require similar processing steps but with type-specific implementations.

#### Solution
```rust
trait MetricProcessor {
    // Template method
    async fn process_metric(&self, raw_metric: &RawMetric) -> Result<ProcessedMetric> {
        let validated = self.validate_metric(raw_metric)?;
        let normalized = self.normalize_metric(&validated)?;
        let enriched = self.enrich_metric(&normalized).await?;
        let stored = self.store_metric(&enriched).await?;
        self.trigger_analysis(&stored).await?;
        Ok(stored)
    }
    
    // Abstract methods to be implemented by concrete processors
    fn validate_metric(&self, metric: &RawMetric) -> Result<RawMetric>;
    fn normalize_metric(&self, metric: &RawMetric) -> Result<NormalizedMetric>;
    async fn enrich_metric(&self, metric: &NormalizedMetric) -> Result<EnrichedMetric>;
    async fn store_metric(&self, metric: &EnrichedMetric) -> Result<ProcessedMetric>;
    async fn trigger_analysis(&self, metric: &ProcessedMetric) -> Result<()>;
}

struct CpuMetricProcessor {
    storage: Arc<MetricStorage>,
    threshold_analyzer: Arc<ThresholdAnalyzer>,
}

impl MetricProcessor for CpuMetricProcessor {
    fn validate_metric(&self, metric: &RawMetric) -> Result<RawMetric> {
        if metric.value < 0.0 || metric.value > 100.0 {
            return Err(MetricError::InvalidCpuValue(metric.value));
        }
        Ok(metric.clone())
    }
    
    fn normalize_metric(&self, metric: &RawMetric) -> Result<NormalizedMetric> {
        // CPU-specific normalization (e.g., convert to percentage)
        Ok(NormalizedMetric {
            value: metric.value / 100.0,
            unit: MetricUnit::Percentage,
            ..Default::default()
        })
    }
}
```

#### Benefits
- Consistent processing pipeline
- Type-specific customization
- Easy to extend for new metric types

## Architectural Decisions

### Centralized vs Distributed Monitoring

**Decision**: Implement hybrid monitoring with centralized aggregation and distributed collection.

**Rationale**:
- Centralized view needed for system-wide health
- Distributed collection reduces single point of failure
- Local agents can continue monitoring during network partitions

**Implementation**:
```rust
struct HybridMonitoringArchitecture {
    local_agents: HashMap<ServiceId, LocalMonitoringAgent>,
    central_aggregator: CentralHealthAggregator,
    communication_channel: Arc<MonitoringChannel>,
}

struct LocalMonitoringAgent {
    local_checks: Vec<Box<dyn HealthCheck>>,
    local_storage: LocalMetricStorage,
    heartbeat_sender: HeartbeatSender,
}

impl LocalMonitoringAgent {
    async fn run_monitoring_loop(&self) {
        loop {
            // Perform local health checks
            let health_results = self.perform_local_checks().await;
            
            // Store locally
            self.local_storage.store_results(&health_results).await;
            
            // Send to central aggregator (best effort)
            if let Err(e) = self.send_to_central(&health_results).await {
                log::warn!("Failed to send to central aggregator: {}", e);
                // Continue with local monitoring
            }
            
            tokio::time::sleep(self.check_interval).await;
        }
    }
}
```

### Push vs Pull Metrics Collection

**Decision**: Support both push and pull patterns based on use case.

**Rationale**:
- Pull pattern better for infrastructure metrics (Prometheus-style)
- Push pattern better for application metrics and events
- Flexibility allows optimal choice per service

**Implementation**:
```rust
enum MetricCollectionStrategy {
    Pull {
        endpoint: Url,
        interval: Duration,
        timeout: Duration,
    },
    Push {
        receiver_endpoint: Url,
        authentication: AuthConfig,
    },
}

struct MetricCollectionManager {
    pull_collectors: HashMap<ServiceId, PullCollector>,
    push_receivers: HashMap<EndpointId, PushReceiver>,
}

impl MetricCollectionManager {
    async fn start_collection(&self) {
        // Start pull collectors
        for (service_id, collector) in &self.pull_collectors {
            let collector = collector.clone();
            tokio::spawn(async move {
                collector.start_polling().await;
            });
        }
        
        // Start push receivers
        for (endpoint_id, receiver) in &self.push_receivers {
            let receiver = receiver.clone();
            tokio::spawn(async move {
                receiver.start_listening().await;
            });
        }
    }
}
```

### Alert Deduplication Strategy

**Decision**: Implement time-window based deduplication with escalation support.

**Rationale**:
- Prevents alert storms during incidents
- Maintains escalation for unresolved issues
- Balances noise reduction with responsiveness

**Implementation**:
```rust
struct AlertDeduplicationEngine {
    active_alerts: HashMap<AlertKey, AlertState>,
    deduplication_window: Duration,
    escalation_schedule: Vec<Duration>,
}

#[derive(Hash, Eq, PartialEq)]
struct AlertKey {
    service_id: String,
    alert_type: AlertType,
    resource_id: Option<String>,
}

struct AlertState {
    first_occurrence: DateTime<Utc>,
    last_occurrence: DateTime<Utc>,
    count: u32,
    escalation_level: usize,
    last_notification: DateTime<Utc>,
}

impl AlertDeduplicationEngine {
    fn should_send_notification(&mut self, alert: &Alert) -> Option<NotificationLevel> {
        let alert_key = AlertKey::from(alert);
        let now = Utc::now();
        
        match self.active_alerts.get_mut(&alert_key) {
            Some(state) => {
                state.last_occurrence = now;
                state.count += 1;
                
                // Check if escalation time has passed
                if let Some(escalation_interval) = self.escalation_schedule.get(state.escalation_level) {
                    if now - state.last_notification > *escalation_interval {
                        state.escalation_level += 1;
                        state.last_notification = now;
                        Some(NotificationLevel::Escalation(state.escalation_level))
                    } else {
                        None // Still in quiet period
                    }
                } else {
                    None // Max escalation reached
                }
            }
            None => {
                // New alert
                self.active_alerts.insert(alert_key, AlertState {
                    first_occurrence: now,
                    last_occurrence: now,
                    count: 1,
                    escalation_level: 0,
                    last_notification: now,
                });
                Some(NotificationLevel::Initial)
            }
        }
    }
}
```

## Best Practices

### Health Check Design
- **Shallow vs Deep Checks**: Use shallow checks for readiness, deep checks for liveness
- **Timeout Management**: Set appropriate timeouts to avoid false negatives
- **Dependency Checks**: Include critical dependency health in service health
- **Graceful Degradation**: Report degraded status for partial functionality

### Metric Collection
- **Cardinality Control**: Limit metric label combinations to prevent explosion
- **Sampling**: Use sampling for high-frequency metrics to reduce overhead
- **Aggregation**: Pre-aggregate metrics at collection points when possible
- **Retention Policies**: Implement appropriate retention based on metric importance

### Alert Management
- **Severity Levels**: Use consistent severity levels across all services
- **Actionable Alerts**: Only alert on conditions that require human intervention
- **Alert Fatigue**: Implement deduplication and escalation to prevent fatigue
- **Recovery Notifications**: Always send resolution notifications

### Performance Optimization
- **Async Processing**: Use async/await for all I/O operations
- **Connection Pooling**: Pool connections to external services
- **Caching**: Cache frequently accessed configuration and metadata
- **Batch Processing**: Batch metric writes and alert notifications

## Integration Patterns with Other Services

### Event Bus Integration
```rust
// Health Monitoring publishes health events
struct HealthEventPublisher {
    event_bus: Arc<EventBus>,
}

impl HealthEventPublisher {
    async fn publish_health_change(
        &self,
        service_id: &str,
        old_status: HealthStatus,
        new_status: HealthStatus,
    ) {
        let event = HealthChangeEvent {
            service_id: service_id.to_string(),
            old_status,
            new_status,
            timestamp: Utc::now(),
            metadata: self.collect_metadata(service_id).await,
        };
        
        self.event_bus.publish("health.status.changed", event).await;
    }
}
```

### API Gateway Integration
```rust
// Provide health endpoints for API Gateway
struct HealthApiController {
    health_service: Arc<HealthMonitoringService>,
}

impl HealthApiController {
    async fn get_system_health(&self) -> Result<Json<SystemHealthResponse>> {
        let health_status = self.health_service.get_overall_health().await?;
        Ok(Json(SystemHealthResponse {
            status: health_status.overall_status,
            services: health_status.service_statuses,
            timestamp: Utc::now(),
            uptime: health_status.uptime,
        }))
    }
    
    async fn get_service_health(&self, service_id: &str) -> Result<Json<ServiceHealthResponse>> {
        let service_health = self.health_service.get_service_health(service_id).await?;
        Ok(Json(ServiceHealthResponse {
            service_id: service_id.to_string(),
            status: service_health.status,
            checks: service_health.individual_checks,
            last_check: service_health.last_check_time,
        }))
    }
}
```

These patterns ensure that the Health Monitoring Service provides reliable, scalable, and comprehensive monitoring capabilities while integrating seamlessly with the broader RUST-SS ecosystem.