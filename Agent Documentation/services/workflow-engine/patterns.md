# Workflow Engine Service - Usage Patterns and Examples

## Common Workflow Patterns

### 1. Sequential Workflow Pattern

#### Simple Development Pipeline

```typescript
const developmentPipelineWorkflow: WorkflowDefinition = {
  name: 'standard-development-pipeline',
  description: 'Sequential development workflow',
  tasks: [
    {
      id: 'requirements-analysis',
      type: 'research',
      description: 'Analyze project requirements',
      assignTo: 'business-analyst',
      timeout: 3600000, // 1 hour
      parameters: {
        outputFormat: 'markdown',
        includeUserStories: true
      }
    },
    {
      id: 'architecture-design',
      type: 'design',
      description: 'Create system architecture',
      assignTo: 'architect',
      dependencies: ['requirements-analysis'],
      timeout: 7200000, // 2 hours
      parameters: {
        includeSecurityReview: true,
        targetPlatform: 'kubernetes'
      }
    },
    {
      id: 'implementation',
      type: 'coding',
      description: 'Implement the solution',
      assignTo: 'developer',
      dependencies: ['architecture-design'],
      timeout: 28800000, // 8 hours
      retry: {
        maxAttempts: 3,
        backoffMultiplier: 2
      }
    },
    {
      id: 'testing',
      type: 'testing',
      description: 'Execute test suite',
      assignTo: 'qa-engineer',
      dependencies: ['implementation'],
      timeout: 3600000
    }
  ],
  completion: {
    criteria: 'all-tasks',
    successThreshold: 1.0
  }
};

// Execute the workflow
const workflowEngine = new WorkflowEngine(config);
const execution = await workflowEngine.executeWorkflow(developmentPipelineWorkflow, {
  projectName: 'user-authentication-service',
  targetEnvironment: 'production'
});

// Monitor progress
execution.on('task:completed', (taskId, result) => {
  console.log(`Task ${taskId} completed:`, result);
});

const result = await execution.waitForCompletion(86400000); // 24 hours
```

### 2. Parallel Execution Pattern

#### Multi-Service Development

```typescript
const parallelDevelopmentWorkflow: WorkflowDefinition = {
  name: 'microservices-parallel-development',
  description: 'Develop multiple services concurrently',
  tasks: [
    {
      id: 'user-service',
      type: 'service-development',
      description: 'Develop user management service',
      assignTo: 'team-alpha',
      parallel: true,
      parameters: {
        serviceName: 'user-service',
        database: 'postgresql',
        framework: 'express'
      }
    },
    {
      id: 'payment-service',
      type: 'service-development',
      description: 'Develop payment processing service',
      assignTo: 'team-beta',
      parallel: true,
      parameters: {
        serviceName: 'payment-service',
        database: 'mongodb',
        framework: 'fastify',
        security: 'pci-compliant'
      }
    },
    {
      id: 'notification-service',
      type: 'service-development',
      description: 'Develop notification service',
      assignTo: 'team-gamma',
      parallel: true,
      parameters: {
        serviceName: 'notification-service',
        messageQueue: 'rabbitmq',
        framework: 'express'
      }
    },
    {
      id: 'integration-testing',
      type: 'integration-testing',
      description: 'Test service integration',
      assignTo: 'qa-team',
      dependencies: ['user-service', 'payment-service', 'notification-service'],
      parameters: {
        testEnvironment: 'staging',
        loadTesting: true
      }
    }
  ]
};

// Execute with resource management
const execution = await workflowEngine.executeWorkflow(parallelDevelopmentWorkflow, {
  maxParallelTasks: 3,
  resourcePool: 'development-cluster'
});
```

### 3. Conditional Branching Pattern

#### Feature Flag Driven Development

```typescript
const conditionalWorkflow: WorkflowDefinition = {
  name: 'feature-conditional-deployment',
  description: 'Deploy features based on conditions',
  tasks: [
    {
      id: 'feature-development',
      type: 'development',
      description: 'Develop the feature',
      assignTo: 'developer'
    },
    {
      id: 'security-review',
      type: 'security-analysis',
      description: 'Security review for sensitive features',
      assignTo: 'security-team',
      dependencies: ['feature-development'],
      conditions: [
        {
          field: 'feature.type',
          operator: 'in',
          value: ['authentication', 'payment', 'data-processing']
        }
      ]
    },
    {
      id: 'performance-testing',
      type: 'performance-testing',
      description: 'Performance testing for high-load features',
      assignTo: 'performance-team',
      dependencies: ['feature-development'],
      conditions: [
        {
          field: 'feature.expectedLoad',
          operator: 'gt',
          value: 1000
        }
      ]
    },
    {
      id: 'staging-deployment',
      type: 'deployment',
      description: 'Deploy to staging environment',
      assignTo: 'devops-team',
      dependencies: ['feature-development'],
      conditions: [
        {
          field: 'environment',
          operator: 'eq',
          value: 'staging'
        }
      ]
    },
    {
      id: 'production-deployment',
      type: 'deployment',
      description: 'Deploy to production environment',
      assignTo: 'devops-team',
      dependencies: ['staging-deployment', 'security-review'],
      conditions: [
        {
          field: 'approvals.security',
          operator: 'eq',
          value: true
        },
        {
          field: 'approvals.product',
          operator: 'eq',
          value: true
        }
      ]
    }
  ]
};
```

### 4. TodoWrite Pattern Implementation

#### Batch Task Management

```typescript
// Using TodoWrite pattern for coordinated task management
const todoItems: TodoItem[] = [
  {
    id: 'architecture_design',
    content: 'Design system architecture and component interfaces',
    status: 'pending',
    priority: 'high',
    dependencies: [],
    estimatedTime: '60min',
    assignedAgent: 'architect'
  },
  {
    id: 'frontend_development',
    content: 'Develop React components and user interface',
    status: 'pending',
    priority: 'medium',
    dependencies: ['architecture_design'],
    estimatedTime: '120min',
    assignedAgent: 'frontend_team'
  },
  {
    id: 'backend_api',
    content: 'Implement RESTful API endpoints',
    status: 'pending',
    priority: 'high',
    dependencies: ['architecture_design'],
    estimatedTime: '90min',
    assignedAgent: 'backend_team'
  },
  {
    id: 'database_setup',
    content: 'Configure database schema and migrations',
    status: 'pending',
    priority: 'high',
    dependencies: ['architecture_design'],
    estimatedTime: '45min',
    assignedAgent: 'backend_team'
  },
  {
    id: 'integration_testing',
    content: 'Create end-to-end integration tests',
    status: 'pending',
    priority: 'medium',
    dependencies: ['frontend_development', 'backend_api', 'database_setup'],
    estimatedTime: '75min',
    assignedAgent: 'qa_team'
  }
];

// Create batch workflow from todo items
const batchEngine = new BatchOperationEngine(workflowEngine);
const batchId = await batchEngine.createTodoList(todoItems);

// Monitor batch progress
const monitorProgress = async () => {
  const progress = await batchEngine.trackBatchProgress(batchId);
  console.log(`Batch Progress: ${progress.completedTasks}/${progress.totalTasks}`);
  
  if (progress.status === 'completed') {
    console.log('All tasks completed successfully!');
  } else if (progress.status === 'failed') {
    console.log('Batch execution failed, rolling back...');
    await batchEngine.rollbackBatch(batchId);
  }
};

setInterval(monitorProgress, 30000); // Check every 30 seconds
```

### 5. Complex State Machine Workflow

#### Enterprise Software Lifecycle

```typescript
// Complex workflow with multiple states and branches
const createEnterpriseWorkflow = async () => {
  const workflowDefinition: StateMachineWorkflow = {
    name: 'enterprise-software-lifecycle',
    type: 'state-machine',
    version: '2.1',
    variables: {
      project_name: 'enterprise-platform',
      security_level: 'high',
      compliance_required: true,
      deployment_target: 'kubernetes'
    },
    states: {
      'initial': {
        type: 'trigger',
        next: 'requirements-gathering'
      },
      'requirements-gathering': {
        type: 'parallel',
        description: 'Comprehensive requirements analysis',
        branches: {
          'stakeholder-analysis': {
            agent: 'business-analyst',
            tasks: ['stakeholder-mapping', 'requirement-elicitation', 'priority-analysis'],
            duration: '2w',
            deliverables: ['stakeholder-map.md', 'requirements.md']
          },
          'technical-feasibility': {
            agent: 'technical-architect',
            tasks: ['technology-assessment', 'integration-analysis', 'performance-requirements'],
            duration: '1w',
            deliverables: ['feasibility-report.md', 'tech-constraints.md']
          },
          'compliance-review': {
            agent: 'compliance-officer',
            condition: '${compliance_required}',
            tasks: ['regulatory-mapping', 'compliance-gap-analysis', 'audit-requirements'],
            duration: '1w',
            deliverables: ['compliance-plan.md']
          }
        },
        completion: 'all-branches',
        next: 'architecture-design'
      },
      'architecture-design': {
        type: 'sequential',
        description: 'Comprehensive system architecture',
        tasks: [
          {
            id: 'high-level-architecture',
            type: 'architecture',
            assignTo: 'chief-architect',
            timeout: 604800000, // 1 week
            parameters: {
              inputs: ['requirements.md', 'tech-constraints.md'],
              outputs: ['architecture-overview.md', 'component-diagram.png']
            }
          }
        ],
        next: 'implementation-planning'
      }
    },
    initialState: 'initial',
    tasks: []
  };

  return await workflowEngine.executeWorkflow(workflowDefinition);
};
```

### 6. Error Handling and Recovery Patterns

#### Resilient Workflow with Compensation

```typescript
const resilientWorkflow: WorkflowDefinition = {
  name: 'resilient-data-processing',
  description: 'Data processing with comprehensive error handling',
  tasks: [
    {
      id: 'data-validation',
      type: 'validation',
      description: 'Validate input data',
      assignTo: 'validator',
      retry: {
        maxAttempts: 3,
        backoffMultiplier: 2
      }
    },
    {
      id: 'data-transformation',
      type: 'transformation',
      description: 'Transform data to target format',
      assignTo: 'transformer',
      dependencies: ['data-validation'],
      retry: {
        maxAttempts: 5,
        backoffMultiplier: 1.5
      }
    },
    {
      id: 'data-storage',
      type: 'storage',
      description: 'Store processed data',
      assignTo: 'storage-handler',
      dependencies: ['data-transformation'],
      retry: {
        maxAttempts: 3,
        backoffMultiplier: 2
      }
    }
  ],
  errorHandling: {
    retryPolicy: {
      maxRetries: 3,
      backoffMultiplier: 2,
      maxBackoff: 30000,
      retryableErrors: ['NetworkError', 'TimeoutError', 'TemporaryFailure']
    },
    escalation: {
      onFailure: ['tech-lead', 'ops-team'],
      onTimeout: ['ops-team'],
      onResourceConstraint: ['resource-manager']
    },
    rollback: {
      triggers: ['critical-failure', 'data-corruption'],
      strategy: 'compensation',
      preservePartialResults: false
    },
    compensation: {
      'data-storage': 'cleanup-storage',
      'data-transformation': 'revert-transformation',
      'data-validation': 'clear-validation-cache'
    }
  }
};

// Execute with monitoring
const execution = await workflowEngine.executeWorkflow(resilientWorkflow);

execution.on('task:failed', async (taskId, error) => {
  console.log(`Task ${taskId} failed:`, error.message);
  
  // Attempt automatic recovery
  if (error.type === 'TemporaryFailure') {
    console.log('Attempting automatic retry...');
    await execution.retryTask(taskId);
  }
});

execution.on('error:escalated', (escalationInfo) => {
  console.log('Error escalated to:', escalationInfo.recipients);
  // Send notifications to stakeholders
});
```

### 7. Workflow Templates and Reusability

#### Creating Reusable Templates

```typescript
// Define reusable template
const microserviceTemplate: WorkflowDefinition = {
  name: 'microservice-development-template',
  description: 'Standard microservice development workflow',
  parameters: {
    serviceName: { type: 'string', required: true },
    database: { type: 'string', default: 'postgresql' },
    framework: { type: 'string', default: 'express' },
    includeAuth: { type: 'boolean', default: false }
  },
  tasks: [
    {
      id: 'setup-project',
      type: 'project-setup',
      description: 'Initialize project structure',
      assignTo: 'developer',
      parameters: {
        serviceName: '${serviceName}',
        framework: '${framework}'
      }
    },
    {
      id: 'implement-api',
      type: 'api-development',
      description: 'Implement REST API',
      assignTo: 'developer',
      dependencies: ['setup-project'],
      parameters: {
        database: '${database}',
        includeAuth: '${includeAuth}'
      }
    },
    {
      id: 'create-tests',
      type: 'testing',
      description: 'Create unit and integration tests',
      assignTo: 'developer',
      dependencies: ['implement-api']
    },
    {
      id: 'deploy-service',
      type: 'deployment',
      description: 'Deploy to staging environment',
      assignTo: 'devops',
      dependencies: ['create-tests']
    }
  ]
};

// Register template
await workflowEngine.registerTemplate('microservice-development', microserviceTemplate);

// Use template to create specific workflows
const userServiceWorkflow = await workflowEngine.instantiateTemplate(
  'microservice-development',
  {
    serviceName: 'user-service',
    database: 'postgresql',
    framework: 'fastify',
    includeAuth: true
  }
);

const notificationServiceWorkflow = await workflowEngine.instantiateTemplate(
  'microservice-development',
  {
    serviceName: 'notification-service',
    database: 'mongodb',
    framework: 'express',
    includeAuth: false
  }
);
```

### 8. Human-in-the-Loop Workflows

#### Approval-Based Workflow

```typescript
const approvalWorkflow: WorkflowDefinition = {
  name: 'production-deployment-with-approvals',
  description: 'Production deployment requiring multiple approvals',
  tasks: [
    {
      id: 'code-review',
      type: 'code-review',
      description: 'Peer code review',
      assignTo: 'senior-developer'
    },
    {
      id: 'security-review',
      type: 'manual-approval',
      description: 'Security team approval',
      assignTo: 'security-team',
      dependencies: ['code-review'],
      parameters: {
        approvalType: 'security',
        requiresJustification: true,
        timeout: 172800000 // 48 hours
      }
    },
    {
      id: 'business-approval',
      type: 'manual-approval',
      description: 'Business stakeholder approval',
      assignTo: 'product-manager',
      dependencies: ['code-review'],
      parameters: {
        approvalType: 'business',
        requiresJustification: false,
        timeout: 86400000 // 24 hours
      }
    },
    {
      id: 'production-deployment',
      type: 'deployment',
      description: 'Deploy to production',
      assignTo: 'devops-team',
      dependencies: ['security-review', 'business-approval']
    }
  ]
};

// Handle manual approval tasks
workflowEngine.on('task:awaiting-approval', async (taskId, approvalInfo) => {
  const notificationService = new NotificationService();
  
  await notificationService.sendApprovalRequest({
    taskId,
    assignee: approvalInfo.assignee,
    type: approvalInfo.type,
    timeout: approvalInfo.timeout,
    workflowUrl: `https://dashboard.company.com/workflows/${approvalInfo.workflowId}`
  });
});

// Process approval responses
workflowEngine.on('approval:received', async (taskId, approval) => {
  if (approval.approved) {
    await workflowEngine.completeTask(taskId, {
      success: true,
      approvedBy: approval.approver,
      timestamp: approval.timestamp,
      justification: approval.justification
    });
  } else {
    await workflowEngine.failTask(taskId, new Error(
      `Approval denied by ${approval.approver}: ${approval.reason}`
    ));
  }
});
```

These patterns demonstrate the flexibility and power of the workflow engine for orchestrating complex, multi-step operations with robust error handling, human interaction, and state management.