# Session Manager Service - Implementation Documentation

## Core Classes and Interfaces

### SessionManager Interface

The `SessionManager` interface provides comprehensive session lifecycle management capabilities.

```typescript
interface SessionManager {
  // Create new session
  createSession(userId: string, options?: SessionOptions): Promise<Session>;
  
  // Get existing session
  getSession(sessionId: string): Promise<Session>;
  
  // Update session state
  updateSession(sessionId: string, updates: Partial<Session>): Promise<void>;
  
  // Synchronize with CLI state
  syncWithCLI(sessionId: string): Promise<void>;
  
  // Persist session data
  persistSession(session: Session): Promise<void>;
  
  // Clean up expired sessions
  cleanupSessions(): Promise<void>;
}
```

### Session Data Structure

```typescript
interface Session {
  id: string;
  userId: string;
  createdAt: Date;
  lastActivity: Date;
  
  // Working context
  workingDirectory: string;
  environment: Record<string, string>;
  
  // Command history
  history: CommandHistoryEntry[];
  
  // File attachments
  attachments: AttachedFile[];
  
  // CLI process info
  cliProcess?: {
    pid: number;
    status: 'running' | 'completed' | 'failed';
    startTime: Date;
  };
  
  // Web client info
  webClients: WebClient[];
  
  // Session state
  state: SessionState;
}

interface CommandHistoryEntry {
  command: string;
  timestamp: Date;
  duration: number;
  exitCode: number;
  output?: string;
}

interface SessionState {
  agents: AgentInfo[];
  tasks: TaskInfo[];
  memory: MemoryInfo;
  config: ConfigInfo;
}
```

## Terminal Management Implementation

### TerminalManager Interface

```typescript
interface TerminalManager {
  // Terminal creation and management
  async createTerminal(config?: TerminalConfig): Promise<Terminal>
  async getTerminal(id: string): Promise<Terminal | null>
  async listTerminals(): Promise<Terminal[]>
  async destroyTerminal(id: string): Promise<boolean>
  
  // Pool management
  async getPoolStatus(): Promise<PoolStatus>
  async resizePool(size: number): Promise<void>
  async optimizePool(): Promise<void>
  
  // Session management
  async createSession(name: string, config?: SessionConfig): Promise<Session>
  async getSession(name: string): Promise<Session | null>
  async listSessions(): Promise<Session[]>
  async saveSession(name: string): Promise<void>
  async restoreSession(name: string): Promise<void>
}
```

### Terminal Interface Implementation

```typescript
interface Terminal {
  readonly id: string;
  readonly type: TerminalType;
  readonly status: TerminalStatus;
  readonly shell: string;
  readonly workingDirectory: string;
  readonly environment: Record<string, string>;
  
  // Command execution
  async execute(command: string, options?: ExecutionOptions): Promise<ExecutionResult>
  async executeBatch(commands: string[], options?: BatchOptions): Promise<ExecutionResult[]>
  async executeStream(command: string, options?: StreamOptions): Promise<ExecutionStream>
  
  // State management
  async getWorkingDirectory(): Promise<string>
  async changeDirectory(path: string): Promise<void>
  async setEnvironment(env: Record<string, string>): Promise<void>
  async getHistory(): Promise<string[]>
  
  // Lifecycle
  async reset(): Promise<void>
  async dispose(): Promise<void>
}

type TerminalType = 'vscode' | 'native' | 'ssh' | 'container';
type TerminalStatus = 'initializing' | 'ready' | 'busy' | 'error' | 'disposed';
```

## Session Manager Implementation

### Core SessionManager Class

```typescript
export class SessionManagerImpl implements SessionManager {
  private sessions = new Map<string, Session>();
  private persistence: SessionPersistence;
  private cliSync: CLISync;
  private eventBus: EventBus;
  
  constructor(private config: SessionConfig) {
    this.persistence = new SessionPersistence(config.persistenceOptions);
    this.cliSync = new CLISync(config.cliSyncOptions);
    this.eventBus = new EventBus();
    this.startCleanupTimer();
  }
  
  async createSession(userId: string, options?: SessionOptions): Promise<Session> {
    const session: Session = {
      id: generateSessionId(),
      userId,
      createdAt: new Date(),
      lastActivity: new Date(),
      workingDirectory: options?.workingDirectory || process.cwd(),
      environment: { ...process.env, ...options?.environment },
      history: [],
      attachments: [],
      webClients: [],
      state: {
        agents: [],
        tasks: [],
        memory: { banks: [], totalSize: 0 },
        config: { loaded: false, valid: false }
      }
    };
    
    this.sessions.set(session.id, session);
    await this.persistence.save(session);
    
    // Emit session created event
    this.eventBus.emit('session.created', {
      sessionId: session.id,
      userId: session.userId,
      timestamp: session.createdAt
    });
    
    return session;
  }
  
  async getSession(sessionId: string): Promise<Session> {
    let session = this.sessions.get(sessionId);
    
    if (!session) {
      // Try to load from persistence
      session = await this.persistence.load(sessionId);
      if (session) {
        this.sessions.set(sessionId, session);
      }
    }
    
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Update last activity
    session.lastActivity = new Date();
    
    return session;
  }
  
  async syncWithCLI(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Get latest state from CLI process
    const cliState = await this.cliSync.getState(sessionId);
    
    // Merge states
    session.state = this.mergeStates(session.state, cliState);
    session.lastActivity = new Date();
    
    // Notify web clients of state change
    this.notifyWebClients(session, 'state_updated', session.state);
    
    // Persist updated session
    await this.persistence.save(session);
  }
  
  private notifyWebClients(session: Session, event: string, data: any): void {
    for (const client of session.webClients) {
      if (client.socket && client.socket.readyState === WebSocket.OPEN) {
        client.socket.send(JSON.stringify({
          type: 'session_event',
          event,
          data,
          timestamp: Date.now()
        }));
      }
    }
  }
  
  private mergeStates(sessionState: SessionState, cliState: any): SessionState {
    return {
      agents: cliState.agents || sessionState.agents,
      tasks: cliState.tasks || sessionState.tasks,
      memory: {
        ...sessionState.memory,
        ...cliState.memory
      },
      config: cliState.config || sessionState.config
    };
  }
}
```

### REPL Implementation

```typescript
export class REPLManager {
  private sessions: Map<string, REPLSession>;
  private commandProcessor: CommandProcessor;
  private autoComplete: AutoCompleteEngine;
  
  constructor(config: REPLConfig) {
    this.sessions = new Map();
    this.commandProcessor = new CommandProcessor();
    this.autoComplete = new AutoCompleteEngine();
  }
  
  async createREPLSession(sessionId: string, options: REPLOptions): Promise<REPLSession> {
    const replSession: REPLSession = {
      id: generateREPLId(),
      sessionId,
      mode: options.mode || 'interactive',
      context: new REPLContext(),
      history: new CommandHistory(),
      completions: new CompletionCache(),
      state: 'ready'
    };
    
    this.sessions.set(replSession.id, replSession);
    
    // Set up command handlers
    this.setupCommandHandlers(replSession);
    
    return replSession;
  }
  
  async executeCommand(replId: string, command: string): Promise<CommandResult> {
    const replSession = this.sessions.get(replId);
    if (!replSession) {
      throw new Error(`REPL session ${replId} not found`);
    }
    
    // Add to history
    replSession.history.add(command);
    
    // Parse and validate command
    const parsedCommand = this.commandProcessor.parse(command);
    if (!parsedCommand.valid) {
      return {
        success: false,
        error: parsedCommand.error,
        suggestions: await this.getSuggestions(command)
      };
    }
    
    // Execute command
    try {
      replSession.state = 'executing';
      const result = await this.commandProcessor.execute(parsedCommand, replSession.context);
      
      // Update context with results
      this.updateContext(replSession.context, result);
      
      replSession.state = 'ready';
      
      return {
        success: true,
        output: result.output,
        data: result.data,
        context: replSession.context.export(),
        executionTime: result.executionTime
      };
    } catch (error) {
      replSession.state = 'error';
      
      return {
        success: false,
        error: error.message,
        suggestions: await this.getErrorSuggestions(error)
      };
    }
  }
  
  async getCompletions(replId: string, partial: string): Promise<Completion[]> {
    const replSession = this.sessions.get(replId);
    if (!replSession) {
      throw new Error(`REPL session ${replId} not found`);
    }
    
    // Check cache first
    const cached = replSession.completions.get(partial);
    if (cached && !this.isStale(cached)) {
      return cached.completions;
    }
    
    // Generate new completions
    const completions = await this.autoComplete.generate(
      partial,
      replSession.context,
      replSession.history
    );
    
    // Cache results
    replSession.completions.set(partial, {
      completions,
      timestamp: Date.now(),
      ttl: 300000 // 5 minutes
    });
    
    return completions;
  }
  
  private setupCommandHandlers(replSession: REPLSession): void {
    // Agent management commands
    this.commandProcessor.registerCommand('agent', {
      subcommands: ['spawn', 'list', 'status', 'terminate'],
      handler: new AgentCommandHandler()
    });
    
    // Workflow commands
    this.commandProcessor.registerCommand('workflow', {
      subcommands: ['create', 'execute', 'status', 'cancel'],
      handler: new WorkflowCommandHandler()
    });
    
    // Memory commands
    this.commandProcessor.registerCommand('memory', {
      subcommands: ['store', 'get', 'query', 'export'],
      handler: new MemoryCommandHandler()
    });
    
    // System commands
    this.commandProcessor.registerCommand('system', {
      subcommands: ['status', 'config', 'restart', 'shutdown'],
      handler: new SystemCommandHandler()
    });
  }
}
```

### Terminal Session Management

```typescript
export class TerminalSessionManager {
  private terminals: Map<string, Terminal>;
  private sessionRecordings: Map<string, Recording>;
  
  constructor(private config: TerminalConfig) {
    this.terminals = new Map();
    this.sessionRecordings = new Map();
  }
  
  async createTerminal(sessionId: string, config?: TerminalConfig): Promise<Terminal> {
    const terminal = new TerminalImpl({
      id: generateTerminalId(),
      sessionId,
      type: config?.type || 'native',
      shell: config?.shell || '/bin/bash',
      workingDirectory: config?.workingDirectory || process.cwd(),
      environment: { ...process.env, ...config?.environment },
      size: config?.size || { cols: 80, rows: 24 },
      recording: config?.recording || false
    });
    
    this.terminals.set(terminal.id, terminal);
    
    // Start recording if enabled
    if (config?.recording) {
      await this.startRecording(terminal.id);
    }
    
    // Set up event handlers
    terminal.on('data', (data) => this.handleTerminalData(terminal.id, data));
    terminal.on('exit', (code) => this.handleTerminalExit(terminal.id, code));
    terminal.on('error', (error) => this.handleTerminalError(terminal.id, error));
    
    return terminal;
  }
  
  async attachToSession(sessionId: string, terminalId: string): Promise<void> {
    const terminal = this.terminals.get(terminalId);
    if (!terminal) {
      throw new Error(`Terminal ${terminalId} not found`);
    }
    
    // Restore session context
    const session = await this.getSession(sessionId);
    
    // Apply session environment
    await terminal.setEnvironment(session.environment);
    await terminal.changeDirectory(session.workingDirectory);
    
    // Replay recent history if available
    if (session.history.length > 0) {
      const recentHistory = session.history.slice(-10);
      for (const entry of recentHistory) {
        terminal.write(`# ${entry.command}\n`);
      }
    }
  }
  
  private async startRecording(terminalId: string): Promise<void> {
    const recording: Recording = {
      id: generateRecordingId(),
      terminalId,
      startTime: Date.now(),
      events: [],
      metadata: {
        terminalSize: this.terminals.get(terminalId)?.size,
        shell: this.terminals.get(terminalId)?.shell
      }
    };
    
    this.sessionRecordings.set(terminalId, recording);
  }
  
  private handleTerminalData(terminalId: string, data: string): void {
    const recording = this.sessionRecordings.get(terminalId);
    if (recording) {
      recording.events.push({
        type: 'output',
        timestamp: Date.now() - recording.startTime,
        data: data
      });
    }
    
    // Broadcast to connected clients
    this.broadcastTerminalData(terminalId, data);
  }
  
  private broadcastTerminalData(terminalId: string, data: string): void {
    // Implementation would broadcast to WebSocket clients, etc.
    this.eventBus.emit('terminal.data', {
      terminalId,
      data,
      timestamp: Date.now()
    });
  }
}
```

### Session Collaboration Features

```typescript
export class SessionCollaborationManager {
  private sharedSessions: Map<string, SharedSession>;
  private collaborators: Map<string, Collaborator>;
  
  constructor(private communicationHub: CommunicationHub) {
    this.sharedSessions = new Map();
    this.collaborators = new Map();
  }
  
  async shareSession(sessionId: string, options: ShareOptions): Promise<ShareToken> {
    const sharedSession: SharedSession = {
      sessionId,
      ownerId: options.ownerId,
      collaborators: new Set(),
      permissions: options.permissions || {
        canExecute: false,
        canView: true,
        canEdit: false
      },
      createdAt: Date.now(),
      expiresAt: Date.now() + (options.duration || 3600000) // 1 hour default
    };
    
    this.sharedSessions.set(sessionId, sharedSession);
    
    // Generate share token
    const shareToken = this.generateShareToken(sessionId, options);
    
    // Set up collaboration channels
    await this.setupCollaborationChannels(sessionId);
    
    return shareToken;
  }
  
  async joinSharedSession(shareToken: string, userId: string): Promise<SessionAccess> {
    const tokenData = this.validateShareToken(shareToken);
    if (!tokenData.valid) {
      throw new Error('Invalid or expired share token');
    }
    
    const sharedSession = this.sharedSessions.get(tokenData.sessionId);
    if (!sharedSession) {
      throw new Error('Shared session not found');
    }
    
    // Add collaborator
    const collaborator: Collaborator = {
      userId,
      sessionId: tokenData.sessionId,
      joinedAt: Date.now(),
      permissions: sharedSession.permissions,
      cursorPosition: { line: 0, column: 0 },
      status: 'active'
    };
    
    this.collaborators.set(userId, collaborator);
    sharedSession.collaborators.add(userId);
    
    // Notify other collaborators
    await this.broadcastCollaboratorEvent(tokenData.sessionId, 'collaborator:joined', {
      userId,
      timestamp: Date.now()
    });
    
    return {
      sessionId: tokenData.sessionId,
      permissions: sharedSession.permissions,
      collaborators: Array.from(sharedSession.collaborators)
    };
  }
  
  async broadcastMessage(sessionId: string, senderId: string, message: CollaborationMessage): Promise<void> {
    const sharedSession = this.sharedSessions.get(sessionId);
    if (!sharedSession) {
      throw new Error('Session not shared');
    }
    
    // Broadcast to all collaborators except sender
    for (const collaboratorId of sharedSession.collaborators) {
      if (collaboratorId !== senderId) {
        await this.communicationHub.publish(`collaboration.${sessionId}.${collaboratorId}`, {
          type: message.type,
          senderId,
          data: message.data,
          timestamp: Date.now()
        });
      }
    }
  }
  
  async synchronizeCursor(sessionId: string, userId: string, position: CursorPosition): Promise<void> {
    const collaborator = this.collaborators.get(userId);
    if (!collaborator) {
      throw new Error('User not in collaboration session');
    }
    
    collaborator.cursorPosition = position;
    
    // Broadcast cursor position to other collaborators
    await this.broadcastCollaboratorEvent(sessionId, 'cursor:moved', {
      userId,
      position,
      timestamp: Date.now()
    });
  }
  
  private async setupCollaborationChannels(sessionId: string): Promise<void> {
    // Set up communication channels for real-time collaboration
    await this.communicationHub.subscribe(`collaboration.${sessionId}.*`, 
      this.handleCollaborationMessage.bind(this)
    );
  }
  
  private async broadcastCollaboratorEvent(sessionId: string, event: string, data: any): Promise<void> {
    await this.communicationHub.publish(`collaboration.${sessionId}.broadcast`, {
      event,
      data,
      timestamp: Date.now()
    });
  }
}
```

This implementation provides a comprehensive foundation for session management, including REPL interfaces, terminal emulation, collaboration features, and integration with the broader RUST-SS ecosystem.