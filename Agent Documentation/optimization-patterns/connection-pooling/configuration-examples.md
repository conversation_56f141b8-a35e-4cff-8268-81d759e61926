# Connection Pool Configuration Examples

## Default Configuration
```typescript
const defaultPoolConfig = {
  min: 2,                          // Maintain 2 connections minimum
  max: 10,                         // Allow up to 10 connections
  acquireTimeoutMillis: 30000,     // 30s timeout for acquiring
  idleTimeoutMillis: 30000,        // 30s idle before eviction
  evictionRunIntervalMillis: 10000,// Check every 10s
  testOnBorrow: true,              // Validate before use
  testOnReturn: true,              // Validate on return
  testWhileIdle: true,             // Test idle connections
  maxConnectionAge: 3600000,       // 1 hour max age
  healthCheckInterval: 30000,      // Check health every 30s
  leakDetectionThreshold: 60000,   // 1 minute leak detection
  validationTimeout: 5000          // 5s validation timeout
};
```

## Production Configuration
```typescript
// High-traffic production environment
const productionConfig = {
  min: 5,                          // Higher minimum for availability
  max: 50,                         // Support more concurrent requests
  acquireTimeoutMillis: 60000,     // Longer timeout for peak loads
  idleTimeoutMillis: 120000,       // Keep connections longer
  evictionRunIntervalMillis: 30000,// Less frequent eviction
  testOnBorrow: true,              // Always validate
  testOnReturn: false,             // Skip return validation for performance
  testWhileIdle: true,             // Maintain pool health
  maxConnectionAge: 7200000,       // 2 hours max age
  healthCheckInterval: 60000,      // Less frequent health checks
  leakDetectionThreshold: 300000,  // 5 minutes for long operations
  validationTimeout: 10000         // Longer validation timeout
};
```

## Development Configuration
```typescript
// Development environment with debugging
const developmentConfig = {
  min: 1,                          // Minimal resources
  max: 5,                          // Limited connections
  acquireTimeoutMillis: 5000,      // Quick timeout for debugging
  idleTimeoutMillis: 10000,        // Quick eviction
  evictionRunIntervalMillis: 5000, // Frequent checks
  testOnBorrow: true,              // Full validation
  testOnReturn: true,              // Full validation
  testWhileIdle: true,             // Full validation
  maxConnectionAge: 600000,        // 10 minutes max age
  healthCheckInterval: 10000,      // Frequent health checks
  leakDetectionThreshold: 30000,   // Quick leak detection
  validationTimeout: 3000          // Quick validation
};
```

## Specialized Configurations

### High-Reliability Configuration
```typescript
// For critical operations requiring maximum reliability
const highReliabilityConfig = {
  min: 10,
  max: 30,
  acquireTimeoutMillis: 45000,
  idleTimeoutMillis: 60000,
  evictionRunIntervalMillis: 15000,
  testOnBorrow: true,
  testOnReturn: true,
  testWhileIdle: true,
  maxConnectionAge: 1800000,       // 30 minutes rotation
  healthCheckInterval: 20000,      // Frequent health monitoring
  leakDetectionThreshold: 120000,  // 2 minutes leak detection
  validationTimeout: 7500          // Thorough validation
};
```

### Resource-Constrained Configuration
```typescript
// For environments with limited resources
const resourceConstrainedConfig = {
  min: 1,
  max: 3,
  acquireTimeoutMillis: 20000,
  idleTimeoutMillis: 15000,        // Aggressive eviction
  evictionRunIntervalMillis: 5000,  // Frequent cleanup
  testOnBorrow: true,
  testOnReturn: false,              // Skip to save resources
  testWhileIdle: false,             // Minimize overhead
  maxConnectionAge: 900000,         // 15 minutes max
  healthCheckInterval: 60000,       // Infrequent checks
  leakDetectionThreshold: 60000,
  validationTimeout: 3000
};
```

### Batch Processing Configuration
```typescript
// For batch processing with predictable patterns
const batchProcessingConfig = {
  min: 20,                         // Pre-warm for batch starts
  max: 100,                        // Handle large batches
  acquireTimeoutMillis: 120000,    // Long timeout for queuing
  idleTimeoutMillis: 300000,       // Keep connections between batches
  evictionRunIntervalMillis: 60000,// Infrequent eviction
  testOnBorrow: false,             // Skip for performance
  testOnReturn: false,             // Skip for performance
  testWhileIdle: true,             // Maintain health between batches
  maxConnectionAge: 14400000,      // 4 hours for long batches
  healthCheckInterval: 120000,     // Check between batch windows
  leakDetectionThreshold: 600000,  // 10 minutes for long operations
  validationTimeout: 5000
};
```

## Dynamic Configuration Examples

### Load-Based Adjustment
```typescript
// Adjust pool size based on current load
function adjustPoolSize(currentLoad: number, poolConfig: PoolConfig) {
  if (currentLoad > 0.8) {
    // High load - increase pool size
    poolConfig.min = Math.min(poolConfig.min * 1.5, 20);
    poolConfig.max = Math.min(poolConfig.max * 1.5, 100);
  } else if (currentLoad < 0.3) {
    // Low load - decrease pool size
    poolConfig.min = Math.max(poolConfig.min * 0.7, 2);
    poolConfig.max = Math.max(poolConfig.max * 0.7, 10);
  }
}
```

### Time-Based Configuration
```typescript
// Different configurations for different times
function getTimeBasedConfig(): PoolConfig {
  const hour = new Date().getHours();
  
  if (hour >= 9 && hour <= 17) {
    // Business hours - high capacity
    return {
      min: 10,
      max: 50,
      ...productionConfig
    };
  } else if (hour >= 0 && hour <= 6) {
    // Night hours - minimal resources
    return {
      min: 2,
      max: 10,
      ...resourceConstrainedConfig
    };
  } else {
    // Regular hours
    return defaultPoolConfig;
  }
}
```

### Environment-Based Configuration
```typescript
// Configuration based on environment variables
const environmentConfig = {
  min: parseInt(process.env.POOL_MIN || '2'),
  max: parseInt(process.env.POOL_MAX || '10'),
  acquireTimeoutMillis: parseInt(process.env.POOL_ACQUIRE_TIMEOUT || '30000'),
  idleTimeoutMillis: parseInt(process.env.POOL_IDLE_TIMEOUT || '30000'),
  evictionRunIntervalMillis: parseInt(process.env.POOL_EVICTION_INTERVAL || '10000'),
  testOnBorrow: process.env.POOL_TEST_ON_BORROW !== 'false',
  testOnReturn: process.env.POOL_TEST_ON_RETURN !== 'false',
  testWhileIdle: process.env.POOL_TEST_WHILE_IDLE !== 'false',
  maxConnectionAge: parseInt(process.env.POOL_MAX_AGE || '3600000'),
  healthCheckInterval: parseInt(process.env.POOL_HEALTH_CHECK_INTERVAL || '30000'),
  leakDetectionThreshold: parseInt(process.env.POOL_LEAK_THRESHOLD || '60000'),
  validationTimeout: parseInt(process.env.POOL_VALIDATION_TIMEOUT || '5000')
};
```

## Integration Examples

### With OptimizedExecutor
```typescript
const executorConfig = {
  connectionPool: {
    min: 5,
    max: 20
  },
  concurrency: 10,
  caching: {
    enabled: true,
    ttl: 3600000,      // 1 hour cache
    maxSize: 1000
  },
  fileOperations: {
    outputDir: './results',
    concurrency: 10
  },
  monitoring: {
    metricsInterval: 30000,
    slowTaskThreshold: 10000
  }
};

const executor = new OptimizedExecutor(executorConfig);
```

### With Circuit Breaker
```typescript
// Pool configuration with circuit breaker integration
const circuitBreakerPoolConfig = {
  ...productionConfig,
  // Reduce timeouts when circuit is half-open
  acquireTimeoutMillis: 15000,
  validationTimeout: 2500,
  // More aggressive health checking
  healthCheckInterval: 15000,
  testOnBorrow: true,
  testOnReturn: true
};
```

### Multi-Region Configuration
```typescript
// Different pools for different regions
const regionConfigs = {
  'us-east': {
    min: 10,
    max: 50,
    healthCheckInterval: 30000
  },
  'eu-west': {
    min: 5,
    max: 30,
    healthCheckInterval: 45000  // Account for latency
  },
  'ap-south': {
    min: 3,
    max: 20,
    healthCheckInterval: 60000  // Higher latency region
  }
};
```

## Monitoring Configuration

### Metrics Collection
```typescript
// Enable detailed metrics collection
const metricsConfig = {
  ...productionConfig,
  // Add event listeners for metrics
  onConnectionCreated: (conn) => metrics.increment('pool.connections.created'),
  onConnectionDestroyed: (conn) => metrics.increment('pool.connections.destroyed'),
  onConnectionAcquired: (conn) => metrics.increment('pool.connections.acquired'),
  onConnectionReleased: (conn) => metrics.increment('pool.connections.released'),
  onHealthCheckFailed: (conn) => metrics.increment('pool.health.failed'),
  onLeakDetected: (conn) => metrics.increment('pool.leaks.detected')
};
```

### Alert Thresholds
```typescript
// Configuration for alerting
const alertingConfig = {
  poolExhaustedThreshold: 0.9,    // Alert when 90% utilized
  healthCheckFailureRate: 0.2,    // Alert on 20% failure rate
  averageWaitTime: 5000,          // Alert on 5s average wait
  leakDetectionRate: 0.05         // Alert on 5% leak rate
};
```