# Load Balancing Configuration Examples

## Basic Configuration

### Simple Round Robin
```yaml
# config/load_balancer_simple.yaml
load_balancer:
  strategy: "round_robin"
  targets:
    - id: "agent-1"
      address: "*********:8080"
    - id: "agent-2"
      address: "*********:8080"
    - id: "agent-3"
      address: "*********:8080"
  health_check:
    interval: 10s
    timeout: 2s
    healthy_threshold: 2
    unhealthy_threshold: 3
```

### Weighted Distribution
```yaml
# config/load_balancer_weighted.yaml
load_balancer:
  strategy: "weighted_round_robin"
  targets:
    - id: "agent-high-capacity"
      address: "*********:8080"
      weight: 10  # Gets 10x more traffic
      metadata:
        cpu_cores: 32
        memory_gb: 128
        
    - id: "agent-medium-capacity"
      address: "*********:8080"
      weight: 5   # Gets 5x more traffic
      metadata:
        cpu_cores: 16
        memory_gb: 64
        
    - id: "agent-low-capacity"
      address: "*********:8080"
      weight: 1   # Baseline traffic
      metadata:
        cpu_cores: 4
        memory_gb: 16
```

## Advanced Strategies

### Power of Two Choices
```yaml
# config/load_balancer_p2c.yaml
load_balancer:
  strategy: "power_of_two"
  
  # Selection criteria
  selection_metric: "composite_score"
  
  # Locality preference (0.0-1.0)
  # 0.8 = 80% preference for same-zone targets
  locality_preference: 0.8
  
  # Maximum acceptable load difference
  max_load_difference: 2.0
  
  # Target discovery
  service_discovery:
    type: "consul"
    service_name: "worker-agents"
    datacenter: "us-east-1"
    
  # Metrics for selection
  metrics:
    - type: "active_connections"
      weight: 0.4
    - type: "response_time_p99"
      weight: 0.3
    - type: "error_rate"
      weight: 0.3
```

### Consistent Hashing
```yaml
# config/load_balancer_consistent_hash.yaml
load_balancer:
  strategy: "consistent_hash"
  
  # Virtual nodes for better distribution
  virtual_nodes_per_target: 150
  
  # Hash function
  hash_function: "xxhash3"
  
  # Session affinity
  session_affinity:
    enabled: true
    ttl: 3600s
    cookie_name: "AGENT_SESSION"
    
  # Bounded loads to prevent hotspots
  bounded_loads:
    enabled: true
    overload_factor: 1.25
```

### Least Connections
```yaml
# config/load_balancer_least_conn.yaml
load_balancer:
  strategy: "least_connections"
  
  # Connection counting
  connection_tracking:
    # Include pending connections
    count_pending: true
    
    # Weight active vs pending
    active_weight: 1.0
    pending_weight: 0.5
    
  # Slow start for new targets
  slow_start:
    enabled: true
    duration: 30s
    initial_weight: 0.1
```

## Multi-Region Configuration

### Geographic Load Balancing
```yaml
# config/load_balancer_geo.yaml
load_balancer:
  strategy: "geographic"
  
  regions:
    us-east-1:
      weight: 100
      targets:
        - id: "use1-agent-1"
          address: "*********:8080"
          zone: "us-east-1a"
        - id: "use1-agent-2"
          address: "*********:8080"
          zone: "us-east-1b"
          
    us-west-2:
      weight: 80
      targets:
        - id: "usw2-agent-1"
          address: "*********:8080"
          zone: "us-west-2a"
        - id: "usw2-agent-2"
          address: "*********:8080"
          zone: "us-west-2b"
          
    eu-west-1:
      weight: 60
      targets:
        - id: "euw1-agent-1"
          address: "*********:8080"
          zone: "eu-west-1a"
          
  # Failover rules
  failover:
    # Prefer same region
    - from: "us-east-1"
      to: ["us-west-2", "eu-west-1"]
      
    # Then closest region
    - from: "us-west-2"
      to: ["us-east-1", "eu-west-1"]
      
    - from: "eu-west-1"
      to: ["us-east-1", "us-west-2"]
```

### Cross-Zone Load Balancing
```yaml
# config/load_balancer_cross_zone.yaml
load_balancer:
  # Enable cross-zone load balancing
  cross_zone_enabled: true
  
  zones:
    zone-a:
      targets: 5
      capacity_units: 100
      
    zone-b:
      targets: 3
      capacity_units: 60
      
    zone-c:
      targets: 2
      capacity_units: 40
      
  # Zone balancing algorithm
  zone_balancing:
    # Distribute by capacity, not target count
    mode: "capacity_weighted"
    
    # Maximum imbalance allowed
    max_imbalance_percent: 10
    
    # Rebalance interval
    rebalance_interval: 60s
```

## Integration Configurations

### With Circuit Breaker
```yaml
# config/load_balancer_resilient.yaml
load_balancer:
  strategy: "power_of_two"
  
  # Circuit breaker integration
  circuit_breaker:
    # Use breaker state in selection
    respect_breaker_state: true
    
    # Reduced weight for half-open
    half_open_weight: 0.1
    
    # Exclude open breakers
    exclude_open: true
    
  # Retry configuration
  retry:
    enabled: true
    max_attempts: 3
    backoff:
      type: "exponential"
      initial: 100ms
      max: 2s
      multiplier: 2
```

### With Rate Limiter
```yaml
# config/load_balancer_rate_limited.yaml
load_balancer:
  strategy: "weighted_round_robin"
  
  # Per-target rate limits
  rate_limiting:
    enabled: true
    
    # Default limits
    default:
      requests_per_second: 1000
      burst_size: 2000
      
    # Override per target
    per_target:
      "high-capacity-agent":
        requests_per_second: 5000
        burst_size: 10000
        
      "low-capacity-agent":
        requests_per_second: 100
        burst_size: 200
        
  # Behavior when rate limited
  rate_limit_handling:
    # Try next target
    strategy: "spillover"
    
    # Or queue request
    # strategy: "queue"
    # max_queue_size: 1000
    # queue_timeout: 5s
```

## Dynamic Configuration

### Auto-Scaling Integration
```yaml
# config/load_balancer_autoscale.yaml
load_balancer:
  # Base configuration
  strategy: "least_connections"
  
  # Auto-scaling triggers
  auto_scaling:
    enabled: true
    
    # Scale up when
    scale_up:
      avg_connections_per_target: 100
      error_rate_threshold: 0.05
      latency_p99_threshold: 500ms
      
    # Scale down when
    scale_down:
      avg_connections_per_target: 20
      idle_time_threshold: 5m
      
    # Scaling constraints
    min_targets: 2
    max_targets: 50
    cooldown_period: 60s
```

### A/B Testing Configuration
```yaml
# config/load_balancer_ab_test.yaml
load_balancer:
  strategy: "weighted_random"
  
  # A/B test groups
  groups:
    control:
      weight: 90
      targets:
        - "stable-agent-1"
        - "stable-agent-2"
        - "stable-agent-3"
        
    experiment:
      weight: 10
      targets:
        - "canary-agent-1"
        
  # Traffic splitting rules
  traffic_rules:
    # Split by user ID
    - type: "hash"
      key: "user_id"
      
    # Or random assignment
    # - type: "random"
    #   seed: "experiment-123"
```

## Monitoring and Observability

### Metrics Configuration
```yaml
# config/load_balancer_metrics.yaml
monitoring:
  load_balancer:
    # Prometheus metrics
    metrics:
      enabled: true
      port: 9091
      path: "/metrics"
      
      # Detailed metrics
      collect:
        - request_count
        - request_duration
        - target_health
        - connection_count
        - error_rate
        - queue_depth
        
      # Histogram buckets
      latency_buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]
      
    # Distributed tracing
    tracing:
      enabled: true
      sampler:
        type: "probabilistic"
        param: 0.1  # 10% sampling
```

### Health Check Configuration
```yaml
# config/load_balancer_health.yaml
health_checks:
  # HTTP health check
  http:
    enabled: true
    path: "/health"
    interval: 10s
    timeout: 2s
    healthy_threshold: 2
    unhealthy_threshold: 3
    
    # Expected response
    expected_status: [200, 204]
    expected_body_regex: "healthy|ok"
    
  # TCP health check
  tcp:
    enabled: true
    port: 8080
    interval: 5s
    timeout: 1s
    
  # Custom health check
  custom:
    enabled: true
    script: "/usr/local/bin/check_agent_health.sh"
    interval: 30s
    timeout: 5s
```

## Security Configuration

### TLS Configuration
```yaml
# config/load_balancer_tls.yaml
tls:
  # Client certificates
  client_auth:
    enabled: true
    ca_cert: "/etc/certs/ca.pem"
    
  # Per-target TLS
  target_config:
    default:
      cert: "/etc/certs/client.pem"
      key: "/etc/certs/client-key.pem"
      verify_hostname: true
      
    # Override for specific targets
    overrides:
      "internal-agent-*":
        verify_hostname: false
        
      "external-api-*":
        cert: "/etc/certs/external-client.pem"
        key: "/etc/certs/external-client-key.pem"
```