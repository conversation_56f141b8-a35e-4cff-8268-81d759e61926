# Load Balancing Implementation Details

## Overview
The RUST-SS load balancer provides intelligent request distribution across agents, integrating with health monitoring and service discovery for optimal performance and reliability.

## Core Architecture

### Load Balancer Trait
```rust
use async_trait::async_trait;
use std::sync::Arc;
use tokio::sync::RwLock;

#[async_trait]
pub trait LoadBalancer: Send + Sync {
    type Target: Clone + Send + Sync;
    type Context: Send + Sync;
    
    async fn select_target(
        &self,
        ctx: &Self::Context,
    ) -> Result<Self::Target, LoadBalancerError>;
    
    async fn update_targets(&self, targets: Vec<Self::Target>);
    
    async fn report_result(
        &self,
        target: &Self::Target,
        result: RequestResult,
    );
}

#[derive(Debug, Clone)]
pub struct RequestResult {
    pub success: bool,
    pub latency: Duration,
    pub error: Option<String>,
}
```

### Target Management
```rust
#[derive(Debug, <PERSON><PERSON>)]
pub struct LoadBalancerTarget {
    pub id: String,
    pub address: SocketAddr,
    pub metadata: TargetMetadata,
    pub health: Arc<RwLock<HealthStatus>>,
    pub metrics: Arc<RwLock<TargetMetrics>>,
}

#[derive(Debug, Clone)]
pub struct TargetMetadata {
    pub zone: String,
    pub capacity: u32,
    pub version: String,
    pub tags: HashMap<String, String>,
}

#[derive(Debug, Clone)]
pub struct HealthStatus {
    pub healthy: bool,
    pub last_check: Instant,
    pub consecutive_failures: u32,
    pub circuit_breaker_state: CircuitState,
}

#[derive(Debug, Clone, Default)]
pub struct TargetMetrics {
    pub active_connections: u32,
    pub requests_per_second: f64,
    pub latency_p50: Duration,
    pub latency_p99: Duration,
    pub error_rate: f64,
    pub last_updated: Instant,
}
```

### Power of Two Choices Implementation
```rust
pub struct PowerOfTwoBalancer {
    targets: Arc<RwLock<Vec<LoadBalancerTarget>>>,
    rng: Arc<Mutex<SmallRng>>,
    config: PowerOfTwoConfig,
}

#[derive(Debug, Clone)]
pub struct PowerOfTwoConfig {
    pub selection_metric: SelectionMetric,
    pub locality_preference: f64, // 0.0 to 1.0
    pub max_load_difference: f64,
}

#[derive(Debug, Clone)]
pub enum SelectionMetric {
    ActiveConnections,
    RequestRate,
    ResponseTime,
    CompositeScore,
}

impl PowerOfTwoBalancer {
    pub async fn select_target_with_locality(
        &self,
        client_zone: &str,
    ) -> Result<LoadBalancerTarget, LoadBalancerError> {
        let targets = self.targets.read().await;
        
        if targets.is_empty() {
            return Err(LoadBalancerError::NoTargetsAvailable);
        }
        
        // Filter healthy targets
        let healthy_targets: Vec<_> = targets
            .iter()
            .filter(|t| {
                let health = t.health.try_read().unwrap();
                health.healthy && 
                matches!(health.circuit_breaker_state, CircuitState::Closed | CircuitState::HalfOpen)
            })
            .collect();
        
        if healthy_targets.len() < 2 {
            return healthy_targets
                .first()
                .cloned()
                .ok_or(LoadBalancerError::NoHealthyTargets);
        }
        
        // Select two random candidates
        let mut rng = self.rng.lock().await;
        let idx1 = rng.gen_range(0..healthy_targets.len());
        let mut idx2 = rng.gen_range(0..healthy_targets.len());
        while idx2 == idx1 {
            idx2 = rng.gen_range(0..healthy_targets.len());
        }
        
        let target1 = &healthy_targets[idx1];
        let target2 = &healthy_targets[idx2];
        
        // Score based on load and locality
        let score1 = self.calculate_score(target1, client_zone).await;
        let score2 = self.calculate_score(target2, client_zone).await;
        
        if score1 <= score2 {
            Ok(target1.clone())
        } else {
            Ok(target2.clone())
        }
    }
    
    async fn calculate_score(
        &self,
        target: &LoadBalancerTarget,
        client_zone: &str,
    ) -> f64 {
        let metrics = target.metrics.read().await;
        
        // Base score from load metric
        let load_score = match self.config.selection_metric {
            SelectionMetric::ActiveConnections => {
                metrics.active_connections as f64
            }
            SelectionMetric::RequestRate => {
                metrics.requests_per_second
            }
            SelectionMetric::ResponseTime => {
                metrics.latency_p99.as_secs_f64() * 1000.0
            }
            SelectionMetric::CompositeScore => {
                let connection_factor = metrics.active_connections as f64 / 100.0;
                let latency_factor = metrics.latency_p99.as_secs_f64();
                let error_factor = metrics.error_rate * 10.0;
                connection_factor + latency_factor + error_factor
            }
        };
        
        // Apply locality preference
        let locality_multiplier = if target.metadata.zone == client_zone {
            1.0 - self.config.locality_preference
        } else {
            1.0
        };
        
        load_score * locality_multiplier
    }
}
```

### Consistent Hashing Implementation
```rust
use std::collections::BTreeMap;
use std::hash::{Hash, Hasher};
use xxhash_rust::xxh3::Xxh3;

pub struct ConsistentHashBalancer {
    ring: Arc<RwLock<BTreeMap<u64, LoadBalancerTarget>>>,
    virtual_nodes: u32,
    hasher: Xxh3,
}

impl ConsistentHashBalancer {
    pub fn new(virtual_nodes: u32) -> Self {
        Self {
            ring: Arc::new(RwLock::new(BTreeMap::new())),
            virtual_nodes,
            hasher: Xxh3::new(),
        }
    }
    
    pub async fn add_target(&self, target: LoadBalancerTarget) {
        let mut ring = self.ring.write().await;
        
        // Add virtual nodes for better distribution
        for i in 0..self.virtual_nodes {
            let key = format!("{}-{}", target.id, i);
            let hash = self.hash_key(&key);
            ring.insert(hash, target.clone());
        }
    }
    
    pub async fn remove_target(&self, target_id: &str) {
        let mut ring = self.ring.write().await;
        
        // Remove all virtual nodes
        let keys_to_remove: Vec<_> = ring
            .iter()
            .filter(|(_, t)| t.id == target_id)
            .map(|(k, _)| *k)
            .collect();
        
        for key in keys_to_remove {
            ring.remove(&key);
        }
    }
    
    pub async fn get_target(&self, key: &str) -> Result<LoadBalancerTarget, LoadBalancerError> {
        let ring = self.ring.read().await;
        
        if ring.is_empty() {
            return Err(LoadBalancerError::NoTargetsAvailable);
        }
        
        let hash = self.hash_key(key);
        
        // Find the first target with hash >= key hash
        let target = ring
            .range(hash..)
            .next()
            .or_else(|| ring.iter().next()) // Wrap around
            .map(|(_, target)| target.clone())
            .ok_or(LoadBalancerError::NoTargetsAvailable)?;
        
        // Check if target is healthy
        let health = target.health.read().await;
        if !health.healthy {
            // Find next healthy target
            self.find_next_healthy_target(&ring, hash).await
        } else {
            Ok(target)
        }
    }
    
    fn hash_key(&self, key: &str) -> u64 {
        let mut hasher = self.hasher.clone();
        key.hash(&mut hasher);
        hasher.finish()
    }
}
```

### Weighted Round Robin Implementation
```rust
pub struct WeightedRoundRobinBalancer {
    targets: Arc<RwLock<Vec<WeightedTarget>>>,
    current_weights: Arc<RwLock<Vec<i32>>>,
    position: Arc<AtomicUsize>,
}

#[derive(Debug, Clone)]
struct WeightedTarget {
    target: LoadBalancerTarget,
    weight: u32,
    effective_weight: i32,
}

impl WeightedRoundRobinBalancer {
    pub async fn select_target(&self) -> Result<LoadBalancerTarget, LoadBalancerError> {
        let mut targets = self.targets.write().await;
        let mut current_weights = self.current_weights.write().await;
        
        if targets.is_empty() {
            return Err(LoadBalancerError::NoTargetsAvailable);
        }
        
        let total_weight: i32 = targets.iter()
            .map(|t| t.effective_weight)
            .sum();
        
        if total_weight <= 0 {
            return Err(LoadBalancerError::NoHealthyTargets);
        }
        
        // Smooth weighted round-robin algorithm
        let mut best_idx = 0;
        let mut max_current_weight = i32::MIN;
        
        for (idx, weighted) in targets.iter_mut().enumerate() {
            // Skip unhealthy targets
            let health = weighted.target.health.read().await;
            if !health.healthy {
                weighted.effective_weight = 0;
                current_weights[idx] = 0;
                continue;
            }
            
            // Update current weight
            current_weights[idx] += weighted.effective_weight;
            
            if current_weights[idx] > max_current_weight {
                max_current_weight = current_weights[idx];
                best_idx = idx;
            }
        }
        
        // Decrease the selected target's current weight
        current_weights[best_idx] -= total_weight;
        
        Ok(targets[best_idx].target.clone())
    }
}
```

### Least Connections Implementation
```rust
pub struct LeastConnectionsBalancer {
    targets: Arc<RwLock<Vec<LoadBalancerTarget>>>,
    connection_counts: Arc<DashMap<String, AtomicU32>>,
}

impl LeastConnectionsBalancer {
    pub async fn select_target(&self) -> Result<LoadBalancerTarget, LoadBalancerError> {
        let targets = self.targets.read().await;
        
        let mut best_target = None;
        let mut min_connections = u32::MAX;
        
        for target in targets.iter() {
            // Check health
            let health = target.health.read().await;
            if !health.healthy {
                continue;
            }
            
            // Get connection count
            let count = self.connection_counts
                .get(&target.id)
                .map(|c| c.load(Ordering::Relaxed))
                .unwrap_or(0);
            
            if count < min_connections {
                min_connections = count;
                best_target = Some(target.clone());
            }
        }
        
        best_target.ok_or(LoadBalancerError::NoHealthyTargets)
    }
    
    pub fn acquire_connection(&self, target_id: &str) {
        self.connection_counts
            .entry(target_id.to_string())
            .or_insert_with(|| AtomicU32::new(0))
            .fetch_add(1, Ordering::Relaxed);
    }
    
    pub fn release_connection(&self, target_id: &str) {
        if let Some(count) = self.connection_counts.get(target_id) {
            count.fetch_sub(1, Ordering::Relaxed);
        }
    }
}
```

### Service Discovery Integration
```rust
#[async_trait]
pub trait ServiceDiscovery: Send + Sync {
    async fn discover_targets(
        &self,
        service_name: &str,
    ) -> Result<Vec<LoadBalancerTarget>, DiscoveryError>;
    
    async fn watch_targets(
        &self,
        service_name: &str,
    ) -> Result<TargetWatcher, DiscoveryError>;
}

// Example: Consul integration
pub struct ConsulServiceDiscovery {
    client: ConsulClient,
    health_check_interval: Duration,
}

#[async_trait]
impl ServiceDiscovery for ConsulServiceDiscovery {
    async fn discover_targets(
        &self,
        service_name: &str,
    ) -> Result<Vec<LoadBalancerTarget>, DiscoveryError> {
        let services = self.client
            .health()
            .service(service_name, true)
            .await?;
        
        Ok(services
            .into_iter()
            .map(|s| self.consul_to_target(s))
            .collect())
    }
}
```

## Performance Optimizations

### Lock-Free Target Selection
```rust
use crossbeam::epoch;

pub struct LockFreeBalancer {
    targets: epoch::Atomic<Vec<LoadBalancerTarget>>,
    strategy: AtomicU8,
}
```

### Connection Pooling Integration
```rust
pub struct PooledLoadBalancer<LB: LoadBalancer> {
    balancer: LB,
    pools: DashMap<String, ConnectionPool>,
}

impl<LB: LoadBalancer> PooledLoadBalancer<LB> {
    pub async fn execute_with_balancing<F, R>(
        &self,
        operation: F,
    ) -> Result<R, Error>
    where
        F: FnOnce(Connection) -> Future<Output = Result<R, Error>>,
    {
        let target = self.balancer.select_target(&()).await?;
        let pool = self.get_or_create_pool(&target).await;
        let conn = pool.acquire().await?;
        
        let result = operation(conn).await;
        
        self.balancer.report_result(
            &target,
            RequestResult::from_result(&result),
        ).await;
        
        result
    }
}
```