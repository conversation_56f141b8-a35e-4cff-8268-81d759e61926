# Rate Limiting Implementation Details

## Overview
The RUST-SS rate limiter provides flexible, high-performance request throttling with support for multiple algorithms and distributed coordination.

## Core Architecture

### Rate Limiter Trait
```rust
use async_trait::async_trait;
use std::time::Duration;

#[async_trait]
pub trait RateLimiter: Send + Sync {
    async fn check_limit(&self, key: &str, cost: u32) -> Result<Decision, Error>;
    async fn check_and_update(&self, key: &str, cost: u32) -> Result<Decision, Error>;
    async fn get_quota(&self, key: &str) -> Result<Quota, Error>;
    async fn reset(&self, key: &str) -> Result<(), Error>;
}

#[derive(Debug, Clone)]
pub struct Decision {
    pub allowed: bool,
    pub remaining: u32,
    pub reset_at: Instant,
    pub retry_after: Option<Duration>,
}

#[derive(Debug, <PERSON>lone)]
pub struct Quota {
    pub limit: u32,
    pub remaining: u32,
    pub reset_at: Instant,
    pub period: Duration,
}
```

### Token Bucket Implementation
```rust
use std::sync::atomic::{AtomicU64, Ordering};
use parking_lot::RwLock;

pub struct TokenBucket {
    capacity: u32,
    refill_rate: f64,
    tokens: Arc<AtomicU64>,
    last_refill: Arc<RwLock<Instant>>,
}

impl TokenBucket {
    pub fn new(capacity: u32, refill_per_second: u32) -> Self {
        Self {
            capacity,
            refill_rate: refill_per_second as f64,
            tokens: Arc::new(AtomicU64::new(capacity as u64)),
            last_refill: Arc::new(RwLock::new(Instant::now())),
        }
    }
    
    pub async fn try_acquire(&self, tokens: u32) -> Result<Decision, Error> {
        // Refill tokens based on elapsed time
        self.refill_tokens();
        
        let requested = tokens as u64;
        let mut current = self.tokens.load(Ordering::Acquire);
        
        loop {
            if current >= requested {
                // Try to acquire tokens
                match self.tokens.compare_exchange_weak(
                    current,
                    current - requested,
                    Ordering::Release,
                    Ordering::Acquire,
                ) {
                    Ok(_) => {
                        return Ok(Decision {
                            allowed: true,
                            remaining: (current - requested) as u32,
                            reset_at: self.next_refill_time(),
                            retry_after: None,
                        });
                    }
                    Err(actual) => current = actual,
                }
            } else {
                // Not enough tokens
                let tokens_needed = requested - current;
                let wait_time = Duration::from_secs_f64(
                    tokens_needed as f64 / self.refill_rate
                );
                
                return Ok(Decision {
                    allowed: false,
                    remaining: current as u32,
                    reset_at: self.next_refill_time(),
                    retry_after: Some(wait_time),
                });
            }
        }
    }
    
    fn refill_tokens(&self) {
        let now = Instant::now();
        let mut last_refill = self.last_refill.write();
        
        let elapsed = now.duration_since(*last_refill).as_secs_f64();
        let tokens_to_add = (elapsed * self.refill_rate) as u64;
        
        if tokens_to_add > 0 {
            *last_refill = now;
            
            // Add tokens up to capacity
            let mut current = self.tokens.load(Ordering::Acquire);
            loop {
                let new_tokens = (current + tokens_to_add).min(self.capacity as u64);
                match self.tokens.compare_exchange_weak(
                    current,
                    new_tokens,
                    Ordering::Release,
                    Ordering::Acquire,
                ) {
                    Ok(_) => break,
                    Err(actual) => current = actual,
                }
            }
        }
    }
}
```

### Sliding Window Implementation
```rust
use dashmap::DashMap;
use std::collections::VecDeque;

pub struct SlidingWindowLimiter {
    window_size: Duration,
    max_requests: u32,
    precision: Duration,
    buckets: Arc<DashMap<String, WindowState>>,
}

#[derive(Debug)]
struct WindowState {
    requests: VecDeque<RequestRecord>,
    total_count: u32,
}

#[derive(Debug, Clone)]
struct RequestRecord {
    timestamp: Instant,
    count: u32,
}

impl SlidingWindowLimiter {
    pub async fn check_and_update(&self, key: &str, count: u32) -> Result<Decision, Error> {
        let now = Instant::now();
        let window_start = now - self.window_size;
        
        let mut state = self.buckets
            .entry(key.to_string())
            .or_insert_with(|| WindowState {
                requests: VecDeque::new(),
                total_count: 0,
            });
        
        // Remove expired records
        while let Some(front) = state.requests.front() {
            if front.timestamp < window_start {
                state.total_count -= front.count;
                state.requests.pop_front();
            } else {
                break;
            }
        }
        
        // Check if request would exceed limit
        if state.total_count + count > self.max_requests {
            let oldest = state.requests.front()
                .map(|r| r.timestamp + self.window_size)
                .unwrap_or(now + self.window_size);
            
            return Ok(Decision {
                allowed: false,
                remaining: self.max_requests.saturating_sub(state.total_count),
                reset_at: oldest,
                retry_after: Some(oldest.duration_since(now)),
            });
        }
        
        // Add new request
        state.requests.push_back(RequestRecord {
            timestamp: now,
            count,
        });
        state.total_count += count;
        
        Ok(Decision {
            allowed: true,
            remaining: self.max_requests - state.total_count,
            reset_at: now + self.window_size,
            retry_after: None,
        })
    }
}
```

### Distributed Rate Limiter
```rust
use redis::aio::ConnectionManager;
use redis::Script;

pub struct DistributedRateLimiter {
    redis: ConnectionManager,
    local_cache: Arc<DashMap<String, CachedDecision>>,
    config: DistributedConfig,
}

#[derive(Debug, Clone)]
struct CachedDecision {
    decision: Decision,
    cached_at: Instant,
    ttl: Duration,
}

#[derive(Debug, Clone)]
pub struct DistributedConfig {
    pub cache_positive_ttl: Duration,
    pub cache_negative_ttl: Duration,
    pub sync_interval: Duration,
    pub local_quota_percent: f64,
}

// Lua script for atomic rate limit check
const RATE_LIMIT_SCRIPT: &str = r#"
    local key = KEYS[1]
    local limit = tonumber(ARGV[1])
    local window = tonumber(ARGV[2])
    local cost = tonumber(ARGV[3])
    local now = tonumber(ARGV[4])
    
    -- Get current count
    local current = redis.call('GET', key)
    if current == false then
        current = 0
    else
        current = tonumber(current)
    end
    
    -- Check limit
    if current + cost > limit then
        return {0, limit - current, redis.call('TTL', key)}
    end
    
    -- Update count
    local new_count = current + cost
    redis.call('SET', key, new_count, 'EX', window)
    
    return {1, limit - new_count, window}
"#;

impl DistributedRateLimiter {
    pub async fn check_distributed(
        &self,
        key: &str,
        cost: u32,
        limit: u32,
        window: Duration,
    ) -> Result<Decision, Error> {
        // Check local cache first
        if let Some(cached) = self.get_cached_decision(key).await {
            return Ok(cached);
        }
        
        // Execute Lua script
        let script = Script::new(RATE_LIMIT_SCRIPT);
        let result: Vec<i64> = script
            .key(key)
            .arg(limit)
            .arg(window.as_secs())
            .arg(cost)
            .arg(Instant::now().elapsed().as_secs())
            .invoke_async(&mut self.redis.clone())
            .await?;
        
        let decision = Decision {
            allowed: result[0] == 1,
            remaining: result[1] as u32,
            reset_at: Instant::now() + Duration::from_secs(result[2] as u64),
            retry_after: if result[0] == 0 {
                Some(Duration::from_secs(result[2] as u64))
            } else {
                None
            },
        };
        
        // Cache decision
        self.cache_decision(key, decision.clone()).await;
        
        Ok(decision)
    }
}
```

### Hierarchical Rate Limiter
```rust
pub struct HierarchicalRateLimiter {
    limiters: Vec<(String, Box<dyn RateLimiter>)>,
    strategy: HierarchyStrategy,
}

#[derive(Debug, Clone)]
pub enum HierarchyStrategy {
    // All limits must pass
    AllMustPass,
    // Stop at first rejection
    FailFast,
    // Use most restrictive
    MostRestrictive,
}

impl HierarchicalRateLimiter {
    pub async fn check_hierarchy(
        &self,
        key: &str,
        cost: u32,
    ) -> Result<Decision, Error> {
        match self.strategy {
            HierarchyStrategy::AllMustPass => {
                let mut most_restrictive = None;
                
                for (name, limiter) in &self.limiters {
                    let decision = limiter.check_limit(key, cost).await?;
                    
                    if !decision.allowed {
                        return Ok(decision);
                    }
                    
                    // Track most restrictive remaining quota
                    match &mut most_restrictive {
                        None => most_restrictive = Some(decision),
                        Some(current) => {
                            if decision.remaining < current.remaining {
                                most_restrictive = Some(decision);
                            }
                        }
                    }
                }
                
                Ok(most_restrictive.unwrap_or_else(|| Decision {
                    allowed: true,
                    remaining: u32::MAX,
                    reset_at: Instant::now(),
                    retry_after: None,
                }))
            }
            // Other strategies...
        }
    }
}
```

### Adaptive Rate Limiter
```rust
pub struct AdaptiveRateLimiter {
    base_limiter: Box<dyn RateLimiter>,
    metrics: Arc<SystemMetrics>,
    config: AdaptiveConfig,
}

#[derive(Debug, Clone)]
pub struct AdaptiveConfig {
    pub min_rate: u32,
    pub max_rate: u32,
    pub target_latency: Duration,
    pub target_error_rate: f64,
    pub adjustment_interval: Duration,
}

impl AdaptiveRateLimiter {
    pub async fn adjust_limits(&self) {
        let metrics = self.metrics.get_current().await;
        
        let mut adjustment_factor = 1.0;
        
        // Adjust based on latency
        if metrics.p99_latency > self.config.target_latency {
            adjustment_factor *= 0.9; // Reduce rate
        } else if metrics.p99_latency < self.config.target_latency * 0.8 {
            adjustment_factor *= 1.1; // Increase rate
        }
        
        // Adjust based on error rate
        if metrics.error_rate > self.config.target_error_rate {
            adjustment_factor *= 0.85; // Reduce rate more aggressively
        }
        
        // Apply adjustment
        let current_rate = self.get_current_rate().await;
        let new_rate = (current_rate as f64 * adjustment_factor) as u32;
        let new_rate = new_rate.clamp(self.config.min_rate, self.config.max_rate);
        
        self.update_rate(new_rate).await;
    }
}
```

## Performance Optimizations

### Zero-Allocation Checks
```rust
// Stack-allocated decision for hot path
#[repr(C)]
pub struct StackDecision {
    allowed: bool,
    remaining: u32,
    reset_at_secs: u64,
    retry_after_millis: u32,
}

impl StackDecision {
    #[inline(always)]
    pub fn check_fast(limiter: &AtomicLimiter, cost: u32) -> Self {
        // All operations on stack, no heap allocations
        let state = limiter.load_state();
        let allowed = state.can_acquire(cost);
        
        StackDecision {
            allowed,
            remaining: state.remaining(),
            reset_at_secs: state.reset_time(),
            retry_after_millis: if allowed { 0 } else { state.wait_millis() },
        }
    }
}
```

### SIMD Batch Checking
```rust
use std::simd::*;

pub fn batch_check_limits(
    keys: &[u64],
    costs: &[u32],
    limits: &[u32],
) -> Vec<bool> {
    // Process 8 requests at once
    let mut results = vec![false; keys.len()];
    
    for (chunk_idx, (key_chunk, cost_chunk, limit_chunk)) in 
        keys.chunks(8)
            .zip(costs.chunks(8))
            .zip(limits.chunks(8))
            .enumerate() 
    {
        // SIMD operations for parallel checking
        let costs_vec = u32x8::from_slice(cost_chunk);
        let limits_vec = u32x8::from_slice(limit_chunk);
        
        // Check if costs are within limits
        let mask = costs_vec.simd_le(limits_vec);
        
        // Store results
        for (i, allowed) in mask.to_array().iter().enumerate() {
            results[chunk_idx * 8 + i] = *allowed;
        }
    }
    
    results
}
```