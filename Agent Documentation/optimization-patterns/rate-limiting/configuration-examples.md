# Rate Limiting Configuration Examples

## Basic Configuration

### Simple Rate Limiting
```yaml
# config/rate_limiter_simple.yaml
rate_limiter:
  algorithm: "token_bucket"
  limits:
    default:
      rate: 100           # requests per second
      burst: 200         # burst capacity
      
  # Response when rate limited
  rejection:
    status_code: 429
    message: "Rate limit exceeded"
    include_headers: true  # Add X-RateLimit headers
```

### Per-Agent Rate Limiting
```yaml
# config/rate_limiter_per_agent.yaml
rate_limiter:
  algorithm: "sliding_window"
  
  # Default limits for all agents
  default_limits:
    requests_per_minute: 1000
    requests_per_hour: 50000
    
  # Agent-specific overrides
  agent_limits:
    "premium-agent-*":
      requests_per_minute: 10000
      requests_per_hour: 500000
      
    "basic-agent-*":
      requests_per_minute: 100
      requests_per_hour: 5000
      
    "internal-system-*":
      # No limits for internal agents
      unlimited: true
```

## Advanced Algorithms

### Token Bucket Configuration
```yaml
# config/rate_limiter_token_bucket.yaml
token_bucket:
  # Bucket configuration
  capacity: 1000          # Maximum tokens
  refill_rate: 100       # Tokens per second
  refill_interval: 10ms  # How often to add tokens
  
  # Initial state
  initial_tokens: 500    # Start half full
  
  # Cost per operation
  operation_costs:
    read: 1
    write: 10
    batch_read: 5
    batch_write: 50
    admin_operation: 100
```

### Sliding Window Log
```yaml
# config/rate_limiter_sliding_window.yaml
sliding_window:
  window_size: 60s       # 1 minute window
  precision: 1s          # Bucket granularity
  
  # Multiple windows
  windows:
    - name: "burst"
      size: 1s
      limit: 100
      
    - name: "sustained"
      size: 60s
      limit: 3000
      
    - name: "daily"
      size: 86400s
      limit: 1000000
      
  # Memory optimization
  max_tracked_keys: 100000
  cleanup_interval: 5m
```

### Leaky Bucket
```yaml
# config/rate_limiter_leaky_bucket.yaml
leaky_bucket:
  capacity: 1000         # Queue size
  leak_rate: 50         # Requests per second
  
  # Queue behavior
  queue:
    enabled: true
    max_wait_time: 5s
    priority_based: true
    
  # Priority levels
  priorities:
    critical: 1.0      # Process immediately
    high: 0.8
    normal: 0.5
    low: 0.2
```

## Distributed Rate Limiting

### Redis-Based Configuration
```yaml
# config/rate_limiter_redis.yaml
rate_limiter:
  backend: "redis"
  
  redis:
    # Cluster configuration
    mode: "cluster"
    nodes:
      - "redis-1:6379"
      - "redis-2:6379"
      - "redis-3:6379"
      
    # Connection settings
    connection_pool:
      max_connections: 100
      min_idle: 10
      timeout: 1s
      
    # Key configuration
    key_prefix: "rate_limit:"
    key_expiration: 3600s
    
  # Sync settings
  synchronization:
    mode: "eventual"      # or "strong"
    sync_interval: 100ms
    
  # Local cache
  local_cache:
    enabled: true
    ttl: 5s
    max_entries: 10000
```

### DynamoDB-Based Configuration
```yaml
# config/rate_limiter_dynamodb.yaml
rate_limiter:
  backend: "dynamodb"
  
  dynamodb:
    table_name: "RateLimits"
    region: "us-east-1"
    
    # Capacity settings
    read_capacity: 1000
    write_capacity: 500
    
    # Auto-scaling
    auto_scaling:
      enabled: true
      min_capacity: 100
      max_capacity: 5000
      target_utilization: 70
      
    # TTL for automatic cleanup
    ttl_attribute: "expires_at"
```

## Hierarchical Rate Limiting

### Multi-Level Limits
```yaml
# config/rate_limiter_hierarchical.yaml
hierarchical_limits:
  # Global limits (top level)
  global:
    requests_per_second: 10000
    concurrent_requests: 1000
    
  # Per-tenant limits
  tenant:
    default:
      requests_per_second: 100
      requests_per_day: 1000000
      
    overrides:
      "enterprise-*":
        requests_per_second: 1000
        requests_per_day: 100000000
        
  # Per-user limits (within tenant)
  user:
    default:
      requests_per_minute: 60
      concurrent_requests: 10
      
  # Per-endpoint limits
  endpoint:
    "/api/v1/compute":
      requests_per_second: 10
      max_payload_size: 10MB
      
    "/api/v1/batch":
      requests_per_minute: 5
      max_batch_size: 1000
      
  # Hierarchy strategy
  strategy: "most_restrictive"  # Apply tightest limit
```

### Resource-Based Limits
```yaml
# config/rate_limiter_resources.yaml
resource_limits:
  # CPU-based limiting
  cpu:
    target_utilization: 80
    check_interval: 1s
    
    thresholds:
      - utilization: 90
        rate_multiplier: 0.5    # Half rate at 90%
      - utilization: 95
        rate_multiplier: 0.1    # 10% rate at 95%
        
  # Memory-based limiting
  memory:
    max_usage_percent: 85
    
  # Custom resource limits
  resources:
    database_connections:
      max: 1000
      per_agent_limit: 10
      
    gpu_compute_units:
      max: 100
      cost_per_request: 5
```

## Adaptive Rate Limiting

### ML-Based Configuration
```yaml
# config/rate_limiter_adaptive.yaml
adaptive_limiter:
  # Base configuration
  base_rate: 100
  min_rate: 10
  max_rate: 1000
  
  # Learning parameters
  learning:
    algorithm: "gradient_descent"
    learning_rate: 0.01
    update_interval: 60s
    
  # Metrics to consider
  optimization_metrics:
    - name: "response_time_p99"
      target: 100ms
      weight: 0.4
      
    - name: "error_rate"
      target: 0.001
      weight: 0.3
      
    - name: "throughput"
      target: 5000
      weight: 0.3
      
  # Safety constraints
  constraints:
    max_adjustment_per_interval: 0.2  # 20% max change
    cooldown_period: 300s             # 5 min between major changes
```

### Time-Based Patterns
```yaml
# config/rate_limiter_time_patterns.yaml
time_based_limits:
  # Different limits by time of day
  schedules:
    business_hours:
      days: ["Mon", "Tue", "Wed", "Thu", "Fri"]
      hours: "09:00-17:00"
      timezone: "America/New_York"
      rate_multiplier: 1.0
      
    after_hours:
      days: ["Mon", "Tue", "Wed", "Thu", "Fri"]
      hours: "17:00-09:00"
      timezone: "America/New_York"
      rate_multiplier: 2.0  # Double rate after hours
      
    weekends:
      days: ["Sat", "Sun"]
      rate_multiplier: 3.0  # Triple rate on weekends
      
  # Special dates
  overrides:
    - date: "2024-12-25"
      rate_multiplier: 5.0  # 5x on Christmas
      
    - date_range: "2024-11-25:2024-11-30"
      rate_multiplier: 0.5  # Half rate during Black Friday
```

## Integration Examples

### With Load Balancer
```yaml
# config/rate_limiter_load_balanced.yaml
integration:
  load_balancer:
    # Share rate limits across instances
    shared_limits: true
    
    # Distribute quota
    quota_distribution:
      mode: "fair_share"      # Equal distribution
      rebalance_interval: 10s
      
    # Handle at edge
    edge_limiting:
      enabled: true
      cache_decisions: 1s
```

### With Circuit Breaker
```yaml
# config/rate_limiter_circuit_breaker.yaml
integration:
  circuit_breaker:
    # Coordinate limits
    coordination:
      # Reduce rate when circuit is half-open
      half_open_rate_multiplier: 0.1
      
      # Increase rate limit on errors
      error_rate_adjustment:
        threshold: 0.05
        reduction_factor: 0.5
        
    # Shared metrics
    shared_metrics:
      - error_rate
      - response_time
      - throughput
```

## Monitoring Configuration

### Metrics and Alerts
```yaml
# config/rate_limiter_monitoring.yaml
monitoring:
  metrics:
    # What to collect
    collect:
      - requests_allowed
      - requests_rejected
      - current_rate
      - quota_usage
      - wait_time_distribution
      
    # Export configuration
    export:
      prometheus:
        enabled: true
        port: 9092
        path: "/metrics"
        
  alerts:
    high_rejection_rate:
      threshold: 0.1  # 10% rejection
      window: 5m
      severity: "warning"
      
    quota_exhaustion:
      threshold: 0.95  # 95% quota used
      severity: "critical"
      
    distributed_sync_failure:
      max_failures: 3
      severity: "error"
```

### Debug Configuration
```yaml
# config/rate_limiter_debug.yaml
debug:
  # Verbose logging
  logging:
    level: "trace"
    include_decisions: true
    include_calculations: true
    
  # Decision explanation
  explain_rejections: true
  
  # Test mode
  test_mode:
    enabled: false
    bypass_limits_for:
      - "test-agent-*"
      - "debug-client-*"
      
  # Simulation
  simulation:
    enabled: false
    multiplier: 10  # 10x faster for testing
```