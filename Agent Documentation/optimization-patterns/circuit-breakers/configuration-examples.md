# Circuit Breaker Configuration Examples

## Basic Configuration

### Agent-to-Agent Communication
```yaml
# config/agent_circuit_breakers.yaml
circuit_breakers:
  # Default settings for all agent communication
  defaults:
    failure_threshold: 5
    failure_rate_threshold: 0.5
    sample_window_seconds: 60
    reset_timeout_seconds: 30
    half_open_max_requests: 3
    half_open_success_threshold: 2
    
  # Specific configurations per agent type
  agent_types:
    data_processor:
      failure_threshold: 10        # More tolerant for batch processing
      reset_timeout_seconds: 60    # Longer recovery time
      latency_threshold_ms: 5000   # 5 second timeout
      
    real_time_analyzer:
      failure_threshold: 3         # Strict for real-time
      reset_timeout_seconds: 10    # Quick recovery attempts
      latency_threshold_ms: 100    # 100ms max latency
      
    coordinator:
      failure_threshold: 20        # Very tolerant
      failure_rate_threshold: 0.8  # Allow higher failure rate
      reset_timeout_seconds: 5     # Rapid recovery
```

### External Service Integration
```yaml
# config/external_services.yaml
external_services:
  database:
    primary:
      breaker:
        failure_threshold: 3
        reset_timeout_seconds: 60
        error_classification:
          critical:
            - "connection_refused"
            - "authentication_failed"
          recoverable:
            - "timeout"
            - "connection_reset"
          transient:
            - "duplicate_key"
            - "constraint_violation"
            
    read_replica:
      breaker:
        failure_threshold: 5
        reset_timeout_seconds: 30
        # Read replicas can tolerate more failures
        failure_rate_threshold: 0.7
        
  cache:
    redis_cluster:
      breaker:
        failure_threshold: 10
        reset_timeout_seconds: 10
        # Cache can fail gracefully
        half_open_max_requests: 10
```

## Advanced Configurations

### Hierarchical Circuit Breakers
```toml
# config/breakers.toml

[breakers.global]
# Global breaker for entire service
failure_threshold = 100
failure_rate_threshold = 0.95
sample_window_seconds = 300
reset_timeout_seconds = 120

[breakers.per_endpoint]
# Endpoint-specific breakers
[breakers.per_endpoint."/api/v1/process"]
failure_threshold = 20
latency_threshold_ms = 1000
priority = "high"

[breakers.per_endpoint."/api/v1/batch"]
failure_threshold = 50
latency_threshold_ms = 30000
priority = "low"

[breakers.per_client]
# Client-specific configurations
[breakers.per_client."agent-*"]
failure_threshold = 10
half_open_success_threshold = 3

[breakers.per_client."external-*"]
failure_threshold = 5
reset_timeout_seconds = 60
```

### Environment-Specific Settings
```yaml
# config/env/development.yaml
circuit_breakers:
  # More lenient in development
  global_defaults:
    failure_threshold: 20
    reset_timeout_seconds: 5
    log_level: "debug"
    metrics_enabled: true
    
---
# config/env/staging.yaml
circuit_breakers:
  # Balanced for testing
  global_defaults:
    failure_threshold: 10
    reset_timeout_seconds: 15
    log_level: "info"
    metrics_enabled: true
    
---
# config/env/production.yaml
circuit_breakers:
  # Strict for production
  global_defaults:
    failure_threshold: 5
    reset_timeout_seconds: 30
    log_level: "warn"
    metrics_enabled: true
    distributed_state_sync: true
```

## Dynamic Configuration

### Runtime Adjustments
```rust
// Dynamic configuration via control plane
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DynamicBreakerConfig {
    pub target: String,
    pub updates: ConfigUpdates,
    pub effective_after: Option<DateTime<Utc>>,
    pub expires_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigUpdates {
    pub failure_threshold: Option<u32>,
    pub reset_timeout: Option<Duration>,
    pub state_override: Option<StateOverride>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StateOverride {
    ForceOpen { reason: String },
    ForceClosed { reason: String },
    Reset,
}

// Example dynamic update
let update = DynamicBreakerConfig {
    target: "payment-service".to_string(),
    updates: ConfigUpdates {
        failure_threshold: Some(1), // Very strict during incident
        reset_timeout: Some(Duration::from_secs(300)), // 5 minutes
        state_override: None,
    },
    effective_after: Some(Utc::now()),
    expires_at: Some(Utc::now() + Duration::from_hours(2)),
};
```

### Feature Flags Integration
```yaml
# Feature flag controlled breakers
feature_flags:
  circuit_breakers:
    experimental_latency_breaker:
      enabled: true
      rollout_percentage: 10
      config:
        latency_p99_threshold_ms: 500
        latency_p95_threshold_ms: 200
        
    advanced_error_classification:
      enabled: false
      config:
        ml_model_path: "/models/error_classifier_v2.onnx"
        fallback_to_rules: true
```

## Distributed Configuration

### Consensus-Based Settings
```yaml
# Distributed breaker configuration
distributed_breakers:
  consensus:
    # Require 3 out of 5 agents to agree on state
    min_agents_for_consensus: 3
    total_agents_in_group: 5
    
  state_sync:
    # How often to sync state
    sync_interval_seconds: 10
    # State store backend
    backend: "etcd"
    backend_config:
      endpoints:
        - "etcd1:2379"
        - "etcd2:2379"
        - "etcd3:2379"
      
  global_rules:
    # If any region reports >90% failure, open globally
    cross_region_failure_threshold: 0.9
    # Number of regions that must report
    min_reporting_regions: 2
```

### Multi-Tenant Configuration
```yaml
# Per-tenant circuit breaker settings
tenants:
  tenant_a:
    breakers:
      # Generous limits for premium tenant
      failure_threshold: 20
      reset_timeout_seconds: 10
      rate_limit_integration:
        burst_allowance_multiplier: 2.0
        
  tenant_b:
    breakers:
      # Standard limits
      failure_threshold: 10
      reset_timeout_seconds: 30
      rate_limit_integration:
        burst_allowance_multiplier: 1.0
        
  default:
    breakers:
      # Conservative defaults
      failure_threshold: 5
      reset_timeout_seconds: 60
      rate_limit_integration:
        burst_allowance_multiplier: 0.5
```

## Monitoring Configuration

### Metrics and Alerting
```yaml
# Monitoring configuration
monitoring:
  circuit_breakers:
    metrics:
      # Prometheus metrics
      enabled: true
      port: 9090
      path: "/metrics"
      
      # Detailed metrics per breaker
      detailed_metrics:
        - state_transitions
        - request_outcomes
        - latency_percentiles
        - error_classifications
        
    alerts:
      # Alert when breaker opens
      breaker_open:
        enabled: true
        channels: ["slack", "pagerduty"]
        severity: "warning"
        
      # Critical alert for multiple breakers open
      multiple_breakers_open:
        threshold: 3
        window_minutes: 5
        channels: ["pagerduty"]
        severity: "critical"
        
      # Alert on high failure rate
      high_failure_rate:
        threshold: 0.5
        duration_minutes: 10
        channels: ["slack"]
        severity: "warning"
```

## Integration Examples

### With Rate Limiter
```yaml
# Coordinated resilience patterns
resilience:
  circuit_breaker:
    failure_threshold: 5
    reset_timeout_seconds: 30
    
  rate_limiter:
    requests_per_second: 100
    burst_size: 200
    
  coordination:
    # Reduce rate limit when breaker is half-open
    half_open_rate_reduction: 0.5
    # Increase breaker sensitivity when rate limited
    rate_limited_failure_weight: 2.0
```

### With Load Balancer
```yaml
# Load balancer aware configuration
load_balancing:
  strategy: "power_of_two"
  
  circuit_breaker_integration:
    # Remove target from pool when breaker opens
    exclude_open_breakers: true
    # Reduce weight during half-open
    half_open_weight_multiplier: 0.3
    # Probe interval for closed breakers
    health_check_interval_seconds: 30
```