# Circuit Breaker Implementation Details

## Overview
The RUST-SS circuit breaker is a high-performance, async-native resilience pattern that prevents cascading failures in the multi-agent system by detecting unhealthy services and failing fast.

## Core Architecture

### State Machine Implementation
```rust
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use thiserror::Error;

#[derive(Debug, <PERSON>lone)]
pub enum CircuitState {
    Closed {
        failure_count: u32,
        consecutive_successes: u32,
        last_failure_time: Option<Instant>,
    },
    Open {
        opened_at: Instant,
        failure_reason: FailureReason,
        retry_after: Instant,
    },
    HalfOpen {
        success_count: u32,
        test_start: Instant,
        pending_requests: u32,
    },
}

#[derive(Debug, Clone)]
pub struct CircuitBreaker {
    state: Arc<RwLock<CircuitState>>,
    config: Arc<BreakerConfig>,
    metrics: Arc<BreakerMetrics>,
}

#[derive(Debug, <PERSON>lone)]
pub struct BreakerConfig {
    // Failure detection
    pub failure_threshold: u32,
    pub failure_rate_threshold: f64,
    pub sample_window: Duration,
    
    // Recovery settings
    pub reset_timeout: Duration,
    pub half_open_max_requests: u32,
    pub half_open_success_threshold: u32,
    
    // Advanced settings
    pub latency_threshold: Option<Duration>,
    pub error_classifier: Arc<dyn ErrorClassifier>,
}
```

### Lock-Free Fast Path
```rust
impl CircuitBreaker {
    // Fast path for checking if requests should be allowed
    pub async fn should_allow(&self) -> Result<RequestPermit, CircuitOpenError> {
        // Try read lock first for fast path
        let state = self.state.read().await;
        
        match &*state {
            CircuitState::Closed { .. } => {
                drop(state);
                Ok(RequestPermit::new(self.clone()))
            }
            CircuitState::Open { retry_after, .. } => {
                if Instant::now() >= *retry_after {
                    // Transition to half-open
                    drop(state);
                    self.try_transition_to_half_open().await
                } else {
                    Err(CircuitOpenError::StillOpen {
                        retry_after: *retry_after,
                    })
                }
            }
            CircuitState::HalfOpen { pending_requests, .. } => {
                if *pending_requests < self.config.half_open_max_requests {
                    drop(state);
                    self.acquire_half_open_permit().await
                } else {
                    Err(CircuitOpenError::HalfOpenLimitReached)
                }
            }
        }
    }
}
```

### Request Permit System
```rust
// RAII guard for tracking request lifecycle
pub struct RequestPermit {
    breaker: CircuitBreaker,
    start_time: Instant,
    recorded: bool,
}

impl RequestPermit {
    pub async fn record_success(mut self) {
        self.recorded = true;
        let duration = self.start_time.elapsed();
        self.breaker.on_success(duration).await;
    }
    
    pub async fn record_failure(mut self, error: &dyn std::error::Error) {
        self.recorded = true;
        let duration = self.start_time.elapsed();
        self.breaker.on_failure(error, duration).await;
    }
}

impl Drop for RequestPermit {
    fn drop(&mut self) {
        if !self.recorded {
            // Request was cancelled/panicked - record as failure
            let duration = self.start_time.elapsed();
            let breaker = self.breaker.clone();
            tokio::spawn(async move {
                breaker.on_failure(
                    &CircuitBreakerError::RequestDropped,
                    duration
                ).await;
            });
        }
    }
}
```

### Advanced Failure Detection
```rust
// Sliding window for failure rate calculation
pub struct FailureRateTracker {
    window_size: Duration,
    buckets: Vec<FailureBucket>,
    current_bucket: usize,
}

impl FailureRateTracker {
    pub fn record_result(&mut self, success: bool) {
        let now = Instant::now();
        self.rotate_buckets_if_needed(now);
        
        let bucket = &mut self.buckets[self.current_bucket];
        if success {
            bucket.successes += 1;
        } else {
            bucket.failures += 1;
        }
    }
    
    pub fn failure_rate(&self) -> f64 {
        let (total_success, total_failure) = self.buckets.iter()
            .fold((0u64, 0u64), |(s, f), bucket| {
                (s + bucket.successes, f + bucket.failures)
            });
        
        let total = total_success + total_failure;
        if total == 0 {
            0.0
        } else {
            total_failure as f64 / total as f64
        }
    }
}
```

### Error Classification
```rust
#[async_trait]
pub trait ErrorClassifier: Send + Sync {
    async fn classify(&self, error: &dyn std::error::Error) -> ErrorClass;
}

#[derive(Debug, Clone, Copy)]
pub enum ErrorClass {
    // Should trip the circuit breaker
    Critical,
    // Count towards failure rate
    Recoverable,
    // Don't count as failures
    Transient,
}

// Example implementation
pub struct HttpErrorClassifier;

#[async_trait]
impl ErrorClassifier for HttpErrorClassifier {
    async fn classify(&self, error: &dyn std::error::Error) -> ErrorClass {
        if let Some(http_error) = error.downcast_ref::<HttpError>() {
            match http_error.status_code() {
                500..=599 => ErrorClass::Critical,
                429 | 503 => ErrorClass::Recoverable,
                400..=499 => ErrorClass::Transient,
                _ => ErrorClass::Recoverable,
            }
        } else {
            ErrorClass::Recoverable
        }
    }
}
```

### Distributed Circuit Breaker
```rust
// For coordinating breaker state across agents
pub struct DistributedCircuitBreaker {
    local_breaker: CircuitBreaker,
    state_store: Arc<dyn StateStore>,
    sync_interval: Duration,
}

impl DistributedCircuitBreaker {
    pub async fn sync_state(&self) {
        let local_state = self.local_breaker.state.read().await;
        
        // Publish local state
        self.state_store.publish_state(
            &self.breaker_id,
            &*local_state,
        ).await;
        
        // Fetch global state
        let global_state = self.state_store
            .get_aggregated_state(&self.breaker_id)
            .await;
        
        // Merge states using consensus rules
        if should_open_globally(&global_state) {
            drop(local_state);
            self.local_breaker.force_open("Global consensus").await;
        }
    }
}
```

## Integration Patterns

### With Agent Communication
```rust
pub struct AgentClient {
    target: AgentId,
    breaker: CircuitBreaker,
    transport: Transport,
}

impl AgentClient {
    pub async fn send_message<T>(&self, msg: T) -> Result<Response, Error> {
        // Get permit from circuit breaker
        let permit = self.breaker.should_allow().await?;
        
        // Execute request
        match self.transport.send(&self.target, msg).await {
            Ok(response) => {
                permit.record_success().await;
                Ok(response)
            }
            Err(e) => {
                permit.record_failure(&e).await;
                Err(e)
            }
        }
    }
}
```

### With Load Balancer
```rust
impl LoadBalancer {
    pub async fn select_healthy_target(&self) -> Option<Target> {
        let targets = self.targets.read().await;
        
        // Filter by circuit breaker state
        let healthy_targets: Vec<_> = targets
            .iter()
            .filter(|t| t.breaker.is_closed_or_half_open())
            .collect();
        
        if healthy_targets.is_empty() {
            return None;
        }
        
        // Apply load balancing algorithm
        self.algorithm.select(&healthy_targets)
    }
}
```

## Performance Optimizations

### 1. State Caching
```rust
// Cache state checks for hot path
pub struct CachedCircuitBreaker {
    inner: CircuitBreaker,
    state_cache: Arc<AtomicU8>, // 0=closed, 1=open, 2=half-open
    cache_duration: Duration,
}
```

### 2. Batch State Updates
```rust
// Batch multiple state updates to reduce lock contention
pub struct BatchedStateUpdater {
    pending_updates: Arc<Mutex<Vec<StateUpdate>>>,
    flush_interval: Duration,
}
```

### 3. Zero-Copy Metrics
```rust
// Use atomic counters for metrics to avoid allocations
pub struct BreakerMetrics {
    requests_allowed: AtomicU64,
    requests_rejected: AtomicU64,
    state_transitions: AtomicU64,
    current_state: AtomicU8,
}
```

## Testing Utilities

### Chaos Testing
```rust
#[cfg(test)]
pub struct ChaosCircuitBreaker {
    inner: CircuitBreaker,
    failure_injection: FailureInjector,
}

impl ChaosCircuitBreaker {
    pub fn force_failures(&self, count: u32) {
        self.failure_injection.inject_failures(count);
    }
    
    pub fn force_latency(&self, duration: Duration) {
        self.failure_injection.inject_latency(duration);
    }
}
```