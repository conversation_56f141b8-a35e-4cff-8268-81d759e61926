# Memory Sharing Implementation Patterns

## Core Patterns from claude-code-flow

### 1. Hierarchical Memory Architecture

The memory system uses a multi-layered architecture combining different memory managers:

```typescript
// From src/memory/advanced-memory-manager.ts
export class AdvancedMemoryManager extends EventEmitter {
  private entries = new Map<string, MemoryEntry>();
  private index: MemoryIndex;
  private cache = new Map<string, { entry: MemoryEntry; expiry: number }>();
  private retentionPolicies = new Map<string, RetentionPolicy>();
}
```

### 2. Entry Structure Pattern

Memory entries follow a comprehensive structure for cross-agent sharing:

```typescript
// From src/memory/advanced-memory-manager.ts
export interface MemoryEntry {
  id: string;
  key: string;
  value: any;
  type: string;
  namespace: string;
  tags: string[];
  metadata: Record<string, any>;
  owner: string;
  accessLevel: 'private' | 'shared' | 'public';
  createdAt: Date;
  updatedAt: Date;
  lastAccessedAt: Date;
  expiresAt?: Date | undefined;
  version: number;
  size: number;
  compressed: boolean;
  checksum: string;
  references: string[];
  dependencies: string[];
}
```

### 3. Swarm Memory Pattern

Distributed memory system with partition-based organization:

```typescript
// From src/swarm/memory.ts
export class SwarmMemoryManager extends EventEmitter {
  private memory: SwarmMemory;
  private partitions: Map<string, MemoryPartition> = new Map();
  private entries: Map<string, MemoryEntry> = new Map();
  private index: MemoryIndex;
  private cache: MemoryCache;
  private replication: MemoryReplication;
  private persistence: MemoryPersistence;
  private encryption: MemoryEncryption;
}
```

### 4. Type Adapters Pattern

Flexible type conversion between different memory systems:

```typescript
// From src/types/memory-system.ts
export interface MemoryAdapter<T> {
  toMemoryEntry(data: T, metadata?: Partial<MemoryMetadata>): SwarmMemoryEntry;
  fromMemoryEntry(entry: SwarmMemoryEntry): T | null;
  canConvert(entry: SwarmMemoryEntry): boolean;
}

// Example implementation
export class AgentRegistryAdapter implements MemoryAdapter<AgentRegistryEntry> {
  toMemoryEntry(data: AgentRegistryEntry, metadata?: Partial<MemoryMetadata>): SwarmMemoryEntry {
    return {
      id: data.id,
      key: `agent:${data.agent.id.id}`,
      value: data,
      type: 'agent-registry',
      tags: [...data.tags, 'agent', data.agent.type, data.agent.status],
      owner: data.agent.id,
      accessLevel: 'swarm',
      createdAt: data.createdAt,
      updatedAt: data.lastUpdated,
      version: 1,
      references: [],
      dependencies: []
    };
  }
}
```

### 5. Memory Sharing Pattern

Cross-agent memory sharing with access control:

```typescript
// From src/swarm/memory.ts
async shareMemory(
  key: string,
  targetAgent: AgentId,
  options: Partial<{
    partition: string;
    sharer: AgentId;
    accessLevel: AccessLevel;
    expiresAt: Date;
  }> = {}
): Promise<string> {
  const entry = await this.findEntry(key, options.partition);
  if (!entry) {
    throw new Error(`Memory entry not found: ${key}`);
  }

  // Validate sharing permissions
  if (options.sharer) {
    await this.validateAccess(options.sharer, 'share', options.partition);
  }

  // Create shared copy
  const sharedEntry: MemoryEntry = {
    ...entry,
    id: generateId('shared-mem'),
    owner: targetAgent,
    accessLevel: options.accessLevel || entry.accessLevel,
    createdAt: new Date(),
    updatedAt: new Date(),
    references: [...entry.references, entry.id],
  };

  // Store shared entry
  this.entries.set(sharedEntry.id, sharedEntry);
  await this.index.addEntry(sharedEntry);

  return sharedEntry.id;
}
```

### 6. Query and Index Pattern

Efficient querying with multiple index types:

```typescript
// From src/memory/advanced-memory-manager.ts
export interface MemoryIndex {
  keys: Map<string, string[]>; // key -> entryIds
  tags: Map<string, string[]>; // tag -> entryIds
  types: Map<string, string[]>; // type -> entryIds
  namespaces: Map<string, string[]>; // namespace -> entryIds
  owners: Map<string, string[]>; // owner -> entryIds
  fullText: Map<string, string[]>; // word -> entryIds
}

// Query implementation
async query(options: QueryOptions = {}): Promise<{
  entries: MemoryEntry[];
  total: number;
  aggregations?: Record<string, any> | undefined;
}> {
  let candidateEntries: MemoryEntry[] = [];

  // Use index for efficient querying
  if (this.config.indexingEnabled) {
    candidateEntries = this.queryWithIndex(options);
  } else {
    candidateEntries = Array.from(this.entries.values());
  }

  // Apply filters
  let filteredEntries = candidateEntries.filter(entry => {
    return this.matchesQuery(entry, options);
  });

  return { entries: filteredEntries, total: filteredEntries.length };
}
```

### 7. Memory Broadcasting Pattern

One-to-many memory sharing:

```typescript
// From src/swarm/memory.ts
async broadcastMemory(
  key: string,
  targetAgents: AgentId[],
  options: Partial<{
    partition: string;
    broadcaster: AgentId;
    accessLevel: AccessLevel;
  }> = {}
): Promise<string[]> {
  const sharedIds: string[] = [];

  for (const targetAgent of targetAgents) {
    try {
      const sharedId = await this.shareMemory(key, targetAgent, {
        ...options,
        sharer: options.broadcaster
      });
      sharedIds.push(sharedId);
    } catch (error) {
      this.logger.warn('Failed to share memory with agent', {
        key,
        targetAgent: targetAgent.id,
        error: error.message
      });
    }
  }

  this.emit('memory:broadcasted', {
    key,
    broadcaster: options.broadcaster?.id,
    targets: targetAgents.map(a => a.id),
    sharedCount: sharedIds.length
  });

  return sharedIds;
}
```

### 8. Memory Partitioning Pattern

Logical separation of memory spaces:

```typescript
// From src/swarm/memory.ts
interface MemoryPartition {
  id: string;
  name: string;
  type: MemoryType;
  entries: MemoryEntry[];
  maxSize: number;
  ttl?: number;
  readOnly: boolean;
  shared: boolean;
  indexed: boolean;
  compressed: boolean;
}

async createPartition(
  name: string,
  options: Partial<{
    type: MemoryType;
    maxSize: number;
    ttl: number;
    readOnly: boolean;
    shared: boolean;
    indexed: boolean;
    compressed: boolean;
  }> = {}
): Promise<string> {
  const partition: MemoryPartition = {
    id: generateId('partition'),
    name,
    type: options.type || 'knowledge',
    entries: [],
    maxSize: options.maxSize || this.config.maxMemorySize,
    readOnly: options.readOnly || false,
    shared: options.shared || true,
    indexed: options.indexed !== false,
    compressed: options.compressed || this.config.enableCompression
  };

  this.partitions.set(name, partition);
  return partition.id;
}
```

### 9. Cache Layer Pattern

Multi-level caching for performance:

```typescript
// From src/memory/advanced-memory-manager.ts
private updateCache(key: string, entry: MemoryEntry): void {
  if (this.cache.size >= this.config.cacheSize) {
    this.evictCache();
  }
  
  this.cache.set(key, {
    entry: { ...entry },
    expiry: Date.now() + this.config.cacheTtl
  });
}

private evictCache(): void {
  const entries = Array.from(this.cache.entries());
  entries.sort((a, b) => a[1].expiry - b[1].expiry);
  
  const toRemove = entries.slice(0, Math.floor(this.config.cacheSize * 0.1));
  toRemove.forEach(([key]) => this.cache.delete(key));
}
```

### 10. Access Control Pattern

Fine-grained permission management:

```typescript
// From src/types/memory-system.ts
export type AccessLevel = 
  | 'private'     // Only owner can access
  | 'team'        // Team members can access
  | 'swarm'       // All swarm agents can access
  | 'public'      // Publicly accessible
  | 'system';     // System-level access

export function canAccess(
  entry: SwarmMemoryEntry,
  agentId: AgentId,
  level?: AccessLevel
): boolean {
  // Owner always has access
  if (entry.owner.id === agentId.id) return true;
  
  // Check access level hierarchy
  const levels: AccessLevel[] = ['public', 'swarm', 'team', 'private', 'system'];
  const entryLevel = levels.indexOf(entry.accessLevel);
  const requiredLevel = levels.indexOf(level || 'public');
  
  return entryLevel <= requiredLevel;
}
```

## Key Implementation Principles

1. **Hierarchical Access Control**: Multiple access levels with clear permission boundaries
2. **Namespace Isolation**: Logical separation of memory spaces for different contexts
3. **Version Control**: Built-in versioning for change tracking and rollback
4. **Lazy Loading**: Cache layers to minimize memory access overhead
5. **Type Safety**: Strong typing with adapter patterns for flexibility
6. **Event-Driven**: EventEmitter pattern for reactive updates
7. **Compression Support**: Optional compression for large entries
8. **TTL Management**: Automatic expiration of time-sensitive data
9. **Index Optimization**: Multiple index types for efficient querying
10. **Replication Ready**: Built-in support for distributed memory synchronization