# Core Capability Execution Patterns

> **Note**: For fault tolerance patterns including circuit breakers, see the [Circuit Breaker Pattern Reference](../../../docs/circuit-breaker-pattern.md).

## Runtime Behavior Models

The execution patterns define how core capabilities operate at runtime, including their lifecycle, interaction protocols, and performance characteristics.

## Capability Lifecycle Patterns

### Initialization Pattern

**Bootstrap Sequence**
```typescript
interface CapabilityBootstrap {
  phase: 'pre-init' | 'init' | 'post-init' | 'ready';
  dependencies: string[];
  timeout: number;
  rollbackStrategy: RollbackFunction;
}

// Example: Orchestrator Initialization
const orchestratorBootstrap: CapabilityBootstrap = {
  phase: 'init',
  dependencies: ['event-bus', 'config-manager', 'logger'],
  timeout: 30000,
  rollbackStrategy: async (error) => {
    await this.cleanupPartialState();
    await this.notifyAdministrators(error);
  }
};
```

**Dependency Resolution**
```mermaid
graph TD
    A[Configuration Manager] --> B[Logger]
    A --> C[Event Bus]
    B --> D[Orchestrator]
    C --> D
    D --> E[Memory Manager]
    D --> F[Coordination Manager]
    E --> G[MCP Server]
    F --> G
    G --> H[System Ready]
```

### Operational Pattern

**Steady-State Execution**
```typescript
interface OperationalLoop {
  interval: number;
  operations: OperationFunction[];
  healthCheck: HealthCheckFunction;
  errorHandler: ErrorHandlerFunction;
}

// Example: Health Monitoring Loop
const healthMonitoringLoop: OperationalLoop = {
  interval: 10000, // 10 seconds
  operations: [
    () => this.checkComponentHealth(),
    () => this.collectMetrics(),
    () => this.updateStatus(),
    () => this.triggerAlerts()
  ],
  healthCheck: () => this.selfDiagnostic(),
  errorHandler: async (error) => {
    await this.logError(error);
    if (error.severity === 'critical') {
      await this.initiateSafeMode();
    }
  }
};
```

### Shutdown Pattern

**Graceful Termination Sequence**
```typescript
interface ShutdownStrategy {
  phases: ShutdownPhase[];
  timeout: number;
  forceShutdown: boolean;
}

const gracefulShutdown: ShutdownStrategy = {
  phases: [
    {
      name: 'stop-accepting-requests',
      timeout: 5000,
      action: async () => await this.stopAcceptingNewWork()
    },
    {
      name: 'complete-current-work',
      timeout: 30000,
      action: async () => await this.finishCurrentTasks()
    },
    {
      name: 'persist-state',
      timeout: 10000,
      action: async () => await this.saveSystemState()
    },
    {
      name: 'cleanup-resources',
      timeout: 5000,
      action: async () => await this.releaseResources()
    }
  ],
  timeout: 60000, // Total shutdown timeout
  forceShutdown: true // Force shutdown if timeout exceeded
};
```

## Execution Flow Patterns

### Request Processing Pattern

**Synchronous Request Flow**
```typescript
interface RequestPipeline {
  middleware: MiddlewareFunction[];
  handler: RequestHandler;
  postProcessors: PostProcessorFunction[];
}

// Example: CLI Command Processing Pipeline
const commandPipeline: RequestPipeline = {
  middleware: [
    authenticationMiddleware,
    authorizationMiddleware,
    validationMiddleware,
    rateLimitingMiddleware,
    loggingMiddleware
  ],
  handler: async (request) => {
    const command = this.parseCommand(request);
    const result = await this.executeCommand(command);
    return this.formatResponse(result);
  },
  postProcessors: [
    responseFormatterProcessor,
    auditLogProcessor,
    metricsCollectionProcessor
  ]
};
```

**Asynchronous Event Flow**
```typescript
interface EventProcessingPipeline {
  filters: EventFilter[];
  transformers: EventTransformer[];
  handlers: EventHandler[];
  errorHandlers: ErrorHandler[];
}

// Example: Agent Lifecycle Event Processing
const agentEventPipeline: EventProcessingPipeline = {
  filters: [
    (event) => event.type.startsWith('agent.'),
    (event) => event.data.agentId !== undefined
  ],
  transformers: [
    (event) => this.enrichEventWithMetadata(event),
    (event) => this.addCorrelationId(event)
  ],
  handlers: [
    async (event) => await this.updateAgentStatus(event),
    async (event) => await this.notifyInterestedParties(event),
    async (event) => await this.updateMetrics(event)
  ],
  errorHandlers: [
    async (error, event) => await this.logProcessingError(error, event),
    async (error, event) => await this.alertOperators(error, event)
  ]
};
```

### Batch Processing Pattern

**Work Item Batching**
```typescript
interface BatchProcessor<T> {
  batchSize: number;
  timeout: number;
  processor: BatchProcessorFunction<T>;
  resultAggregator: ResultAggregatorFunction<T>;
}

// Example: Task Assignment Batching
const taskAssignmentBatcher: BatchProcessor<Task> = {
  batchSize: 50,
  timeout: 5000,
  processor: async (tasks: Task[]) => {
    const assignments = await this.optimizeTaskAssignments(tasks);
    return await Promise.allSettled(
      assignments.map(assignment => this.assignTask(assignment))
    );
  },
  resultAggregator: (results) => {
    const successful = results.filter(r => r.status === 'fulfilled');
    const failed = results.filter(r => r.status === 'rejected');
    return {
      successCount: successful.length,
      failureCount: failed.length,
      errors: failed.map(f => f.reason)
    };
  }
};
```

### Stream Processing Pattern

**Real-time Data Streams**
```typescript
interface StreamProcessor<T> {
  source: StreamSource<T>;
  transforms: StreamTransform<T>[];
  sink: StreamSink<T>;
  backpressureStrategy: BackpressureStrategy;
}

// Example: Metrics Stream Processing
const metricsStreamProcessor: StreamProcessor<MetricEvent> = {
  source: this.metricsEventStream,
  transforms: [
    (stream) => stream.filter(event => event.value !== null),
    (stream) => stream.map(event => this.normalizeMetric(event)),
    (stream) => stream.window(60000), // 1-minute windows
    (stream) => stream.aggregate(this.calculateAverages)
  ],
  sink: this.metricsDatabase,
  backpressureStrategy: 'drop-oldest'
};
```

## Concurrency Patterns

### Actor Model Pattern

**Agent as Actor**
```typescript
interface Actor {
  id: string;
  mailbox: MessageQueue;
  behavior: BehaviorFunction;
  state: ActorState;
}

class AgentActor implements Actor {
  constructor(
    public id: string,
    public mailbox: MessageQueue,
    private initialState: AgentState
  ) {}

  async behavior(message: Message): Promise<void> {
    switch (message.type) {
      case 'task.assign':
        await this.handleTaskAssignment(message.data);
        break;
      case 'task.cancel':
        await this.handleTaskCancellation(message.data);
        break;
      case 'health.check':
        await this.respondWithHealthStatus(message.replyTo);
        break;
      default:
        await this.handleUnknownMessage(message);
    }
  }

  private async handleTaskAssignment(taskData: TaskData): Promise<void> {
    // Actor behavior for task assignment
    this.state.currentTask = taskData;
    this.state.status = 'busy';
    
    try {
      const result = await this.executeTask(taskData);
      await this.sendMessage('coordinator', {
        type: 'task.completed',
        data: { taskId: taskData.id, result }
      });
    } catch (error) {
      await this.sendMessage('coordinator', {
        type: 'task.failed',
        data: { taskId: taskData.id, error }
      });
    }
    
    this.state.status = 'idle';
  }
}
```

### Producer-Consumer Pattern

**Work Queue Implementation**
```typescript
interface WorkQueue<T> {
  enqueue(item: T): Promise<void>;
  dequeue(): Promise<T | null>;
  size(): number;
  isEmpty(): boolean;
}

class TaskQueue implements WorkQueue<Task> {
  private queue: Task[] = [];
  private waitingConsumers: Array<(task: Task | null) => void> = [];
  private maxSize: number;

  async enqueue(task: Task): Promise<void> {
    if (this.queue.length >= this.maxSize) {
      throw new Error('Queue is full');
    }

    this.queue.push(task);
    
    // Notify waiting consumers
    if (this.waitingConsumers.length > 0) {
      const consumer = this.waitingConsumers.shift()!;
      consumer(this.queue.shift()!);
    }
  }

  async dequeue(): Promise<Task | null> {
    if (this.queue.length > 0) {
      return this.queue.shift()!;
    }

    // Wait for new items
    return new Promise((resolve) => {
      this.waitingConsumers.push(resolve);
      
      // Timeout after 30 seconds
      setTimeout(() => {
        const index = this.waitingConsumers.indexOf(resolve);
        if (index !== -1) {
          this.waitingConsumers.splice(index, 1);
          resolve(null);
        }
      }, 30000);
    });
  }
}
```

### Semaphore Pattern

**Resource Limiting**
```typescript
class Semaphore {
  private permits: number;
  private waitQueue: Array<() => void> = [];

  constructor(initialPermits: number) {
    this.permits = initialPermits;
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return;
    }

    return new Promise((resolve) => {
      this.waitQueue.push(resolve);
    });
  }

  release(): void {
    if (this.waitQueue.length > 0) {
      const next = this.waitQueue.shift()!;
      next();
    } else {
      this.permits++;
    }
  }
}

// Usage: Limit concurrent agent spawning
class AgentManager {
  private spawnSemaphore = new Semaphore(10); // Max 10 concurrent spawns

  async spawnAgent(profile: AgentProfile): Promise<string> {
    await this.spawnSemaphore.acquire();
    
    try {
      return await this.doSpawnAgent(profile);
    } finally {
      this.spawnSemaphore.release();
    }
  }
}
```

## Error Handling Patterns

### Circuit Breaker Pattern

**Failure Detection and Recovery**
```typescript
enum CircuitState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open'
}

class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private lastFailureTime: Date | null = null;
  private successCount: number = 0;

  constructor(
    private threshold: number = 5,
    private timeout: number = 60000,
    private monitoringPeriod: number = 10000
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;
    if (this.state === CircuitState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= 3) {
        this.state = CircuitState.CLOSED;
        this.successCount = 0;
      }
    }
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = new Date();
    
    if (this.failureCount >= this.threshold) {
      this.state = CircuitState.OPEN;
    }
  }

  private shouldAttemptReset(): boolean {
    return this.lastFailureTime !== null &&
           (Date.now() - this.lastFailureTime.getTime()) >= this.timeout;
  }
}
```

### Retry Pattern

**Exponential Backoff with Jitter**
```typescript
interface RetryConfig {
  maxAttempts: number;
  initialDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitterFactor: number;
  retryCondition: (error: Error) => boolean;
}

class RetryExecutor {
  async execute<T>(
    operation: () => Promise<T>,
    config: RetryConfig
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (!config.retryCondition(lastError) || attempt === config.maxAttempts) {
          throw lastError;
        }
        
        const delay = this.calculateDelay(attempt, config);
        await this.delay(delay);
      }
    }
    
    throw lastError!;
  }

  private calculateDelay(attempt: number, config: RetryConfig): number {
    const exponentialDelay = Math.min(
      config.initialDelay * Math.pow(config.backoffMultiplier, attempt - 1),
      config.maxDelay
    );
    
    // Add jitter to prevent thundering herd
    const jitter = exponentialDelay * config.jitterFactor * Math.random();
    return exponentialDelay + jitter;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

## Performance Optimization Patterns

### Caching Pattern

**Multi-Level Cache**
```typescript
interface CacheLevel {
  name: string;
  get(key: string): Promise<any>;
  set(key: string, value: any, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
}

class MultiLevelCache {
  constructor(private levels: CacheLevel[]) {}

  async get(key: string): Promise<any> {
    for (let i = 0; i < this.levels.length; i++) {
      const value = await this.levels[i].get(key);
      if (value !== null) {
        // Promote to higher cache levels
        for (let j = 0; j < i; j++) {
          await this.levels[j].set(key, value);
        }
        return value;
      }
    }
    return null;
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    // Set in all cache levels
    await Promise.all(
      this.levels.map(level => level.set(key, value, ttl))
    );
  }
}
```

### Connection Pooling Pattern

**Database Connection Pool**
```typescript
class ConnectionPool {
  private available: Connection[] = [];
  private inUse: Set<Connection> = new Set();
  private waiting: Array<(connection: Connection) => void> = [];

  constructor(
    private minSize: number,
    private maxSize: number,
    private connectionFactory: () => Promise<Connection>
  ) {}

  async getConnection(): Promise<Connection> {
    if (this.available.length > 0) {
      const connection = this.available.pop()!;
      this.inUse.add(connection);
      return connection;
    }

    if (this.inUse.size < this.maxSize) {
      const connection = await this.connectionFactory();
      this.inUse.add(connection);
      return connection;
    }

    // Wait for available connection
    return new Promise((resolve) => {
      this.waiting.push(resolve);
    });
  }

  releaseConnection(connection: Connection): void {
    this.inUse.delete(connection);
    
    if (this.waiting.length > 0) {
      const waiter = this.waiting.shift()!;
      this.inUse.add(connection);
      waiter(connection);
    } else {
      this.available.push(connection);
    }
  }
}
```

These execution patterns provide the runtime behavior models necessary for implementing robust, scalable, and maintainable core capabilities in the claude-code-flow system.