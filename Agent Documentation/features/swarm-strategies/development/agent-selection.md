# Development Strategy Agent Selection

## Agent Selection Algorithm

The Development strategy employs a hierarchical agent selection algorithm that matches technical expertise to development phases and creates effective team structures. Based on claude-code-flow implementation patterns, it uses capability-based assignment with performance optimization.

## Agent Type Definitions

### Development Team Agent Types

```typescript
// Development strategy agent type definitions from claude-code-flow
interface DevelopmentAgentTypes {
  architect: {
    capabilities: ['system-design', 'architecture-planning', 'technical-leadership'],
    specializations: ['microservices', 'monolith', 'serverless', 'distributed-systems'],
    workload: 'strategic-planning',
    coordination: 'centralized',
    leadership: true
  };
  
  coder: {
    capabilities: ['code-implementation', 'debugging', 'optimization'],
    specializations: ['frontend', 'backend', 'fullstack', 'devops'],
    workload: 'implementation',
    coordination: 'hierarchical',
    leadership: false
  };
  
  tester: {
    capabilities: ['test-creation', 'quality-assurance', 'validation'],
    specializations: ['unit-testing', 'integration-testing', 'e2e-testing', 'performance-testing'],
    workload: 'validation',
    coordination: 'mesh',
    leadership: false
  };
  
  reviewer: {
    capabilities: ['code-review', 'architecture-review', 'quality-assessment'],
    specializations: ['security-review', 'performance-review', 'best-practices'],
    workload: 'quality-control',
    coordination: 'mesh',
    leadership: false
  };
  
  'batch-executor': {
    capabilities: ['deployment', 'automation', 'ci-cd'],
    specializations: ['docker', 'kubernetes', 'cloud-deployment', 'pipeline-automation'],
    workload: 'operations',
    coordination: 'centralized',
    leadership: false
  };
}
```

## Agent Spawning Logic

### Development-Specific Agent Selection

```typescript
// Agent selection algorithm for development strategy
class DevelopmentAgentSelector {
  async selectAgentsForDevelopment(
    objective: SwarmObjective,
    tracks: DevelopmentTrack[]
  ): Promise<AgentSelection> {
    const selection: AgentSelection = {
      leadership: [],
      implementation: [],
      quality: [],
      operations: [],
      total: 0
    };
    
    // Analyze development requirements
    const requirements = this.analyzeDevelopmentRequirements(objective, tracks);
    
    // Leadership layer (always required)
    selection.leadership.push({
      type: 'architect',
      count: 1,
      priority: 'critical',
      specialization: this.selectArchitecturalSpecialization(requirements),
      role: 'team-lead'
    });
    
    // Implementation layer
    const coderRequirements = this.calculateCoderRequirements(tracks, requirements);
    selection.implementation.push({
      type: 'coder',
      count: coderRequirements.count,
      priority: 'high',
      specializations: coderRequirements.specializations,
      teamStructure: coderRequirements.teamStructure
    });
    
    // Quality layer
    if (requirements.needsQualityAssurance) {
      const testerCount = Math.ceil(coderRequirements.count / 3); // 1 tester per 3 coders
      selection.quality.push({
        type: 'tester',
        count: testerCount,
        priority: 'high',
        specialization: this.selectTestingSpecialization(requirements)
      });
      
      const reviewerCount = Math.ceil(coderRequirements.count / 4); // 1 reviewer per 4 coders
      selection.quality.push({
        type: 'reviewer',
        count: reviewerCount,
        priority: 'medium',
        specialization: 'code-review'
      });
    }
    
    // Operations layer
    if (requirements.needsDeployment) {
      selection.operations.push({
        type: 'batch-executor',
        count: 1,
        priority: 'medium',
        specialization: this.selectDeploymentSpecialization(requirements)
      });
    }
    
    selection.total = this.calculateTotalAgents(selection);
    
    return selection;
  }
  
  private calculateCoderRequirements(
    tracks: DevelopmentTrack[],
    requirements: DevelopmentRequirements
  ): CoderRequirements {
    let baseCount = 2; // Minimum team size
    const specializations: string[] = [];
    
    // Frontend specialization
    if (tracks.some(t => t.type === 'frontend')) {
      baseCount += 2;
      specializations.push('frontend');
    }
    
    // Backend specialization
    if (tracks.some(t => t.type === 'backend')) {
      baseCount += 2;
      specializations.push('backend');
    }
    
    // Scale based on complexity
    const complexityMultiplier = this.calculateComplexityMultiplier(requirements.complexity);
    const adjustedCount = Math.min(Math.ceil(baseCount * complexityMultiplier), 8);
    
    return {
      count: adjustedCount,
      specializations,
      teamStructure: this.determineTeamStructure(adjustedCount, specializations)
    };
  }
}
```

## Agent Capability Matching

### Development-Specific Agent Scoring

```typescript
// Agent scoring algorithm for development tasks
class DevelopmentAgentScoring {
  async scoreAgentForDevelopmentTask(
    agent: AgentProfile,
    task: DevelopmentTask,
    context: DevelopmentContext
  ): Promise<AgentScore> {
    const scores = await Promise.all([
      this.calculateTechnicalCapability(agent, task),      // 35% weight
      this.calculateDevelopmentExperience(agent, task),    // 25% weight
      this.calculateTeamCompatibility(agent, context),     // 20% weight
      this.calculateWorkloadBalance(agent),                // 15% weight
      this.calculateSpecializationMatch(agent, task)       // 5% weight
    ]);
    
    return this.aggregateScores(scores, [0.35, 0.25, 0.20, 0.15, 0.05]);
  }
  
  private async calculateTechnicalCapability(
    agent: AgentProfile,
    task: DevelopmentTask
  ): Promise<number> {
    const requiredTechnologies = task.requiredTechnologies;
    const agentTechnologies = agent.technicalSkills;
    
    // Calculate technology stack overlap
    const intersection = requiredTechnologies.filter(tech =>
      agentTechnologies.some(skill => skill.technology === tech && skill.proficiency >= 0.7)
    );
    
    const technicalScore = intersection.length / requiredTechnologies.length;
    
    // Bonus for advanced proficiency
    const proficiencyBonus = this.calculateProficiencyBonus(agent, requiredTechnologies);
    
    // Bonus for related experience
    const experienceBonus = this.calculateExperienceBonus(agent, task);
    
    return Math.min(technicalScore + proficiencyBonus + experienceBonus, 1.0);
  }
  
  private async calculateDevelopmentExperience(
    agent: AgentProfile,
    task: DevelopmentTask
  ): Promise<number> {
    const developmentHistory = await this.getAgentDevelopmentHistory(agent.id);
    
    if (developmentHistory.length === 0) return 0.4; // Neutral score for new agents
    
    // Filter history by relevant project types
    const relevantProjects = developmentHistory.filter(project =>
      this.isProjectRelevant(project, task)
    );
    
    const metrics = {
      successRate: relevantProjects.filter(p => p.success).length / relevantProjects.length,
      avgQuality: relevantProjects.reduce((sum, p) => sum + p.codeQuality, 0) / relevantProjects.length,
      avgSpeed: relevantProjects.reduce((sum, p) => sum + p.deliverySpeed, 0) / relevantProjects.length,
      complexity: relevantProjects.reduce((sum, p) => sum + p.complexityHandled, 0) / relevantProjects.length
    };
    
    // Weight: 40% success rate, 25% quality, 20% speed, 15% complexity
    return (metrics.successRate * 0.4) + (metrics.avgQuality * 0.25) + 
           (metrics.avgSpeed * 0.2) + (metrics.complexity * 0.15);
  }
  
  private async calculateTeamCompatibility(
    agent: AgentProfile,
    context: DevelopmentContext
  ): Promise<number> {
    const existingTeam = context.currentTeam || [];
    
    if (existingTeam.length === 0) return 0.8; // High score for first team member
    
    // Analyze collaboration history
    const collaborationHistory = await this.getCollaborationHistory(agent.id, existingTeam);
    
    // Calculate communication style compatibility
    const communicationCompatibility = this.calculateCommunicationCompatibility(
      agent.communicationStyle,
      existingTeam.map(m => m.communicationStyle)
    );
    
    // Calculate skill complementarity
    const skillComplementarity = this.calculateSkillComplementarity(
      agent.technicalSkills,
      existingTeam.map(m => m.technicalSkills).flat()
    );
    
    // Calculate working pattern compatibility
    const workingPatternCompatibility = this.calculateWorkingPatternCompatibility(
      agent.workingPattern,
      existingTeam.map(m => m.workingPattern)
    );
    
    return (collaborationHistory * 0.4) + (communicationCompatibility * 0.3) +
           (skillComplementarity * 0.2) + (workingPatternCompatibility * 0.1);
  }
}
```

## Team Structure Creation

### Hierarchical Team Organization

```typescript
// Team structure creation for development projects
class DevelopmentTeamBuilder {
  async buildDevelopmentTeam(
    agents: AgentProfile[],
    tracks: DevelopmentTrack[],
    requirements: DevelopmentRequirements
  ): Promise<DevelopmentTeamStructure> {
    const teamStructure: DevelopmentTeamStructure = {
      architect: null,
      teams: [],
      crossFunctional: [],
      coordination: 'hierarchical'
    };
    
    // Select architect (team lead)
    teamStructure.architect = await this.selectArchitect(agents, requirements);
    
    // Create specialized development teams
    if (requirements.needsFrontend) {
      const frontendTeam = await this.createFrontendTeam(agents, tracks);
      teamStructure.teams.push(frontendTeam);
    }
    
    if (requirements.needsBackend) {
      const backendTeam = await this.createBackendTeam(agents, tracks);
      teamStructure.teams.push(backendTeam);
    }
    
    // Create cross-functional roles
    teamStructure.crossFunctional = await this.assignCrossFunctionalRoles(
      agents,
      teamStructure.teams
    );
    
    // Validate team composition
    await this.validateTeamComposition(teamStructure);
    
    return teamStructure;
  }
  
  private async createFrontendTeam(
    agents: AgentProfile[],
    tracks: DevelopmentTrack[]
  ): Promise<DevelopmentTeam> {
    const frontendTracks = tracks.filter(t => t.type === 'frontend');
    const frontendAgents = agents.filter(a => 
      a.specializations.includes('frontend') || a.specializations.includes('fullstack')
    );
    
    // Select team lead (most experienced frontend developer)
    const teamLead = await this.selectTeamLead(frontendAgents, 'frontend');
    
    // Select team members
    const teamMembers = await this.selectTeamMembers(
      frontendAgents.filter(a => a.id !== teamLead.id),
      frontendTracks,
      2 // Target team size
    );
    
    // Assign specialized roles
    const roles = await this.assignSpecializedRoles(teamMembers, [
      'ui-implementation',
      'component-development',
      'state-management',
      'testing'
    ]);
    
    return {
      id: 'frontend-team',
      type: 'frontend',
      lead: teamLead,
      members: teamMembers,
      roles,
      tracks: frontendTracks,
      coordination: 'hierarchical'
    };
  }
  
  private async createBackendTeam(
    agents: AgentProfile[],
    tracks: DevelopmentTrack[]
  ): Promise<DevelopmentTeam> {
    const backendTracks = tracks.filter(t => t.type === 'backend');
    const backendAgents = agents.filter(a =>
      a.specializations.includes('backend') || a.specializations.includes('fullstack')
    );
    
    // Select team lead
    const teamLead = await this.selectTeamLead(backendAgents, 'backend');
    
    // Select team members based on backend complexity
    const teamSize = this.calculateBackendTeamSize(backendTracks);
    const teamMembers = await this.selectTeamMembers(
      backendAgents.filter(a => a.id !== teamLead.id),
      backendTracks,
      teamSize
    );
    
    // Assign specialized roles
    const roles = await this.assignSpecializedRoles(teamMembers, [
      'api-development',
      'database-design',
      'business-logic',
      'integration',
      'testing'
    ]);
    
    return {
      id: 'backend-team',
      type: 'backend',
      lead: teamLead,
      members: teamMembers,
      roles,
      tracks: backendTracks,
      coordination: 'hierarchical'
    };
  }
}
```

## Agent Assignment Optimization

### Dynamic Role Assignment

```typescript
// Dynamic role assignment for development tasks
class DevelopmentRoleAssigner {
  async assignRolesToTasks(
    team: DevelopmentTeamStructure,
    tasks: DevelopmentTask[]
  ): Promise<RoleAssignmentPlan> {
    const plan: RoleAssignmentPlan = {
      assignments: new Map(),
      workloadDistribution: new Map(),
      collaborationMatrix: new Map()
    };
    
    // Phase-based assignment strategy
    const phases = this.groupTasksByPhase(tasks);
    
    for (const [phase, phaseTasks] of phases) {
      const phaseAssignments = await this.assignPhaseRoles(team, phaseTasks, phase);
      
      for (const [taskId, assignment] of phaseAssignments) {
        plan.assignments.set(taskId, assignment);
      }
    }
    
    // Calculate workload distribution
    plan.workloadDistribution = this.calculateWorkloadDistribution(plan.assignments);
    
    // Optimize for load balancing
    const optimizedPlan = await this.optimizeLoadBalance(plan, team);
    
    return optimizedPlan;
  }
  
  private async assignPhaseRoles(
    team: DevelopmentTeamStructure,
    tasks: DevelopmentTask[],
    phase: string
  ): Promise<Map<string, RoleAssignment>> {
    const assignments = new Map<string, RoleAssignment>();
    
    switch (phase) {
      case 'architecture':
        // Architect leads, reviewers provide feedback
        for (const task of tasks) {
          assignments.set(task.id, {
            primaryAgent: team.architect.id,
            supportingAgents: team.crossFunctional
              .filter(a => a.type === 'reviewer')
              .map(a => a.id),
            role: 'lead',
            coordination: 'centralized'
          });
        }
        break;
        
      case 'implementation':
        // Team leads coordinate, team members implement
        for (const task of tasks) {
          const relevantTeam = this.findRelevantTeam(task, team.teams);
          
          if (relevantTeam) {
            assignments.set(task.id, {
              primaryAgent: relevantTeam.lead.id,
              supportingAgents: relevantTeam.members.map(m => m.id),
              role: 'coordinator',
              coordination: 'hierarchical'
            });
          }
        }
        break;
        
      case 'testing':
        // Testers lead, developers support
        for (const task of tasks) {
          const testers = team.crossFunctional.filter(a => a.type === 'tester');
          const relevantDevelopers = this.findRelevantDevelopers(task, team);
          
          assignments.set(task.id, {
            primaryAgent: testers[0]?.id,
            supportingAgents: relevantDevelopers.map(d => d.id),
            role: 'validator',
            coordination: 'mesh'
          });
        }
        break;
        
      case 'deployment':
        // Batch executor leads, architect reviews
        for (const task of tasks) {
          const batchExecutor = team.crossFunctional.find(a => a.type === 'batch-executor');
          
          assignments.set(task.id, {
            primaryAgent: batchExecutor?.id,
            supportingAgents: [team.architect.id],
            role: 'executor',
            coordination: 'centralized'
          });
        }
        break;
    }
    
    return assignments;
  }
}
```

## Specialization Mapping

### Technology-Specific Agent Selection

```typescript
// Technology specialization mapping for development
interface DevelopmentSpecializations {
  'frontend-react': {
    agents: ['coder'],
    skills: ['react', 'typescript', 'redux', 'css'],
    focus: 'ui-development',
    coordination: 'hierarchical'
  };
  
  'backend-nodejs': {
    agents: ['coder', 'tester'],
    skills: ['nodejs', 'express', 'mongodb', 'rest-api'],
    focus: 'api-development',
    coordination: 'hierarchical'
  };
  
  'fullstack-web': {
    agents: ['architect', 'coder', 'tester'],
    skills: ['frontend', 'backend', 'database', 'deployment'],
    focus: 'end-to-end-development',
    coordination: 'distributed'
  };
  
  'microservices': {
    agents: ['architect', 'coder', 'batch-executor'],
    skills: ['docker', 'kubernetes', 'api-gateway', 'service-mesh'],
    focus: 'distributed-architecture',
    coordination: 'hierarchical'
  };
}

class DevelopmentSpecializationMapper {
  mapSpecializationToAgents(
    projectType: string,
    objective: SwarmObjective
  ): AgentSpecializationMap {
    const specialization = this.detectDevelopmentSpecialization(objective);
    const spec = DevelopmentSpecializations[specialization];
    
    return {
      requiredAgentTypes: spec.agents,
      requiredSkills: spec.skills,
      focusArea: spec.focus,
      recommendedCoordination: spec.coordination,
      teamSize: this.calculateOptimalTeamSize(specialization),
      estimatedDuration: this.estimateProjectDuration(specialization, objective)
    };
  }
  
  private detectDevelopmentSpecialization(objective: SwarmObjective): string {
    const description = objective.description.toLowerCase();
    
    // Technology stack detection
    if (description.includes('react') && !description.includes('backend')) {
      return 'frontend-react';
    }
    
    if (description.includes('api') && description.includes('nodejs')) {
      return 'backend-nodejs';
    }
    
    if (description.includes('full-stack') || description.includes('end-to-end')) {
      return 'fullstack-web';
    }
    
    if (description.includes('microservice') || description.includes('distributed')) {
      return 'microservices';
    }
    
    // Default to fullstack for comprehensive projects
    return 'fullstack-web';
  }
  
  private calculateOptimalTeamSize(specialization: string): number {
    const teamSizes = {
      'frontend-react': 3,
      'backend-nodejs': 3,
      'fullstack-web': 5,
      'microservices': 8
    };
    
    return teamSizes[specialization] || 4;
  }
}
```

## Load Balancing and Performance

### Development Team Load Optimization

```typescript
// Load balancing for development teams
class DevelopmentLoadBalancer {
  optimizeDevelopmentLoad(
    teamStructure: DevelopmentTeamStructure,
    tasks: DevelopmentTask[]
  ): OptimizedTeamStructure {
    // Analyze current workload distribution
    const workloadAnalysis = this.analyzeTeamWorkload(teamStructure, tasks);
    
    // Identify load imbalances
    const imbalances = this.identifyLoadImbalances(workloadAnalysis);
    
    // Rebalance team assignments
    const rebalancedStructure = this.rebalanceTeams(teamStructure, imbalances);
    
    // Optimize cross-team collaboration
    const optimizedStructure = this.optimizeCrossTeamCollaboration(rebalancedStructure);
    
    return optimizedStructure;
  }
  
  private analyzeTeamWorkload(
    teamStructure: DevelopmentTeamStructure,
    tasks: DevelopmentTask[]
  ): WorkloadAnalysis {
    const teamLoads = new Map<string, TeamWorkload>();
    
    for (const team of teamStructure.teams) {
      const teamTasks = tasks.filter(task => this.isTaskRelevantToTeam(task, team));
      const totalComplexity = teamTasks.reduce((sum, task) => sum + task.complexity, 0);
      const avgComplexity = totalComplexity / team.members.length;
      
      teamLoads.set(team.id, {
        taskCount: teamTasks.length,
        totalComplexity,
        avgComplexityPerMember: avgComplexity,
        estimatedDuration: this.estimateTeamDuration(teamTasks),
        utilizationRate: this.calculateUtilizationRate(team, teamTasks)
      });
    }
    
    return {
      teamLoads,
      overallBalance: this.calculateOverallBalance(teamLoads),
      bottlenecks: this.identifyBottlenecks(teamLoads)
    };
  }
  
  private rebalanceTeams(
    teamStructure: DevelopmentTeamStructure,
    imbalances: LoadImbalance[]
  ): DevelopmentTeamStructure {
    const rebalanced = JSON.parse(JSON.stringify(teamStructure)); // Deep copy
    
    for (const imbalance of imbalances) {
      if (imbalance.type === 'OVERLOADED_TEAM') {
        // Move some members from overloaded team to underloaded teams
        const sourceTeam = rebalanced.teams.find(t => t.id === imbalance.sourceTeam);
        const targetTeam = rebalanced.teams.find(t => t.id === imbalance.targetTeam);
        
        if (sourceTeam && targetTeam && sourceTeam.members.length > 2) {
          const memberToMove = this.selectMemberToMove(sourceTeam, targetTeam);
          
          if (memberToMove) {
            sourceTeam.members = sourceTeam.members.filter(m => m.id !== memberToMove.id);
            targetTeam.members.push(memberToMove);
          }
        }
      }
    }
    
    return rebalanced;
  }
}
```

This agent selection framework ensures optimal team composition for development strategies, leveraging the claude-code-flow patterns for hierarchical team organization and intelligent role assignment.