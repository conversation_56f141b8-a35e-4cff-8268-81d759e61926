# Research Strategy

## Purpose and Use Cases

The Research strategy focuses on comprehensive information gathering, analysis, and knowledge synthesis. Swarms operating under this strategy excel at exploring complex topics, evaluating options, and producing actionable insights through collaborative investigation.

### Primary Use Cases
- Technology evaluation and selection
- Market research and competitive analysis
- Best practices investigation
- Problem space exploration
- Literature reviews and synthesis

## Key Behaviors and Characteristics

### Core Behaviors
- **Systematic Exploration**: Methodical investigation approach
- **Multi-Source Validation**: Cross-reference findings
- **Collaborative Synthesis**: Combine diverse insights
- **Critical Analysis**: Evaluate information quality
- **Knowledge Building**: Create comprehensive understanding

### Unique Characteristics
- Emphasis on thoroughness over speed
- Multiple perspective integration
- Evidence-based conclusions
- Iterative refinement process
- Clear documentation focus

## When to Use This Strategy

Deploy Research strategy when:
- Entering new problem domains
- Making critical technology decisions
- Understanding complex systems
- Evaluating multiple alternatives
- Building knowledge foundations

## Integration Points

### Agent Composition
- **Primary**: Researcher agents for investigation
- **Supporting**: Analyzer agents for deep dives
- **Optional**: Documenter agents for synthesis
- **Coordination**: Orchestrator for direction
- **Memory**: Memory Manager for knowledge storage

### Workflow Patterns
- Parallel investigation tracks
- Convergent synthesis phases
- Iterative refinement cycles
- Peer review processes
- Knowledge consolidation

## Success Criteria

Research strategy succeeds when:
1. **Coverage**: Comprehensive topic exploration
2. **Quality**: Reliable, verified information
3. **Insights**: Actionable recommendations
4. **Documentation**: Clear knowledge capture
5. **Efficiency**: Timely completion

## Best Practices

1. Define clear research objectives
2. Establish quality criteria upfront
3. Use diverse information sources
4. Implement peer validation
5. Document methodology clearly
6. Create actionable outputs

## Anti-Patterns to Avoid

- Endless Research: Set clear boundaries
- Single Source: Diversify inputs
- Raw Data Dumps: Synthesize findings
- Bias Confirmation: Seek contrary views
- Poor Documentation: Capture process
- Isolated Work: Collaborate actively

## Research Methodologies

### Investigation Approaches
- **Systematic Review**: Comprehensive analysis
- **Comparative Study**: Side-by-side evaluation
- **Case Study**: Deep specific examples
- **Meta-Analysis**: Aggregate findings
- **Trend Analysis**: Pattern identification

### Quality Assurance
- Source credibility assessment
- Cross-validation techniques
- Peer review processes
- Bias identification
- Confidence scoring

## Output Formats

Research strategy produces:
- Executive summaries
- Detailed research reports
- Comparison matrices
- Decision frameworks
- Best practice guides
- Technology assessments
- Risk analyses

## Coordination Patterns

### Effective Modes
- **Distributed**: For broad exploration
- **Hierarchical**: For organized synthesis
- **Mesh**: For collaborative analysis
- **Hybrid**: For adaptive research

### Team Dynamics
- Lead researchers set direction
- Specialist agents deep-dive
- Analysts validate findings
- Documenters capture knowledge
- Coordinators ensure coverage

The Research strategy transforms uncertainty into understanding through systematic, collaborative investigation that produces reliable, actionable knowledge.

## Rust Implementation Examples

### Parallel Investigation with Multi-Source Validation

This example demonstrates how researcher agents can investigate multiple sources in parallel and validate findings:

```rust
use tokio::sync::{mpsc, broadcast};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

// Research finding from an agent
#[derive(Clone, Debug)]
struct ResearchFinding {
    agent_id: String,
    source: String,
    topic: String,
    confidence: f32,
    data: String,
}

// Shared knowledge base for synthesis
type KnowledgeBase = Arc<RwLock<HashMap<String, Vec<ResearchFinding>>>>;

pub struct ResearchCoordinator {
    findings_tx: broadcast::Sender<ResearchFinding>,
    knowledge_base: KnowledgeBase,
}

impl ResearchCoordinator {
    pub fn new() -> Self {
        let (findings_tx, _) = broadcast::channel(100);
        let knowledge_base = Arc::new(RwLock::new(HashMap::new()));
        
        Self { findings_tx, knowledge_base }
    }
    
    // Spawn researcher agents for parallel investigation
    pub async fn launch_research(&self, topics: Vec<String>) {
        for (id, topic) in topics.into_iter().enumerate() {
            let agent_id = format!("researcher_{}", id);
            let topic_clone = topic.clone();
            let findings_tx = self.findings_tx.clone();
            
            // Each researcher investigates independently
            tokio::spawn(async move {
                // Simulate research across multiple sources
                for source_id in 0..3 {
                    let finding = ResearchFinding {
                        agent_id: agent_id.clone(),
                        source: format!("source_{}", source_id),
                        topic: topic_clone.clone(),
                        confidence: 0.8 + (source_id as f32 * 0.05),
                        data: format!("Research data about {}", topic_clone),
                    };
                    
                    let _ = findings_tx.send(finding);
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                }
            });
        }
    }
}
```

### Convergent Synthesis with Validation

This pattern shows how multiple analyzer agents validate and synthesize findings:

```rust
use tokio::sync::{Barrier, Mutex};
use std::sync::Arc;

pub struct SynthesisEngine {
    validators: Vec<ValidatorAgent>,
    barrier: Arc<Barrier>,
    synthesis_result: Arc<Mutex<Option<String>>>,
}

struct ValidatorAgent {
    id: String,
    specialization: String,
}

impl SynthesisEngine {
    pub async fn synthesize(&self, findings: Vec<ResearchFinding>) -> Result<String, String> {
        let findings = Arc::new(findings);
        let validation_results = Arc::new(Mutex::new(Vec::new()));
        
        // Phase 1: Parallel validation
        let mut handles = vec![];
        
        for validator in &self.validators {
            let findings_clone = findings.clone();
            let results_clone = validation_results.clone();
            let barrier = self.barrier.clone();
            let validator_id = validator.id.clone();
            
            let handle = tokio::spawn(async move {
                // Validate findings based on specialization
                let mut validated = vec![];
                for finding in findings_clone.iter() {
                    if finding.confidence > 0.7 {
                        validated.push(finding.clone());
                    }
                }
                
                results_clone.lock().await.push((validator_id, validated));
                
                // Wait for all validators
                barrier.wait().await;
            });
            
            handles.push(handle);
        }
        
        // Wait for all validation tasks
        for handle in handles {
            handle.await.unwrap();
        }
        
        // Phase 2: Synthesis (single coordinator)
        let all_validations = validation_results.lock().await;
        let synthesis = self.merge_validated_findings(&all_validations);
        
        Ok(synthesis)
    }
    
    fn merge_validated_findings(&self, validations: &[(String, Vec<ResearchFinding>)]) -> String {
        // Cross-reference and merge validated findings
        "Synthesized research report".to_string()
    }
}
```

### Actor-Based Research Agents

This demonstrates the actor pattern for autonomous research agents:

```rust
use tokio::sync::mpsc;

enum ResearchCommand {
    Investigate { topic: String, depth: u8 },
    ValidateFinding { finding: ResearchFinding },
    ShareKnowledge { with_agent: String },
    Synthesize,
}

struct ResearcherActor {
    id: String,
    inbox: mpsc::Receiver<ResearchCommand>,
    peers: HashMap<String, mpsc::Sender<ResearchCommand>>,
    knowledge: Vec<ResearchFinding>,
}

impl ResearcherActor {
    pub async fn run(mut self) {
        while let Some(cmd) = self.inbox.recv().await {
            match cmd {
                ResearchCommand::Investigate { topic, depth } => {
                    println!("Agent {} investigating: {}", self.id, topic);
                    
                    // Simulate research work
                    tokio::time::sleep(tokio::time::Duration::from_millis(depth as u64 * 100)).await;
                    
                    let finding = ResearchFinding {
                        agent_id: self.id.clone(),
                        source: "research".to_string(),
                        topic,
                        confidence: 0.85,
                        data: "Discovered insights...".to_string(),
                    };
                    
                    self.knowledge.push(finding.clone());
                    
                    // Share with peers for validation
                    for (peer_id, peer_tx) in &self.peers {
                        let _ = peer_tx.send(ResearchCommand::ValidateFinding { 
                            finding: finding.clone() 
                        }).await;
                    }
                }
                
                ResearchCommand::ValidateFinding { finding } => {
                    // Cross-validate with own knowledge
                    let is_valid = self.validate_against_knowledge(&finding);
                    if is_valid {
                        self.knowledge.push(finding);
                    }
                }
                
                ResearchCommand::Synthesize => {
                    // Produce synthesis from accumulated knowledge
                    println!("Agent {} synthesizing {} findings", self.id, self.knowledge.len());
                }
                
                _ => {}
            }
        }
    }
    
    fn validate_against_knowledge(&self, finding: &ResearchFinding) -> bool {
        // Validation logic
        finding.confidence > 0.6
    }
}
```

### Knowledge Consolidation Pattern

This pattern shows how to consolidate research findings using a watch channel for real-time updates:

```rust
use tokio::sync::watch;

#[derive(Clone)]
struct ResearchProgress {
    topics_explored: usize,
    findings_count: usize,
    confidence_avg: f32,
    synthesis_ready: bool,
}

pub struct ResearchMonitor {
    progress_tx: watch::Sender<ResearchProgress>,
    progress_rx: watch::Receiver<ResearchProgress>,
}

impl ResearchMonitor {
    pub fn new() -> Self {
        let initial = ResearchProgress {
            topics_explored: 0,
            findings_count: 0,
            confidence_avg: 0.0,
            synthesis_ready: false,
        };
        
        let (tx, rx) = watch::channel(initial);
        Self { progress_tx: tx, progress_rx: rx }
    }
    
    // Coordinator updates progress
    pub async fn update_progress(&self, findings: &[ResearchFinding]) {
        let topics: std::collections::HashSet<_> = findings.iter()
            .map(|f| &f.topic)
            .collect();
        
        let confidence_sum: f32 = findings.iter()
            .map(|f| f.confidence)
            .sum();
        
        let progress = ResearchProgress {
            topics_explored: topics.len(),
            findings_count: findings.len(),
            confidence_avg: if findings.is_empty() { 0.0 } else { confidence_sum / findings.len() as f32 },
            synthesis_ready: findings.len() >= 10 && confidence_sum / findings.len() as f32 > 0.75,
        };
        
        let _ = self.progress_tx.send(progress);
    }
    
    // Agents can monitor progress
    pub async fn await_synthesis_ready(&mut self) {
        while self.progress_rx.changed().await.is_ok() {
            let progress = self.progress_rx.borrow();
            if progress.synthesis_ready {
                println!("Research complete! {} topics, {} findings, {:.2} avg confidence",
                    progress.topics_explored,
                    progress.findings_count,
                    progress.confidence_avg
                );
                break;
            }
        }
    }
}
```

### Key Implementation Notes

1. **Parallel Investigation**: Use `tokio::spawn` for concurrent research tasks
2. **Multi-Source Validation**: Broadcast channels enable all agents to see findings
3. **Convergent Synthesis**: Barriers synchronize phases, ensuring all research completes before synthesis
4. **Actor Pattern**: Each researcher maintains its own state and communicates via messages
5. **Progress Monitoring**: Watch channels provide real-time visibility into research progress

These patterns ensure the Research strategy maintains its core characteristics of thoroughness, validation, and collaborative synthesis while leveraging Rust's async capabilities for efficient parallel execution.