# Research Strategy Agent Selection

## Agent Selection Algorithm

The Research strategy employs a sophisticated agent selection algorithm that matches agent capabilities to investigation requirements. Based on claude-code-flow implementation patterns, it uses ML-inspired scoring for optimal agent assignment.

## Agent Type Definitions

### Primary Research Agents

```typescript
// Research strategy agent type definitions from claude-code-flow
interface ResearchAgentTypes {
  researcher: {
    capabilities: ['information-gathering', 'source-analysis', 'data-collection'],
    specializations: ['web-research', 'literature-review', 'fact-checking'],
    workload: 'parallel-investigation',
    coordination: 'distributed'
  };
  
  analyzer: {
    capabilities: ['data-analysis', 'pattern-recognition', 'comparative-analysis'],
    specializations: ['trend-analysis', 'correlation-detection', 'statistical-analysis'],
    workload: 'deep-analysis', 
    coordination: 'hierarchical'
  };
  
  documenter: {
    capabilities: ['knowledge-synthesis', 'report-generation', 'documentation'],
    specializations: ['technical-writing', 'summary-creation', 'structured-output'],
    workload: 'consolidation',
    coordination: 'centralized'
  };
  
  reviewer: {
    capabilities: ['quality-assessment', 'validation', 'peer-review'],
    specializations: ['source-credibility', 'fact-verification', 'bias-detection'],
    workload: 'validation',
    coordination: 'mesh'
  };
}
```

## Agent Spawning Logic

### Research-Specific Agent Selection

```typescript
// Agent selection algorithm for research strategy
class ResearchAgentSelector {
  async selectAgentsForResearch(
    objective: SwarmObjective, 
    tracks: InvestigationTrack[]
  ): Promise<AgentSelection> {
    const selection: AgentSelection = {
      primary: [],
      supporting: [],
      coordinator: null,
      total: 0
    };
    
    // Calculate required agent types based on research complexity
    const requirements = this.analyzeResearchRequirements(objective, tracks);
    
    // Primary researchers (always required)
    const researcherCount = this.calculateResearcherCount(tracks);
    selection.primary.push({
      type: 'researcher',
      count: researcherCount,
      priority: 'high',
      specialization: this.selectResearchSpecialization(objective)
    });
    
    // Analyzers for comparative research
    if (requirements.needsComparativeAnalysis) {
      selection.primary.push({
        type: 'analyzer',
        count: Math.min(2, Math.ceil(tracks.length / 3)),
        priority: 'high',
        specialization: 'comparative-analysis'
      });
    }
    
    // Documenters for synthesis
    if (requirements.needsSynthesis) {
      selection.supporting.push({
        type: 'documenter', 
        count: 1,
        priority: 'medium',
        specialization: 'knowledge-synthesis'
      });
    }
    
    // Reviewers for validation
    if (requirements.needsValidation) {
      selection.supporting.push({
        type: 'reviewer',
        count: Math.min(2, Math.ceil(researcherCount / 2)),
        priority: 'high',
        specialization: 'source-credibility'
      });
    }
    
    // Coordinator selection
    selection.coordinator = this.selectCoordinator(selection);
    
    return selection;
  }
  
  private calculateResearcherCount(tracks: InvestigationTrack[]): number {
    // Base count on investigation tracks
    const baseCount = Math.min(tracks.length, 4);
    
    // Increase for broad exploration research
    const exploratoryTracks = tracks.filter(t => t.type === 'exploratory').length;
    const additionalCount = Math.ceil(exploratoryTracks / 2);
    
    return Math.min(baseCount + additionalCount, 8); // Max 8 researchers
  }
}
```

## Agent Capability Matching

### ML-Inspired Agent Scoring

```typescript
// Agent scoring algorithm based on claude-code-flow patterns
class ResearchAgentScoring {
  async scoreAgentForTask(
    agent: AgentProfile, 
    task: ResearchTask,
    context: ResearchContext
  ): Promise<AgentScore> {
    const scores = await Promise.all([
      this.calculateCapabilityMatch(agent, task),        // 40% weight
      this.calculatePerformanceHistory(agent, 'research'), // 30% weight  
      this.calculateWorkloadBalance(agent),              // 20% weight
      this.calculateSpecializationMatch(agent, task)     // 10% weight
    ]);
    
    return this.aggregateScores(scores, [0.4, 0.3, 0.2, 0.1]);
  }
  
  private async calculateCapabilityMatch(
    agent: AgentProfile, 
    task: ResearchTask
  ): Promise<number> {
    const requiredCapabilities = task.requiredCapabilities;
    const agentCapabilities = agent.capabilities;
    
    // Calculate capability overlap
    const intersection = requiredCapabilities.filter(cap => 
      agentCapabilities.includes(cap)
    );
    
    const matchScore = intersection.length / requiredCapabilities.length;
    
    // Bonus for specialized research capabilities
    const specializationBonus = this.calculateSpecializationBonus(agent, task);
    
    return Math.min(matchScore + specializationBonus, 1.0);
  }
  
  private async calculatePerformanceHistory(
    agent: AgentProfile, 
    strategy: string
  ): Promise<number> {
    const history = await this.getAgentHistory(agent.id, strategy);
    
    if (history.length === 0) return 0.5; // Neutral score for new agents
    
    const metrics = {
      successRate: history.filter(h => h.success).length / history.length,
      avgQuality: history.reduce((sum, h) => sum + h.qualityScore, 0) / history.length,
      avgSpeed: history.reduce((sum, h) => sum + h.speedScore, 0) / history.length
    };
    
    // Weight: 50% success rate, 30% quality, 20% speed
    return (metrics.successRate * 0.5) + (metrics.avgQuality * 0.3) + (metrics.avgSpeed * 0.2);
  }
}
```

## Agent Assignment Patterns

### Dynamic Agent Assignment

```typescript
// Dynamic agent assignment for research tasks
class ResearchAgentAssignment {
  async assignAgentsToTracks(
    agents: AgentProfile[],
    tracks: InvestigationTrack[]
  ): Promise<AssignmentPlan> {
    const plan: AssignmentPlan = {
      assignments: new Map(),
      loadBalancing: new Map(),
      dependencies: new Map()
    };
    
    // Phase 1: Assign primary researchers to exploratory tracks
    const exploratoryTracks = tracks.filter(t => t.type === 'exploratory');
    const researchers = agents.filter(a => a.type === 'researcher');
    
    for (const track of exploratoryTracks) {
      const bestResearcher = await this.selectBestAgent(researchers, track);
      plan.assignments.set(track.id, [bestResearcher.id]);
      this.updateAgentLoad(plan.loadBalancing, bestResearcher.id, track);
    }
    
    // Phase 2: Assign analyzers to comparative tracks
    const comparativeTracks = tracks.filter(t => t.type === 'comparative');
    const analyzers = agents.filter(a => a.type === 'analyzer');
    
    for (const track of comparativeTracks) {
      const bestAnalyzer = await this.selectBestAgent(analyzers, track);
      plan.assignments.set(track.id, [bestAnalyzer.id]);
      plan.dependencies.set(track.id, exploratoryTracks.map(t => t.id));
    }
    
    // Phase 3: Assign reviewers for validation
    const validationTracks = tracks.filter(t => t.type === 'evaluative');
    const reviewers = agents.filter(a => a.type === 'reviewer');
    
    for (const track of validationTracks) {
      const assignedReviewers = await this.selectMultipleAgents(reviewers, track, 2);
      plan.assignments.set(track.id, assignedReviewers.map(r => r.id));
    }
    
    return plan;
  }
  
  private async selectBestAgent(
    candidates: AgentProfile[],
    track: InvestigationTrack
  ): Promise<AgentProfile> {
    const scores = await Promise.all(
      candidates.map(async agent => ({
        agent,
        score: await this.scoreAgentForTrack(agent, track)
      }))
    );
    
    // Sort by score and return best match
    scores.sort((a, b) => b.score - a.score);
    return scores[0].agent;
  }
}
```

## Agent Specialization Mapping

### Research Domain Specializations

```typescript
// Agent specialization for different research domains
interface ResearchSpecializations {
  'technology-research': {
    agents: ['researcher', 'analyzer'],
    focus: 'technical-evaluation',
    sources: ['documentation', 'benchmarks', 'case-studies'],
    outputs: ['comparison-matrices', 'technical-assessments']
  };
  
  'market-research': {
    agents: ['researcher', 'analyzer', 'documenter'],
    focus: 'market-analysis', 
    sources: ['reports', 'surveys', 'statistics'],
    outputs: ['market-reports', 'trend-analysis']
  };
  
  'academic-research': {
    agents: ['researcher', 'reviewer', 'documenter'],
    focus: 'literature-review',
    sources: ['papers', 'journals', 'citations'],
    outputs: ['literature-reviews', 'meta-analyses']
  };
  
  'competitive-research': {
    agents: ['researcher', 'analyzer'],
    focus: 'competitive-intelligence',
    sources: ['public-data', 'announcements', 'products'],
    outputs: ['competitive-matrices', 'gap-analyses']
  };
}

class ResearchSpecializationMapper {
  mapSpecializationToAgents(
    researchType: string,
    objective: SwarmObjective
  ): AgentSpecializationMap {
    const specialization = this.detectResearchSpecialization(objective);
    const spec = ResearchSpecializations[specialization];
    
    return {
      requiredAgentTypes: spec.agents,
      focusArea: spec.focus,
      expectedSources: spec.sources,
      targetOutputs: spec.outputs,
      coordination: this.selectCoordinationForSpecialization(specialization)
    };
  }
  
  private detectResearchSpecialization(objective: SwarmObjective): string {
    const description = objective.description.toLowerCase();
    
    if (description.includes('market') || description.includes('business')) {
      return 'market-research';
    }
    if (description.includes('technology') || description.includes('technical')) {
      return 'technology-research';
    }
    if (description.includes('academic') || description.includes('literature')) {
      return 'academic-research';
    }
    if (description.includes('competitive') || description.includes('competitor')) {
      return 'competitive-research';
    }
    
    return 'technology-research'; // Default fallback
  }
}
```

## Load Balancing and Optimization

### Research Agent Load Balancing

```typescript
// Load balancing for research agents
class ResearchLoadBalancer {
  optimizeAgentLoad(
    assignments: Map<string, string[]>,
    agents: AgentProfile[]
  ): OptimizedAssignment {
    const loadMap = new Map<string, number>();
    
    // Calculate current load per agent
    for (const [trackId, agentIds] of assignments) {
      for (const agentId of agentIds) {
        const currentLoad = loadMap.get(agentId) || 0;
        loadMap.set(agentId, currentLoad + 1);
      }
    }
    
    // Identify overloaded and underutilized agents
    const avgLoad = Array.from(loadMap.values()).reduce((sum, load) => sum + load, 0) / agents.length;
    const overloaded = Array.from(loadMap.entries()).filter(([_, load]) => load > avgLoad * 1.5);
    const underutilized = Array.from(loadMap.entries()).filter(([_, load]) => load < avgLoad * 0.5);
    
    // Rebalance assignments
    return this.rebalanceAssignments(assignments, overloaded, underutilized);
  }
  
  private rebalanceAssignments(
    assignments: Map<string, string[]>,
    overloaded: [string, number][],
    underutilized: [string, number][]
  ): OptimizedAssignment {
    const optimized = new Map(assignments);
    
    for (const [overloadedAgent, _] of overloaded) {
      for (const [underutilizedAgent, _] of underutilized) {
        // Find tracks where we can transfer load
        const transferCandidate = this.findTransferCandidate(
          optimized, 
          overloadedAgent, 
          underutilizedAgent
        );
        
        if (transferCandidate) {
          this.transferAssignment(optimized, transferCandidate, overloadedAgent, underutilizedAgent);
          break;
        }
      }
    }
    
    return { assignments: optimized, improved: true };
  }
}
```

This agent selection framework ensures optimal agent assignment for research strategies, leveraging the claude-code-flow patterns for intelligent agent matching and dynamic load balancing.