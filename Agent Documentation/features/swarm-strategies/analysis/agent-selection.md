# Analysis Strategy Agent Selection

## Agent Selection Algorithm

The Analysis strategy employs collaborative agent selection optimized for data examination and pattern recognition. Based on claude-code-flow implementation patterns, it uses mesh coordination for peer validation and distributed coordination for parallel data processing.

## Agent Type Definitions

### Analysis Team Agent Types

```typescript
// Analysis strategy agent type definitions from claude-code-flow
interface AnalysisAgentTypes {
  analyzer: {
    capabilities: ['data-analysis', 'statistical-modeling', 'pattern-recognition'],
    specializations: ['exploratory-analysis', 'statistical-analysis', 'predictive-modeling'],
    workload: 'analytical-processing',
    coordination: 'mesh'
  };
  
  documenter: {
    capabilities: ['insight-synthesis', 'report-generation', 'visualization'],
    specializations: ['data-visualization', 'technical-writing', 'executive-summaries'],
    workload: 'knowledge-synthesis',
    coordination: 'centralized'
  };
  
  reviewer: {
    capabilities: ['validation', 'peer-review', 'quality-assessment'],
    specializations: ['methodology-review', 'statistical-validation', 'bias-detection'],
    workload: 'quality-assurance',
    coordination: 'mesh'
  };
}

class AnalysisAgentSelector {
  async selectAgentsForAnalysis(
    objective: SwarmObjective,
    dimensions: AnalysisDimension[]
  ): Promise<AgentSelection> {
    const selection: AgentSelection = {
      primary: [],
      supporting: [],
      coordinator: null,
      total: 0
    };
    
    // Primary analyzers (always required)
    const analyzerCount = this.calculateAnalyzerCount(dimensions);
    selection.primary.push({
      type: 'analyzer',
      count: analyzerCount,
      priority: 'high',
      specialization: this.selectAnalysisSpecialization(objective)
    });
    
    // Supporting roles based on analysis complexity
    const requirements = this.analyzeAnalysisRequirements(objective, dimensions);
    
    if (requirements.needsVisualization) {
      selection.supporting.push({
        type: 'documenter',
        count: 1,
        priority: 'medium',
        specialization: 'data-visualization'
      });
    }
    
    if (requirements.needsValidation) {
      selection.supporting.push({
        type: 'reviewer',
        count: Math.ceil(analyzerCount / 2),
        priority: 'high',
        specialization: 'statistical-validation'
      });
    }
    
    return selection;
  }
}
```

## Agent Capability Matching

### Analysis-Specific Agent Scoring

```typescript
// Agent scoring for analysis tasks
class AnalysisAgentScoring {
  async scoreAgentForAnalysisTask(
    agent: AgentProfile,
    task: AnalysisTask,
    context: AnalysisContext
  ): Promise<AgentScore> {
    const scores = await Promise.all([
      this.calculateAnalyticalCapability(agent, task),      // 40% weight
      this.calculateDomainExpertise(agent, task),           // 25% weight
      this.calculateCollaborationSkills(agent, context),    // 20% weight
      this.calculateToolProficiency(agent, task),           // 10% weight
      this.calculateMethodologyExperience(agent, task)      // 5% weight
    ]);
    
    return this.aggregateScores(scores, [0.40, 0.25, 0.20, 0.10, 0.05]);
  }
}
```

This agent selection framework ensures optimal team composition for analysis strategies through collaborative mesh coordination and intelligent specialization matching.