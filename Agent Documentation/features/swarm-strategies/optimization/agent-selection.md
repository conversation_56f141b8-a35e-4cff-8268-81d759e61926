# Optimization Strategy Agent Selection

## Agent Selection Algorithm

The Optimization strategy employs performance-focused agent selection optimized for systematic improvement. Based on claude-code-flow patterns, it uses hybrid coordination for adaptive optimization and mesh coordination for collaborative performance analysis.

## Optimization Agent Types

```typescript
// Optimization strategy agent type definitions from claude-code-flow
interface OptimizationAgentTypes {
  optimizer: {
    capabilities: ['performance-analysis', 'bottleneck-identification', 'optimization-implementation'],
    specializations: ['algorithm-optimization', 'system-optimization', 'database-optimization'],
    workload: 'performance-improvement',
    coordination: 'mesh'
  };
  
  analyzer: {
    capabilities: ['performance-profiling', 'metrics-analysis', 'impact-assessment'],
    specializations: ['performance-monitoring', 'statistical-analysis', 'trend-analysis'],
    workload: 'performance-analysis',
    coordination: 'distributed'
  };
  
  coder: {
    capabilities: ['code-optimization', 'refactoring', 'implementation'],
    specializations: ['algorithm-improvement', 'data-structure-optimization', 'caching'],
    workload: 'code-improvement',
    coordination: 'hierarchical'
  };
}

class OptimizationAgentSelector {
  async selectAgentsForOptimization(
    objective: SwarmObjective,
    dimensions: OptimizationDimension[]
  ): Promise<AgentSelection> {
    const optimizerCount = this.calculateOptimizerCount(dimensions);
    
    return {
      primary: [{
        type: 'optimizer',
        count: optimizerCount,
        priority: 'high',
        specialization: this.selectOptimizationSpecialization(objective)
      }],
      supporting: [
        {
          type: 'analyzer',
          count: Math.ceil(optimizerCount / 2),
          priority: 'high',
          specialization: 'performance-profiling'
        },
        {
          type: 'coder',
          count: Math.ceil(optimizerCount / 3),
          priority: 'medium',
          specialization: 'algorithm-optimization'
        }
      ]
    };
  }
}
```

This agent selection framework ensures comprehensive optimization coverage through adaptive coordination and collaborative performance analysis.