# Tester Mode

## Purpose and Use Cases

The Tester mode specializes in comprehensive testing, validation, and quality assurance. Tester agents ensure system reliability through systematic testing across multiple dimensions including functionality, performance, security, and usability.

### Primary Use Cases
- Executing comprehensive test suites
- Performing integration and system testing
- Conducting performance and load testing
- Validating security requirements
- Ensuring user experience quality

## Rust Code Examples

### SPARC Tester Mode Trait Definition

```rust
// Example: Tester Mode trait for SPARC system
pub trait TesterMode: Send + Sync {
    fn name(&self) -> &'static str { "Tester" }
    fn description(&self) -> &'static str { 
        "Comprehensive testing and quality assurance mode"
    }
    
    // Core testing methods
    async fn plan_test_campaign(&mut self, requirements: &Requirements) -> Result<TestPlan, ModeError>;
    async fn execute_tests(&mut self, test_suite: &TestSuite) -> Result<TestResults, ModeError>;
    async fn analyze_test_coverage(&mut self, code: &Code) -> Result<CoverageReport, ModeError>;
    
    // Specialized testing
    async fn performance_test(&mut self, scenarios: &[LoadScenario]) -> Result<PerformanceReport, ModeError>;
    async fn security_test(&mut self, security_rules: &SecurityRules) -> Result<SecurityReport, ModeError>;
}

// Test planning structures
#[derive(Debug, Clone)]
pub struct TestPlan {
    pub test_strategies: Vec<TestStrategy>,
    pub test_cases: Vec<TestCase>,
    pub execution_order: Vec<TestPhase>,
    pub resource_requirements: ResourceRequirements,
    pub risk_assessment: RiskAssessment,
}

#[derive(Debug, Clone)]
pub enum TestStrategy {
    Functional { coverage_target: f64 },
    Integration { interfaces: Vec<Interface> },
    Performance { sla_requirements: SLA },
    Security { threat_model: ThreatModel },
    Usability { user_scenarios: Vec<UserScenario> },
    Regression { baseline: TestBaseline },
}
```

### Test Execution State Machine

```rust
// State machine for test execution process
#[derive(Debug, Clone)]
pub enum TesterState {
    Planning {
        requirements: Requirements,
        risk_analysis: Option<RiskAnalysis>,
    },
    Preparing {
        test_plan: TestPlan,
        environment_setup: EnvironmentSetup,
    },
    Executing {
        current_phase: TestPhase,
        completed_tests: Vec<TestResult>,
        remaining_tests: Vec<TestCase>,
    },
    Analyzing {
        test_results: TestResults,
        coverage_data: CoverageData,
    },
    Reporting {
        analysis: TestAnalysis,
        defects: Vec<Defect>,
    },
    Complete {
        final_report: QualityReport,
        recommendations: Vec<Recommendation>,
    },
}

impl TesterState {
    pub fn transition(&mut self, event: TestEvent) -> Result<(), StateError> {
        match (self.clone(), event) {
            (TesterState::Planning { requirements, .. }, 
             TestEvent::PlanCreated(plan)) => {
                *self = TesterState::Preparing {
                    test_plan: plan,
                    environment_setup: EnvironmentSetup::new(),
                };
                Ok(())
            }
            (TesterState::Preparing { test_plan, .. }, 
             TestEvent::EnvironmentReady) => {
                let (first_phase, test_cases) = test_plan.first_phase();
                *self = TesterState::Executing {
                    current_phase: first_phase,
                    completed_tests: Vec::new(),
                    remaining_tests: test_cases,
                };
                Ok(())
            }
            // ... other transitions
            _ => Err(StateError::InvalidTransition),
        }
    }
}
```

## Key Behaviors and Characteristics

### Core Behaviors
- **Test Planning**: Creates comprehensive test strategies
- **Test Execution**: Runs systematic test campaigns
- **Defect Reporting**: Documents issues clearly
- **Coverage Analysis**: Ensures thorough testing
- **Risk Assessment**: Prioritizes critical paths

### Unique Characteristics
- Attention to detail and edge cases
- User-centric thinking
- Strong analytical skills
- Patience and persistence
- Clear communication of issues

## When to Use This Mode

Deploy Tester agents when:
- Features are ready for validation
- System integration points need testing
- Performance benchmarks are required
- Security validation is needed
- User acceptance testing begins

## Integration Points

### Works Well With
- **TDD**: Extends unit tests with integration tests
- **Coder**: Validates implementations
- **Debugger**: Reports issues for investigation
- **Reviewer**: Shares test results
- **Orchestrator**: Provides quality metrics

### Communication Patterns
- Receives test requirements from orchestrators
- Reports defects to debuggers
- Collaborates with coders on test data
- Shares results via memory system
- Updates team on quality status

## Success Criteria

Tester success is measured by:
1. **Defect Detection**: Finding issues before production
2. **Test Coverage**: Comprehensive validation
3. **Test Efficiency**: Optimal test execution time
4. **Clear Reporting**: Actionable defect reports
5. **Risk Mitigation**: Critical paths validated

## Best Practices

1. Start with test planning and strategy
2. Prioritize tests based on risk
3. Use both manual and automated testing
4. Create realistic test scenarios
5. Document test cases clearly
6. Maintain test data integrity

## Anti-Patterns to Avoid

- Testing Only Happy Paths: Include edge cases
- Ignoring Non-Functional: Test performance, security
- Poor Bug Reports: Provide reproduction steps
- Testing in Isolation: Consider integrations
- Stale Test Data: Keep data current
- Skipping Regression: Always retest fixes

## Testing Types

The Tester mode performs:
- **Functional Testing**: Feature validation
- **Integration Testing**: Component interactions
- **Performance Testing**: Load, stress, endurance
- **Security Testing**: Vulnerability assessment
- **Usability Testing**: User experience validation
- **Regression Testing**: Ensuring stability

## Test Strategies

Testers employ:
- **Risk-Based Testing**: Focus on critical areas
- **Exploratory Testing**: Discover unknown issues
- **Boundary Testing**: Edge case validation
- **Equivalence Partitioning**: Efficient coverage
- **State Transition**: Complex workflow testing
- **Model-Based Testing**: Systematic coverage

## Quality Metrics

Testers track:
- Test coverage percentages
- Defect detection rates
- Test execution times
- Defect severity distribution
- Test automation ratios
- Mean time to detect

The Tester mode ensures comprehensive quality validation, protecting users from defects and maintaining system reliability through rigorous testing practices.

## Advanced Testing Patterns

### Test Generation Framework

```rust
// Example: Intelligent test generation system
pub struct TestGenerator {
    code_analyzer: CodeAnalyzer,
    test_patterns: TestPatternLibrary,
    coverage_optimizer: CoverageOptimizer,
}

impl TestGenerator {
    pub async fn generate_tests(
        &self,
        component: &Component,
        strategy: &TestStrategy,
    ) -> Result<Vec<TestCase>, GenerationError> {
        let mut test_cases = Vec::new();
        
        // Analyze component structure
        let analysis = self.code_analyzer.analyze(component).await?;
        
        // Generate tests based on strategy
        match strategy {
            TestStrategy::Functional { coverage_target } => {
                // Generate edge case tests
                test_cases.extend(self.generate_edge_cases(&analysis)?);
                
                // Generate boundary value tests
                test_cases.extend(self.generate_boundary_tests(&analysis)?);
                
                // Generate equivalence class tests
                test_cases.extend(self.generate_equivalence_tests(&analysis)?);
                
                // Optimize for coverage
                test_cases = self.coverage_optimizer
                    .optimize_test_set(test_cases, *coverage_target)?;
            }
            TestStrategy::Integration { interfaces } => {
                // Generate interface contract tests
                for interface in interfaces {
                    test_cases.extend(self.generate_contract_tests(interface)?);
                }
                
                // Generate interaction sequence tests
                test_cases.extend(self.generate_interaction_tests(interfaces)?);
            }
            _ => {}
        }
        
        Ok(test_cases)
    }
    
    fn generate_edge_cases(&self, analysis: &ComponentAnalysis) -> Result<Vec<TestCase>, Error> {
        let mut tests = Vec::new();
        
        for method in &analysis.methods {
            // Null/empty input tests
            if method.accepts_nullable() {
                tests.push(TestCase {
                    name: format!("test_{}_with_null_input", method.name),
                    test_type: TestType::EdgeCase,
                    inputs: vec![TestInput::Null],
                    expected: ExpectedResult::Error(ErrorType::NullPointer),
                    // ...
                });
            }
            
            // Boundary value tests
            if let Some(numeric_params) = method.numeric_parameters() {
                for param in numeric_params {
                    tests.push(TestCase {
                        name: format!("test_{}_max_value_{}", method.name, param.name),
                        test_type: TestType::BoundaryValue,
                        inputs: vec![TestInput::MaxValue(param.type_info)],
                        expected: ExpectedResult::Success,
                        // ...
                    });
                }
            }
        }
        
        Ok(tests)
    }
}
```

### Performance Testing Engine

```rust
// Example: Sophisticated performance testing
pub struct PerformanceTester {
    load_generator: LoadGenerator,
    metrics_collector: MetricsCollector,
    performance_analyzer: PerformanceAnalyzer,
}

#[derive(Debug, Clone)]
pub struct LoadScenario {
    pub name: String,
    pub pattern: LoadPattern,
    pub duration: Duration,
    pub virtual_users: VirtualUserConfig,
    pub think_time: Distribution,
}

#[derive(Debug, Clone)]
pub enum LoadPattern {
    Constant { rate: f64 },
    Ramp { start: f64, end: f64, duration: Duration },
    Spike { baseline: f64, spike: f64, spike_duration: Duration },
    Wave { min: f64, max: f64, period: Duration },
    Custom { profile: Vec<(Duration, f64)> },
}

impl PerformanceTester {
    pub async fn run_performance_test(
        &mut self,
        system: &System,
        scenarios: &[LoadScenario],
    ) -> Result<PerformanceReport, TestError> {
        let mut results = Vec::new();
        
        for scenario in scenarios {
            // Setup monitoring
            self.metrics_collector.start_collection().await?;
            
            // Generate load
            let load_result = self.load_generator
                .generate_load(system, scenario)
                .await?;
                
            // Collect metrics
            let metrics = self.metrics_collector.stop_collection().await?;
            
            // Analyze performance
            let analysis = self.performance_analyzer
                .analyze(&load_result, &metrics)
                .await?;
                
            results.push(ScenarioResult {
                scenario: scenario.clone(),
                metrics: analysis.key_metrics(),
                bottlenecks: analysis.identify_bottlenecks(),
                recommendations: analysis.optimization_suggestions(),
            });
        }
        
        Ok(PerformanceReport {
            test_date: Utc::now(),
            scenarios: results,
            overall_assessment: self.assess_performance(&results),
        })
    }
}
```

### Test Coordination and Orchestration

```rust
// Example: Coordinating testing with other modes
pub struct TestCoordinator {
    tester_mode: Box<dyn TesterMode>,
    tdd_mode: Option<Box<dyn TDDMode>>,
    debugger_mode: Option<Box<dyn DebuggerMode>>,
    reviewer_mode: Option<Box<dyn ReviewerMode>>,
}

impl TestCoordinator {
    pub async fn comprehensive_quality_validation(
        &mut self,
        system: &System,
    ) -> Result<QualityValidation, CoordinationError> {
        // Step 1: Get test cases from TDD if available
        let mut test_suite = if let Some(tdd) = &mut self.tdd_mode {
            tdd.get_existing_tests().await?
        } else {
            TestSuite::new()
        };
        
        // Step 2: Plan additional tests
        let test_plan = self.tester_mode
            .plan_test_campaign(&system.requirements)
            .await?;
            
        test_suite.extend(test_plan.test_cases);
        
        // Step 3: Execute tests
        let test_results = self.tester_mode
            .execute_tests(&test_suite)
            .await?;
            
        // Step 4: Debug failures if debugger available
        let debug_reports = if let Some(debugger) = &mut self.debugger_mode {
            let mut reports = Vec::new();
            for failure in test_results.failures() {
                let report = debugger
                    .investigate_test_failure(failure)
                    .await?;
                reports.push(report);
            }
            Some(reports)
        } else {
            None
        };
        
        // Step 5: Review test quality if reviewer available
        let test_review = if let Some(reviewer) = &mut self.reviewer_mode {
            Some(reviewer.review_test_suite(&test_suite).await?)
        } else {
            None
        };
        
        Ok(QualityValidation {
            test_results,
            coverage_report: self.tester_mode.analyze_test_coverage(&system.code).await?,
            debug_findings: debug_reports,
            test_quality_assessment: test_review,
            overall_quality_score: self.calculate_quality_score(&test_results),
        })
    }
}
```

### Security Testing Framework

```rust
// Example: Security-focused testing
pub struct SecurityTester {
    vulnerability_scanner: VulnerabilityScanner,
    penetration_tester: PenetrationTester,
    compliance_checker: ComplianceChecker,
}

#[derive(Debug, Clone)]
pub struct SecurityTest {
    pub test_type: SecurityTestType,
    pub target: SecurityTarget,
    pub attack_vectors: Vec<AttackVector>,
    pub severity_threshold: Severity,
}

#[derive(Debug, Clone)]
pub enum SecurityTestType {
    StaticAnalysis,
    DynamicAnalysis,
    PenetrationTest,
    ComplianceAudit,
    ThreatModeling,
}

impl SecurityTester {
    pub async fn security_assessment(
        &mut self,
        system: &System,
        threat_model: &ThreatModel,
    ) -> Result<SecurityAssessment, SecurityError> {
        let mut vulnerabilities = Vec::new();
        
        // Static vulnerability scanning
        let static_vulns = self.vulnerability_scanner
            .scan_static(system.code)
            .await?;
        vulnerabilities.extend(static_vulns);
        
        // Dynamic security testing
        let dynamic_vulns = self.vulnerability_scanner
            .scan_dynamic(system.runtime)
            .await?;
        vulnerabilities.extend(dynamic_vulns);
        
        // Penetration testing
        let pen_test_results = self.penetration_tester
            .execute_attacks(system, threat_model)
            .await?;
            
        // Compliance checking
        let compliance_results = self.compliance_checker
            .check_compliance(system, &SecurityStandards::default())
            .await?;
            
        Ok(SecurityAssessment {
            vulnerabilities,
            penetration_test_results: pen_test_results,
            compliance_status: compliance_results,
            risk_score: self.calculate_risk_score(&vulnerabilities),
            remediation_plan: self.generate_remediation_plan(&vulnerabilities),
        })
    }
}
```

### Test Data Management

```rust
// Example: Sophisticated test data generation and management
pub struct TestDataManager {
    data_generator: DataGenerator,
    data_anonymizer: DataAnonymizer,
    data_validator: DataValidator,
}

impl TestDataManager {
    pub async fn prepare_test_data(
        &self,
        schema: &DataSchema,
        constraints: &DataConstraints,
    ) -> Result<TestDataSet, DataError> {
        // Generate synthetic data
        let synthetic_data = self.data_generator
            .generate_synthetic(schema, constraints)
            .await?;
            
        // Anonymize production data if needed
        let anonymized_data = if constraints.use_production_data {
            Some(self.data_anonymizer.anonymize_production_data().await?)
        } else {
            None
        };
        
        // Validate all test data
        let mut test_data = TestDataSet::new();
        
        for data in synthetic_data {
            if self.data_validator.validate(&data, schema).await? {
                test_data.add(data);
            }
        }
        
        if let Some(anon_data) = anonymized_data {
            test_data.merge(anon_data);
        }
        
        Ok(test_data)
    }
}
```