# Coder Mode

## Purpose and Use Cases

The Coder mode specializes in implementation, code generation, and hands-on development. Coder agents transform designs and requirements into working code, focusing on clean implementation, best practices, and efficient batch operations for maximum productivity.

### Primary Use Cases
- Feature implementation from specifications
- Code generation and scaffolding
- Refactoring and code improvement
- Bug fixes and patches
- API and integration development

## Rust Code Examples

### SPARC Coder Mode Trait Definition

```rust
// Example: Coder Mode trait for SPARC system
pub trait CoderMode: Send + Sync {
    fn name(&self) -> &'static str { "Coder" }
    fn description(&self) -> &'static str { 
        "Implementation and code generation mode with batch operations"
    }
    
    // Core coding methods
    async fn implement_feature(&mut self, spec: &FeatureSpec) -> Result<Implementation, ModeError>;
    async fn generate_code(&mut self, template: &CodeTemplate) -> Result<GeneratedCode, ModeError>;
    async fn refactor_code(&mut self, target: &RefactorTarget) -> Result<RefactoredCode, ModeError>;
    
    // Batch operations for efficiency
    async fn batch_implement(&mut self, specs: Vec<FeatureSpec>) -> Result<Vec<Implementation>, ModeError>;
    async fn batch_generate(&mut self, templates: Vec<CodeTemplate>) -> Result<Vec<GeneratedCode>, ModeError>;
}

// Code generation data structures
#[derive(Debug, Clone)]
pub struct Implementation {
    pub files: Vec<CodeFile>,
    pub tests: Vec<TestFile>,
    pub documentation: Vec<DocFile>,
    pub dependencies: Vec<Dependency>,
    pub integration_points: Vec<IntegrationPoint>,
}

#[derive(Debug, Clone)]
pub enum CodingTask {
    CreateModule {
        module_spec: ModuleSpecification,
        patterns: Vec<DesignPattern>,
    },
    ImplementAPI {
        api_spec: APISpecification,
        endpoints: Vec<Endpoint>,
    },
    BatchRefactor {
        targets: Vec<RefactorTarget>,
        strategy: RefactorStrategy,
    },
    GenerateBoilerplate {
        templates: Vec<Template>,
        customizations: HashMap<String, Value>,
    },
    FixBugs {
        bug_reports: Vec<BugReport>,
        test_cases: Vec<TestCase>,
    },
}
```

### Coder State Machine

```rust
// State machine for coding process
#[derive(Debug, Clone)]
pub enum CoderState {
    AnalyzingRequirements {
        specifications: Vec<Specification>,
        constraints: Vec<Constraint>,
    },
    PlanningImplementation {
        implementation_plan: ImplementationPlan,
        task_breakdown: Vec<Task>,
    },
    WritingCode {
        current_file: String,
        progress: f64,
        batch_queue: Vec<CodeTask>,
    },
    TestingImplementation {
        test_suite: TestSuite,
        coverage_target: f64,
    },
    RefactoringCode {
        refactor_targets: Vec<RefactorTarget>,
        quality_metrics: QualityMetrics,
    },
    DocumentingCode {
        doc_targets: Vec<DocumentationTarget>,
        doc_style: DocumentationStyle,
    },
    Complete {
        implementation: Implementation,
        metrics: ImplementationMetrics,
    },
}

impl CoderState {
    pub fn transition(&mut self, event: CodingEvent) -> Result<(), StateError> {
        match (self.clone(), event) {
            (CoderState::AnalyzingRequirements { specifications, .. }, 
             CodingEvent::RequirementsAnalyzed) => {
                *self = CoderState::PlanningImplementation {
                    implementation_plan: Self::create_plan(&specifications),
                    task_breakdown: Self::break_down_tasks(&specifications),
                };
                Ok(())
            }
            (CoderState::PlanningImplementation { implementation_plan, task_breakdown }, 
             CodingEvent::PlanComplete) => {
                *self = CoderState::WritingCode {
                    current_file: task_breakdown[0].file.clone(),
                    progress: 0.0,
                    batch_queue: Self::create_batch_queue(&task_breakdown),
                };
                Ok(())
            }
            // ... other transitions
            _ => Err(StateError::InvalidTransition),
        }
    }
}
```

## Key Behaviors and Characteristics

### Core Behaviors
- **Clean Code Writing**: Following best practices
- **Batch Processing**: Efficient multi-file operations
- **Pattern Application**: Using design patterns
- **Test Integration**: Writing tests alongside code
- **Incremental Development**: Small, focused changes

### Unique Characteristics
- Strong language proficiency
- Framework and library expertise
- Performance optimization skills
- Debugging and troubleshooting ability
- Code organization and structure sense

## When to Use This Mode

Deploy Coder agents when:
- Implementing new features or modules
- Refactoring existing codebases
- Creating API endpoints or services
- Fixing bugs and issues
- Generating boilerplate code

## Integration Points

### Works Well With
- **Architect**: Implements architectural designs
- **TDD**: Follows test-driven approach
- **Reviewer**: Receives code quality feedback
- **Debugger**: Collaborates on bug fixes
- **Batch Executor**: Leverages batch operations

### Communication Patterns
- Receives specifications from architects
- Shares code with reviewers for validation
- Collaborates with TDD for test creation
- Updates memory with implementation patterns
- Coordinates with batch executor for efficiency

## Success Criteria

Coder success is measured by:
1. **Code Quality**: Clean, maintainable code
2. **Functionality**: Meeting requirements
3. **Performance**: Efficient implementations
4. **Test Coverage**: Comprehensive testing
5. **Documentation**: Well-documented code

## Best Practices

1. Write self-documenting code
2. Follow established coding standards
3. Implement comprehensive error handling
4. Use batch operations for multiple files
5. Write tests for all new code
6. Keep commits focused and atomic

## Anti-Patterns to Avoid

- Code Duplication: Use DRY principles
- Over-Engineering: Keep it simple
- Ignoring Tests: Always test new code
- Poor Naming: Use descriptive names
- Large Functions: Break into smaller units
- Skipping Documentation: Document as you go

## Coding Techniques

The Coder mode employs:
- **Design Patterns**: Factory, Observer, Strategy
- **SOLID Principles**: Single responsibility, etc.
- **Functional Programming**: When appropriate
- **Async/Await**: For concurrent operations
- **Batch Processing**: Multi-file operations
- **Code Generation**: Template-based creation

## Output Artifacts

Coders produce:
- Source code files with implementations
- Unit and integration tests
- API documentation and examples
- Configuration files and scripts
- Migration scripts when needed
- README files for new modules

## Batch Operation Capabilities

Supports efficient batch operations:
- Multi-file code generation
- Bulk refactoring across codebases
- Parallel test creation
- Mass documentation updates
- Batch dependency updates
- Concurrent API endpoint creation

The Coder mode provides the implementation expertise needed to transform ideas into working software efficiently and reliably.

## Advanced Coding Patterns

### Batch Code Generation Engine

```rust
// Example: Efficient batch code generation
pub struct BatchCodeGenerator {
    template_engine: TemplateEngine,
    code_formatter: CodeFormatter,
    dependency_resolver: DependencyResolver,
    parallel_executor: ParallelExecutor,
}

impl BatchCodeGenerator {
    pub async fn generate_batch(
        &mut self,
        batch_request: BatchGenerationRequest,
    ) -> Result<BatchGenerationResult, GenerationError> {
        // Analyze dependencies between files
        let dependency_graph = self.dependency_resolver
            .analyze_dependencies(&batch_request.files)
            .await?;
            
        // Group by generation order
        let generation_groups = self.group_by_dependencies(&dependency_graph);
        
        // Generate in parallel where possible
        let mut generated_files = Vec::new();
        
        for group in generation_groups {
            let group_results = self.parallel_executor
                .execute_parallel(group, |file_spec| async {
                    self.generate_single_file(file_spec).await
                })
                .await?;
                
            generated_files.extend(group_results);
        }
        
        // Format all generated code
        let formatted_files = self.code_formatter
            .batch_format(&generated_files)
            .await?;
            
        Ok(BatchGenerationResult {
            files: formatted_files,
            generation_time: batch_request.started_at.elapsed(),
            dependency_graph,
        })
    }
    
    async fn generate_single_file(
        &self,
        spec: &FileSpecification,
    ) -> Result<GeneratedFile, GenerationError> {
        let template = self.template_engine
            .load_template(&spec.template_name)?;
            
        let generated_content = self.template_engine
            .render(template, &spec.context)?;
            
        Ok(GeneratedFile {
            path: spec.output_path.clone(),
            content: generated_content,
            metadata: FileMetadata {
                generated_at: Utc::now(),
                template_used: spec.template_name.clone(),
                dependencies: spec.dependencies.clone(),
            },
        })
    }
}
```

### Intelligent Refactoring System

```rust
// Example: Smart refactoring with pattern recognition
pub struct IntelligentRefactorer {
    pattern_detector: PatternDetector,
    refactor_strategies: HashMap<PatternType, Box<dyn RefactorStrategy>>,
    impact_analyzer: ImpactAnalyzer,
    test_generator: TestGenerator,
}

#[derive(Debug, Clone)]
pub struct RefactorPlan {
    pub identified_patterns: Vec<CodePattern>,
    pub proposed_changes: Vec<ProposedChange>,
    pub impact_assessment: ImpactAssessment,
    pub required_tests: Vec<TestSpecification>,
    pub execution_order: Vec<RefactorStep>,
}

impl IntelligentRefactorer {
    pub async fn plan_refactoring(
        &mut self,
        codebase: &Codebase,
        goals: &RefactoringGoals,
    ) -> Result<RefactorPlan, RefactorError> {
        // Detect patterns that need refactoring
        let patterns = self.pattern_detector
            .detect_refactor_candidates(codebase, goals)
            .await?;
            
        // Generate refactoring proposals
        let mut proposed_changes = Vec::new();
        
        for pattern in &patterns {
            if let Some(strategy) = self.refactor_strategies.get(&pattern.pattern_type) {
                let changes = strategy.propose_changes(pattern).await?;
                proposed_changes.extend(changes);
            }
        }
        
        // Analyze impact
        let impact = self.impact_analyzer
            .analyze_impact(&proposed_changes, codebase)
            .await?;
            
        // Generate required tests
        let required_tests = self.test_generator
            .generate_refactor_tests(&proposed_changes)
            .await?;
            
        // Order execution steps
        let execution_order = self.order_refactor_steps(&proposed_changes, &impact)?;
        
        Ok(RefactorPlan {
            identified_patterns: patterns,
            proposed_changes,
            impact_assessment: impact,
            required_tests,
            execution_order,
        })
    }
    
    pub async fn execute_refactoring(
        &mut self,
        plan: &RefactorPlan,
        codebase: &mut Codebase,
    ) -> Result<RefactorResult, RefactorError> {
        let mut results = Vec::new();
        
        // Execute in planned order
        for step in &plan.execution_order {
            // Create safety checkpoint
            let checkpoint = codebase.create_checkpoint()?;
            
            // Apply refactoring
            match self.apply_refactor_step(step, codebase).await {
                Ok(step_result) => {
                    // Verify tests still pass
                    if self.verify_tests(&step_result.affected_tests).await? {
                        results.push(step_result);
                    } else {
                        // Rollback on test failure
                        codebase.restore_checkpoint(checkpoint)?;
                        return Err(RefactorError::TestFailure(step.clone()));
                    }
                }
                Err(e) => {
                    codebase.restore_checkpoint(checkpoint)?;
                    return Err(e);
                }
            }
        }
        
        Ok(RefactorResult {
            applied_changes: results,
            final_metrics: self.calculate_quality_metrics(codebase).await?,
        })
    }
}
```

### API Implementation Framework

```rust
// Example: Comprehensive API implementation
pub struct APIImplementer {
    endpoint_generator: EndpointGenerator,
    validation_builder: ValidationBuilder,
    auth_integrator: AuthIntegrator,
    doc_generator: DocumentationGenerator,
}

#[derive(Debug, Clone)]
pub struct APIImplementation {
    pub endpoints: Vec<Endpoint>,
    pub middleware: Vec<Middleware>,
    pub validators: HashMap<String, Validator>,
    pub documentation: APIDocumentation,
    pub tests: APITestSuite,
}

impl APIImplementer {
    pub async fn implement_api(
        &mut self,
        spec: &APISpecification,
    ) -> Result<APIImplementation, ImplementationError> {
        // Generate endpoint handlers
        let endpoints = self.endpoint_generator
            .generate_endpoints(&spec.endpoints)
            .await?;
            
        // Build validation middleware
        let validators = self.validation_builder
            .build_validators(&spec.schemas)
            .await?;
            
        // Integrate authentication
        let auth_middleware = self.auth_integrator
            .integrate_auth(&spec.security_requirements)
            .await?;
            
        // Generate comprehensive documentation
        let documentation = self.doc_generator
            .generate_api_docs(&endpoints, &spec)
            .await?;
            
        // Create test suite
        let tests = self.generate_api_tests(&endpoints, &spec).await?;
        
        Ok(APIImplementation {
            endpoints,
            middleware: vec![auth_middleware],
            validators,
            documentation,
            tests,
        })
    }
    
    async fn generate_api_tests(
        &self,
        endpoints: &[Endpoint],
        spec: &APISpecification,
    ) -> Result<APITestSuite, ImplementationError> {
        let mut test_suite = APITestSuite::new();
        
        for endpoint in endpoints {
            // Happy path tests
            test_suite.add_test(self.create_happy_path_test(endpoint)?);
            
            // Validation tests
            test_suite.add_tests(self.create_validation_tests(endpoint, spec)?);
            
            // Auth tests
            test_suite.add_tests(self.create_auth_tests(endpoint)?);
            
            // Error handling tests
            test_suite.add_tests(self.create_error_tests(endpoint)?);
        }
        
        Ok(test_suite)
    }
}
```

### Code Quality Enforcement

```rust
// Example: Automated code quality enforcement
pub struct CodeQualityEnforcer {
    linter: CodeLinter,
    formatter: CodeFormatter,
    complexity_analyzer: ComplexityAnalyzer,
    security_scanner: SecurityScanner,
}

impl CodeQualityEnforcer {
    pub async fn enforce_quality(
        &mut self,
        code: &mut Code,
    ) -> Result<QualityReport, QualityError> {
        // Lint code
        let lint_issues = self.linter.lint(code).await?;
        
        // Auto-fix what we can
        let auto_fixed = self.auto_fix_issues(&lint_issues, code).await?;
        
        // Format code
        self.formatter.format(code).await?;
        
        // Check complexity
        let complexity_issues = self.complexity_analyzer
            .analyze(code)
            .await?;
            
        // Security scan
        let security_issues = self.security_scanner
            .scan(code)
            .await?;
            
        // Generate report
        Ok(QualityReport {
            lint_issues: lint_issues.into_iter()
                .filter(|i| !auto_fixed.contains(i))
                .collect(),
            complexity_issues,
            security_issues,
            auto_fixed_count: auto_fixed.len(),
            quality_score: self.calculate_quality_score(code).await?,
        })
    }
}
```

### Parallel Implementation Coordinator

```rust
// Example: Coordinating parallel implementation tasks
pub struct ParallelImplementationCoordinator {
    task_scheduler: TaskScheduler,
    resource_manager: ResourceManager,
    progress_tracker: ProgressTracker,
    conflict_resolver: ConflictResolver,
}

impl ParallelImplementationCoordinator {
    pub async fn coordinate_implementation(
        &mut self,
        features: Vec<FeatureSpec>,
        team_size: usize,
    ) -> Result<ParallelImplementationResult, CoordinationError> {
        // Analyze feature dependencies
        let dependency_graph = self.analyze_dependencies(&features)?;
        
        // Schedule tasks optimally
        let schedule = self.task_scheduler
            .create_schedule(&dependency_graph, team_size)
            .await?;
            
        // Allocate resources
        let resources = self.resource_manager
            .allocate_resources(&schedule)
            .await?;
            
        // Execute in parallel
        let implementation_handle = self.execute_parallel_implementation(
            schedule,
            resources
        );
        
        // Track progress
        let progress_handle = self.progress_tracker
            .track_progress(implementation_handle.subscribe());
            
        // Wait for completion and handle conflicts
        let results = implementation_handle.await?;
        
        // Resolve any conflicts
        let resolved_results = self.conflict_resolver
            .resolve_conflicts(results)
            .await?;
            
        Ok(ParallelImplementationResult {
            implementations: resolved_results,
            total_time: progress_handle.total_time(),
            parallelism_efficiency: progress_handle.efficiency_score(),
        })
    }
}
```

### Template-Based Code Generation

```rust
// Example: Sophisticated template system
pub struct TemplateCodeGenerator {
    template_registry: TemplateRegistry,
    context_builder: ContextBuilder,
    customizer: TemplateCustomizer,
    validator: GeneratedCodeValidator,
}

#[derive(Debug, Clone)]
pub struct CodeTemplate {
    pub name: String,
    pub template_type: TemplateType,
    pub parameters: Vec<TemplateParameter>,
    pub dependencies: Vec<TemplateDependency>,
    pub customization_points: Vec<CustomizationPoint>,
}

impl TemplateCodeGenerator {
    pub async fn generate_from_template(
        &mut self,
        template_name: &str,
        context: &GenerationContext,
    ) -> Result<GeneratedCode, GenerationError> {
        // Load template
        let template = self.template_registry
            .get_template(template_name)
            .await?;
            
        // Build full context
        let full_context = self.context_builder
            .build_context(&template, context)
            .await?;
            
        // Apply customizations
        let customized_template = self.customizer
            .customize_template(&template, &full_context)
            .await?;
            
        // Generate code
        let generated = self.render_template(&customized_template, &full_context)?;
        
        // Validate generated code
        self.validator.validate(&generated).await?;
        
        Ok(generated)
    }
    
    pub async fn batch_generate_related(
        &mut self,
        root_template: &str,
        context: &GenerationContext,
    ) -> Result<Vec<GeneratedCode>, GenerationError> {
        // Get template and its dependencies
        let template_graph = self.template_registry
            .get_template_graph(root_template)
            .await?;
            
        // Generate in dependency order
        let mut generated_files = Vec::new();
        
        for template_name in template_graph.topological_order() {
            let file_context = self.context_builder
                .build_file_context(template_name, context, &generated_files)
                .await?;
                
            let generated = self.generate_from_template(template_name, &file_context).await?;
            generated_files.push(generated);
        }
        
        Ok(generated_files)
    }
}
```