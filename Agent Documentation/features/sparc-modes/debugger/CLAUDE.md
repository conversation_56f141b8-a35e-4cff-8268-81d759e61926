# Debugger Mode

## Purpose and Use Cases

The Debugger mode specializes in problem identification, root cause analysis, and issue resolution. Debugger agents excel at tracking down elusive bugs, understanding complex failures, and providing fixes for system issues.

### Primary Use Cases
- Investigating production incidents
- Tracking down elusive bugs
- Analyzing system failures and crashes
- Identifying performance bottlenecks
- Resolving integration issues

## Rust Code Examples

### SPARC Debugger Mode Trait Definition

```rust
// Example: Debugger Mode trait for SPARC system
pub trait DebuggerMode: Send + Sync {
    fn name(&self) -> &'static str { "Debugger" }
    fn description(&self) -> &'static str { 
        "Problem identification and root cause analysis mode"
    }
    
    // Core debugging methods
    async fn investigate_issue(&mut self, issue: &Issue) -> Result<Investigation, ModeError>;
    async fn reproduce_bug(&mut self, bug_report: &BugReport) -> Result<Reproduction, ModeError>;
    async fn analyze_root_cause(&mut self, symptoms: &[Symptom]) -> Result<RootCause, ModeError>;
    
    // Fix generation
    async fn propose_fix(&mut self, root_cause: &RootCause) -> Result<Fix, ModeError>;
    async fn validate_fix(&mut self, fix: &Fix, test_cases: &[TestCase]) -> Result<ValidationResult, ModeError>;
}

// Debugging data structures
#[derive(Debug, Clone)]
pub struct Investigation {
    pub issue_id: String,
    pub hypothesis: Vec<Hypothesis>,
    pub evidence: Vec<Evidence>,
    pub reproduction_steps: Option<ReproductionSteps>,
    pub root_cause: Option<RootCause>,
}

#[derive(Debug, Clone)]
pub enum DebugTechnique {
    BinarySearch { start: Location, end: Location },
    TraceAnalysis { trace_points: Vec<TracePoint> },
    StateInspection { checkpoints: Vec<StateCheckpoint> },
    DifferentialDebugging { working_version: Version, broken_version: Version },
    TimeTravelDebugging { snapshots: Vec<ExecutionSnapshot> },
}
```

### Debugger State Machine

```rust
// State machine for debugging process
#[derive(Debug, Clone)]
pub enum DebuggerState {
    Receiving {
        issue_report: IssueReport,
        initial_data: Vec<LogEntry>,
    },
    Hypothesizing {
        symptoms: Vec<Symptom>,
        hypotheses: Vec<Hypothesis>,
    },
    Investigating {
        current_hypothesis: Hypothesis,
        technique: DebugTechnique,
        evidence: Vec<Evidence>,
    },
    Reproducing {
        hypothesis: Hypothesis,
        environment: TestEnvironment,
        attempts: u32,
    },
    AnalyzingRootCause {
        confirmed_bug: Bug,
        trace_data: TraceData,
    },
    DevelopingFix {
        root_cause: RootCause,
        proposed_fixes: Vec<Fix>,
    },
    ValidatingFix {
        fix: Fix,
        test_results: Vec<TestResult>,
    },
    Complete {
        resolution: Resolution,
        documentation: DebugReport,
    },
}

impl DebuggerState {
    pub fn transition(&mut self, event: DebugEvent) -> Result<(), StateError> {
        match (self.clone(), event) {
            (DebuggerState::Receiving { issue_report, .. }, 
             DebugEvent::SymptomsIdentified(symptoms)) => {
                *self = DebuggerState::Hypothesizing {
                    symptoms,
                    hypotheses: Vec::new(),
                };
                Ok(())
            }
            (DebuggerState::Hypothesizing { symptoms, mut hypotheses }, 
             DebugEvent::HypothesisFormed(hypothesis)) => {
                hypotheses.push(hypothesis.clone());
                *self = DebuggerState::Investigating {
                    current_hypothesis: hypothesis,
                    technique: DebugTechnique::BinarySearch { 
                        start: Location::Beginning, 
                        end: Location::End 
                    },
                    evidence: Vec::new(),
                };
                Ok(())
            }
            // ... other transitions
            _ => Err(StateError::InvalidTransition),
        }
    }
}
```

## Key Behaviors and Characteristics

### Core Behaviors
- **Systematic Investigation**: Methodical approach to problem-solving
- **Hypothesis Testing**: Forms and validates theories
- **Root Cause Analysis**: Digs beyond symptoms
- **Reproduction**: Creates minimal test cases
- **Solution Validation**: Ensures fixes work correctly

### Unique Characteristics
- Strong analytical and deductive skills
- Patience with complex problems
- Ability to read and understand any code
- Knowledge of debugging tools and techniques
- Clear documentation of findings

## When to Use This Mode

Deploy Debugger agents when:
- System exhibits unexpected behavior
- Performance degradation occurs
- Integration tests fail mysteriously
- Production incidents need investigation
- Memory leaks or resource issues arise

## Integration Points

### Works Well With
- **Tester**: Receives bug reports
- **Coder**: Provides fixes for issues
- **Analyzer**: Gets performance data
- **Reviewer**: Validates fix quality
- **Orchestrator**: Reports findings

### Communication Patterns
- Receives incident reports from orchestrators
- Collaborates with testers on reproduction
- Provides fixes to coders
- Shares findings via memory system
- Updates team on root causes

## Success Criteria

Debugger success is measured by:
1. **Problem Resolution**: Issues successfully fixed
2. **Root Cause Identification**: True cause found
3. **Time to Resolution**: Efficient debugging
4. **Fix Quality**: Solutions prevent recurrence
5. **Knowledge Transfer**: Team learns from issues

## Best Practices

1. Reproduce the issue consistently first
2. Use scientific method: hypothesis and test
3. Check recent changes for correlation
4. Use appropriate debugging tools
5. Document steps and findings
6. Create tests to prevent regression

## Anti-Patterns to Avoid

- Random Changes: Use systematic approach
- Symptom Fixing: Address root causes
- Poor Documentation: Record everything
- Working in Isolation: Collaborate with team
- Ignoring Patterns: Look for systemic issues
- Quick Hacks: Implement proper solutions

## Debugging Techniques

The Debugger mode employs:
- **Binary Search**: Narrowing down issue location
- **Trace Analysis**: Following execution paths
- **State Inspection**: Examining variable values
- **Differential Debugging**: Comparing working/broken states
- **Time-Travel Debugging**: Analyzing execution history
- **Statistical Debugging**: Pattern analysis across runs

## Investigation Tools

Debuggers utilize:
- Interactive debuggers (GDB, LLDB)
- Logging and tracing systems
- Profilers and performance analyzers
- Memory analysis tools
- Network packet analyzers
- System monitoring tools

## Common Issue Categories

Debuggers handle:
- **Logic Errors**: Incorrect algorithms or conditions
- **Race Conditions**: Concurrency issues
- **Memory Issues**: Leaks, corruption, overflows
- **Performance**: Bottlenecks and inefficiencies
- **Integration**: API mismatches, protocol errors
- **Environment**: Configuration and deployment issues

The Debugger mode provides the investigative expertise needed to solve complex problems and maintain system reliability.

## Advanced Debugging Patterns

### Hypothesis-Driven Debugging

```rust
// Example: Scientific method for debugging
pub struct HypothesisEngine {
    evidence_collector: EvidenceCollector,
    hypothesis_generator: HypothesisGenerator,
}

#[derive(Debug, Clone)]
pub struct Hypothesis {
    pub id: String,
    pub description: String,
    pub probability: f64,
    pub test_plan: TestPlan,
    pub supporting_evidence: Vec<Evidence>,
    pub contradicting_evidence: Vec<Evidence>,
}

impl HypothesisEngine {
    pub async fn generate_hypotheses(
        &self,
        symptoms: &[Symptom],
    ) -> Result<Vec<Hypothesis>, DebugError> {
        let mut hypotheses = Vec::new();
        
        // Generate hypotheses based on symptom patterns
        if self.has_memory_growth(symptoms) {
            hypotheses.push(Hypothesis {
                id: "memory_leak".to_string(),
                description: "Possible memory leak in resource management".to_string(),
                probability: 0.8,
                test_plan: TestPlan::MemoryProfiling,
                supporting_evidence: vec![],
                contradicting_evidence: vec![],
            });
        }
        
        if self.has_timing_issues(symptoms) {
            hypotheses.push(Hypothesis {
                id: "race_condition".to_string(),
                description: "Race condition in concurrent operations".to_string(),
                probability: 0.7,
                test_plan: TestPlan::ConcurrencyTesting,
                supporting_evidence: vec![],
                contradicting_evidence: vec![],
            });
        }
        
        if self.has_intermittent_failures(symptoms) {
            hypotheses.push(Hypothesis {
                id: "state_corruption".to_string(),
                description: "State corruption due to improper initialization".to_string(),
                probability: 0.6,
                test_plan: TestPlan::StateValidation,
                supporting_evidence: vec![],
                contradicting_evidence: vec![],
            });
        }
        
        Ok(hypotheses)
    }
    
    pub async fn test_hypothesis(
        &mut self,
        hypothesis: &mut Hypothesis,
    ) -> Result<HypothesisResult, DebugError> {
        let evidence = match &hypothesis.test_plan {
            TestPlan::MemoryProfiling => {
                self.evidence_collector.collect_memory_profile().await?
            }
            TestPlan::ConcurrencyTesting => {
                self.evidence_collector.run_concurrency_tests().await?
            }
            TestPlan::StateValidation => {
                self.evidence_collector.validate_state_transitions().await?
            }
        };
        
        // Update hypothesis based on evidence
        for e in evidence {
            if e.supports(&hypothesis.description) {
                hypothesis.supporting_evidence.push(e);
                hypothesis.probability *= 1.2; // Increase confidence
            } else {
                hypothesis.contradicting_evidence.push(e);
                hypothesis.probability *= 0.8; // Decrease confidence
            }
        }
        
        hypothesis.probability = hypothesis.probability.clamp(0.0, 1.0);
        
        Ok(HypothesisResult {
            hypothesis_id: hypothesis.id.clone(),
            confidence: hypothesis.probability,
            should_continue: hypothesis.probability > 0.3,
        })
    }
}
```

### Root Cause Analysis Framework

```rust
// Example: Systematic root cause analysis
pub struct RootCauseAnalyzer {
    trace_analyzer: TraceAnalyzer,
    state_differ: StateDiffer,
    correlation_engine: CorrelationEngine,
}

#[derive(Debug, Clone)]
pub struct RootCause {
    pub location: CodeLocation,
    pub cause_type: CauseType,
    pub description: String,
    pub evidence_chain: Vec<Evidence>,
    pub fix_suggestions: Vec<FixSuggestion>,
}

#[derive(Debug, Clone)]
pub enum CauseType {
    LogicError { condition: String },
    RaceCondition { resources: Vec<String> },
    MemoryIssue { issue_type: MemoryIssueType },
    ConfigurationError { setting: String },
    ExternalDependency { service: String },
}

impl RootCauseAnalyzer {
    pub async fn analyze(
        &self,
        bug: &ConfirmedBug,
        traces: &[ExecutionTrace],
    ) -> Result<RootCause, AnalysisError> {
        // Step 1: Find divergence point
        let divergence = self.find_divergence_point(traces).await?;
        
        // Step 2: Analyze state differences
        let state_diff = self.state_differ
            .diff_states_at_point(&divergence)
            .await?;
            
        // Step 3: Correlate with other signals
        let correlations = self.correlation_engine
            .find_correlations(&divergence, &state_diff)
            .await?;
            
        // Step 4: Determine root cause
        let cause_type = self.classify_cause(&state_diff, &correlations)?;
        
        // Step 5: Generate fix suggestions
        let fix_suggestions = self.generate_fixes(&cause_type, &divergence)?;
        
        Ok(RootCause {
            location: divergence.location,
            cause_type,
            description: self.describe_cause(&cause_type, &state_diff),
            evidence_chain: divergence.evidence_chain,
            fix_suggestions,
        })
    }
    
    fn classify_cause(
        &self,
        state_diff: &StateDiff,
        correlations: &[Correlation],
    ) -> Result<CauseType, ClassificationError> {
        // Check for race conditions
        if correlations.iter().any(|c| c.is_timing_dependent()) {
            let resources = self.identify_shared_resources(state_diff);
            return Ok(CauseType::RaceCondition { resources });
        }
        
        // Check for memory issues
        if let Some(memory_issue) = self.detect_memory_issue(state_diff) {
            return Ok(CauseType::MemoryIssue { issue_type: memory_issue });
        }
        
        // Default to logic error
        Ok(CauseType::LogicError {
            condition: state_diff.primary_difference(),
        })
    }
}
```

### Debugging Coordination

```rust
// Example: Coordinating debugging with other modes
pub struct DebugCoordinator {
    debugger_mode: Box<dyn DebuggerMode>,
    tester_mode: Option<Box<dyn TesterMode>>,
    analyzer_mode: Option<Box<dyn AnalyzerMode>>,
}

impl DebugCoordinator {
    pub async fn comprehensive_debug(
        &mut self,
        issue: &Issue,
    ) -> Result<DebugResolution, CoordinationError> {
        // Step 1: Gather initial data from analyzer if available
        let analysis_data = if let Some(analyzer) = &mut self.analyzer_mode {
            Some(analyzer.analyze_issue_context(issue).await?)
        } else {
            None
        };
        
        // Step 2: Start investigation with debugger
        let mut investigation = self.debugger_mode
            .investigate_issue(issue)
            .await?;
            
        // Step 3: Use tester to reproduce if available
        let reproduction = if let Some(tester) = &mut self.tester_mode {
            if let Some(repro_steps) = &investigation.reproduction_steps {
                Some(tester.execute_reproduction(repro_steps).await?)
            } else {
                None
            }
        } else {
            None
        };
        
        // Step 4: Enhance investigation with test results
        if let Some(repro) = reproduction {
            investigation.evidence.extend(repro.captured_evidence);
        }
        
        // Step 5: Analyze root cause
        let root_cause = self.debugger_mode
            .analyze_root_cause(&investigation.evidence)
            .await?;
            
        // Step 6: Develop and validate fix
        let fix = self.debugger_mode
            .propose_fix(&root_cause)
            .await?;
            
        Ok(DebugResolution {
            issue_id: issue.id.clone(),
            root_cause,
            fix,
            investigation_summary: investigation,
            prevention_recommendations: self.generate_prevention_tips(&root_cause),
        })
    }
}
```

### Performance Debugging

```rust
// Example: Specialized performance debugging
pub struct PerformanceDebugger {
    profiler: Profiler,
    flame_graph_generator: FlameGraphGenerator,
    bottleneck_detector: BottleneckDetector,
}

#[derive(Debug, Clone)]
pub struct PerformanceIssue {
    pub issue_type: PerfIssueType,
    pub location: CodeLocation,
    pub impact: PerformanceImpact,
    pub optimization_suggestions: Vec<Optimization>,
}

#[derive(Debug, Clone)]
pub enum PerfIssueType {
    CPUBottleneck { hot_path: Vec<Function> },
    MemoryBottleneck { allocation_sites: Vec<AllocationSite> },
    IOBottleneck { blocking_operations: Vec<IOOperation> },
    ConcurrencyBottleneck { contention_points: Vec<ContentionPoint> },
}

impl PerformanceDebugger {
    pub async fn debug_performance(
        &mut self,
        performance_report: &PerformanceReport,
    ) -> Result<Vec<PerformanceIssue>, DebugError> {
        let mut issues = Vec::new();
        
        // Profile CPU usage
        let cpu_profile = self.profiler.profile_cpu().await?;
        let flame_graph = self.flame_graph_generator.generate(&cpu_profile)?;
        
        // Detect CPU bottlenecks
        if let Some(hot_paths) = self.bottleneck_detector.find_hot_paths(&flame_graph) {
            for hot_path in hot_paths {
                issues.push(PerformanceIssue {
                    issue_type: PerfIssueType::CPUBottleneck {
                        hot_path: hot_path.functions.clone(),
                    },
                    location: hot_path.primary_location(),
                    impact: self.calculate_impact(&hot_path),
                    optimization_suggestions: vec![
                        Optimization::AlgorithmicImprovement,
                        Optimization::Caching,
                        Optimization::Parallelization,
                    ],
                });
            }
        }
        
        // Profile memory usage
        let memory_profile = self.profiler.profile_memory().await?;
        
        // Detect memory bottlenecks
        if let Some(allocation_issues) = self.bottleneck_detector.find_excessive_allocations(&memory_profile) {
            for issue in allocation_issues {
                issues.push(PerformanceIssue {
                    issue_type: PerfIssueType::MemoryBottleneck {
                        allocation_sites: issue.sites.clone(),
                    },
                    location: issue.primary_site(),
                    impact: self.calculate_memory_impact(&issue),
                    optimization_suggestions: vec![
                        Optimization::ReduceAllocations,
                        Optimization::UseObjectPools,
                        Optimization::OptimizeDataStructures,
                    ],
                });
            }
        }
        
        Ok(issues)
    }
}
```