# Architect Mode

## Purpose and Use Cases

The Architect mode specializes in system design, architectural patterns, and strategic technical decisions. Architect agents create blueprints for robust, scalable systems while ensuring alignment with business requirements and technical constraints.

### Primary Use Cases
- System architecture design and documentation
- Technology stack selection and evaluation
- Design pattern implementation guidance
- Scalability and performance planning
- Integration architecture and API design

## Rust Code Examples

### SPARC Architect Mode Trait Definition

```rust
// Example: Architect Mode trait for SPARC system
pub trait ArchitectMode: Send + Sync {
    fn name(&self) -> &'static str { "Architect" }
    fn description(&self) -> &'static str { 
        "System architecture design and strategic planning mode"
    }
    
    // Core architecture methods
    async fn design_system(&mut self, requirements: &Requirements) -> Result<Architecture, ModeError>;
    async fn evaluate_architecture(&mut self, design: &Architecture) -> Result<ArchitectureAssessment, ModeError>;
    async fn refine_design(&mut self, feedback: &DesignFeedback) -> Result<RefinedArchitecture, ModeError>;
    
    // Pattern and technology selection
    async fn select_patterns(&mut self, context: &SystemContext) -> Result<Vec<DesignPattern>, ModeError>;
    async fn recommend_stack(&mut self, constraints: &TechConstraints) -> Result<TechnologyStack, ModeError>;
}

// Architecture data structures
#[derive(Debug, Clone)]
pub struct Architecture {
    pub components: Vec<Component>,
    pub interactions: Vec<Interaction>,
    pub layers: Vec<ArchitecturalLayer>,
    pub patterns: Vec<DesignPattern>,
    pub constraints: Vec<Constraint>,
}

#[derive(Debug, Clone)]
pub enum ArchitecturalDecision {
    Microservices {
        service_boundaries: Vec<ServiceBoundary>,
        communication_patterns: Vec<CommunicationPattern>,
    },
    Monolithic {
        module_structure: ModuleStructure,
        layering_strategy: LayeringStrategy,
    },
    EventDriven {
        event_sources: Vec<EventSource>,
        event_processors: Vec<EventProcessor>,
    },
    Serverless {
        function_definitions: Vec<FunctionDef>,
        orchestration: OrchestrationStrategy,
    },
    Hybrid {
        components: Vec<ArchitecturalComponent>,
        integration_points: Vec<IntegrationPoint>,
    },
}
```

### Architect State Machine

```rust
// State machine for architecture design process
#[derive(Debug, Clone)]
pub enum ArchitectState {
    RequirementsGathering {
        functional_reqs: Vec<FunctionalRequirement>,
        non_functional_reqs: Vec<NonFunctionalRequirement>,
    },
    ConceptualDesign {
        high_level_components: Vec<ConceptualComponent>,
        interaction_patterns: Vec<InteractionPattern>,
    },
    DetailedDesign {
        component_specifications: Vec<ComponentSpec>,
        interface_definitions: Vec<InterfaceDefinition>,
    },
    ValidationPhase {
        design_reviews: Vec<DesignReview>,
        risk_assessments: Vec<RiskAssessment>,
    },
    RefinementPhase {
        feedback_items: Vec<FeedbackItem>,
        design_iterations: u32,
    },
    DocumentationPhase {
        architecture_docs: Vec<Document>,
        decision_records: Vec<ADR>,
    },
    Complete {
        final_architecture: Architecture,
        implementation_guide: ImplementationGuide,
    },
}

impl ArchitectState {
    pub fn transition(&mut self, event: ArchitectEvent) -> Result<(), StateError> {
        match (self.clone(), event) {
            (ArchitectState::RequirementsGathering { functional_reqs, non_functional_reqs }, 
             ArchitectEvent::RequirementsComplete) => {
                *self = ArchitectState::ConceptualDesign {
                    high_level_components: Self::derive_components(&functional_reqs),
                    interaction_patterns: Self::derive_patterns(&non_functional_reqs),
                };
                Ok(())
            }
            (ArchitectState::ConceptualDesign { high_level_components, .. }, 
             ArchitectEvent::ConceptApproved) => {
                *self = ArchitectState::DetailedDesign {
                    component_specifications: Self::detail_components(&high_level_components),
                    interface_definitions: Self::define_interfaces(&high_level_components),
                };
                Ok(())
            }
            // ... other transitions
            _ => Err(StateError::InvalidTransition),
        }
    }
}
```

## Key Behaviors and Characteristics

### Core Behaviors
- **Strategic Thinking**: Big-picture system design
- **Pattern Recognition**: Applying proven solutions
- **Trade-off Analysis**: Balancing competing concerns
- **Risk Assessment**: Identifying architectural risks
- **Documentation**: Clear architectural communication

### Unique Characteristics
- Deep understanding of design patterns
- Ability to balance technical and business needs
- Strong visualization and diagramming skills
- Forward-thinking scalability planning
- Cross-domain technical knowledge

## When to Use This Mode

Deploy Architect agents when:
- Starting new projects or major features
- Refactoring existing system architecture
- Evaluating technology choices
- Planning system integrations
- Addressing scalability challenges

## Integration Points

### Works Well With
- **Coder**: Translates designs to implementation
- **Analyzer**: Provides metrics for decisions
- **Reviewer**: Validates architectural choices
- **Orchestrator**: Coordinates design activities
- **Memory Manager**: Stores architectural decisions

### Communication Patterns
- Receives requirements from orchestrators
- Shares designs with coders for implementation
- Collaborates with analyzers on performance
- Provides guidance to reviewers
- Updates memory with architectural decisions

## Success Criteria

Architect success is measured by:
1. **Design Quality**: Robust, scalable architectures
2. **Clarity**: Well-documented decisions
3. **Feasibility**: Implementable designs
4. **Alignment**: Meeting business requirements
5. **Maintainability**: Long-term sustainability

## Best Practices

1. Start with clear requirement understanding
2. Consider multiple architectural options
3. Document decisions and rationale
4. Plan for future growth and change
5. Validate designs through prototypes
6. Maintain architectural decision records

## Anti-Patterns to Avoid

- Over-Engineering: Keep designs appropriately simple
- Ivory Tower: Stay connected to implementation
- Technology Bias: Choose based on requirements
- Documentation Lag: Keep docs current
- Ignoring Constraints: Work within limitations
- Solo Design: Collaborate with team

## Architectural Artifacts

The Architect mode produces:
- **System Diagrams**: Component and deployment views
- **Interface Specifications**: API contracts
- **Decision Records**: ADRs documenting choices
- **Pattern Catalogs**: Applicable design patterns
- **Technology Recommendations**: Stack selections
- **Risk Assessments**: Architectural concerns

## Design Principles

Architects follow:
- **SOLID Principles**: For component design
- **DRY/KISS**: Simplicity and reusability
- **Separation of Concerns**: Clear boundaries
- **Loose Coupling**: Flexible integrations
- **High Cohesion**: Focused components
- **Open/Closed**: Extensible designs

## Architecture Styles

Supports various styles:
- Microservices and service-oriented
- Event-driven and message-based
- Layered and hexagonal architectures
- Domain-driven design approaches
- Cloud-native and serverless patterns
- Hybrid and transitional architectures

The Architect mode provides the strategic technical vision needed to build systems that meet today's needs while adapting to tomorrow's challenges.

## Advanced Architecture Patterns

### Memory-Integrated Architecture Design

```rust
// Example: Architecture design with memory integration
pub struct MemoryAwareArchitect {
    architect_mode: Box<dyn ArchitectMode>,
    memory_store: Arc<MemoryStore>,
    pattern_library: PatternLibrary,
}

impl MemoryAwareArchitect {
    pub async fn design_with_context(
        &mut self,
        requirements: &Requirements,
    ) -> Result<ContextualArchitecture, ArchitectError> {
        // Retrieve past architectural decisions
        let historical_decisions = self.memory_store
            .query("architectural_decisions", &requirements.domain)
            .await?;
            
        // Learn from previous patterns
        let successful_patterns = self.analyze_past_successes(&historical_decisions)?;
        
        // Design with historical context
        let base_architecture = self.architect_mode
            .design_system(requirements)
            .await?;
            
        // Apply learned patterns
        let enhanced_architecture = self.apply_learned_patterns(
            base_architecture,
            &successful_patterns
        )?;
        
        // Store new decisions
        self.memory_store.store(
            "architectural_decisions",
            &ArchitecturalDecision {
                requirements: requirements.clone(),
                architecture: enhanced_architecture.clone(),
                rationale: self.generate_rationale(&enhanced_architecture),
                timestamp: Utc::now(),
            }
        ).await?;
        
        Ok(ContextualArchitecture {
            design: enhanced_architecture,
            historical_context: historical_decisions,
            applied_patterns: successful_patterns,
        })
    }
}
```

### Domain-Driven Architecture

```rust
// Example: DDD-based architecture design
pub struct DomainDrivenArchitect {
    domain_analyzer: DomainAnalyzer,
    bounded_context_mapper: BoundedContextMapper,
    aggregate_designer: AggregateDesigner,
}

#[derive(Debug, Clone)]
pub struct DomainModel {
    pub bounded_contexts: Vec<BoundedContext>,
    pub aggregates: Vec<Aggregate>,
    pub value_objects: Vec<ValueObject>,
    pub domain_events: Vec<DomainEvent>,
    pub context_map: ContextMap,
}

impl DomainDrivenArchitect {
    pub async fn design_domain_architecture(
        &mut self,
        business_requirements: &BusinessRequirements,
    ) -> Result<DomainArchitecture, ArchitectError> {
        // Analyze domain language
        let ubiquitous_language = self.domain_analyzer
            .extract_domain_language(business_requirements)
            .await?;
            
        // Identify bounded contexts
        let bounded_contexts = self.bounded_context_mapper
            .identify_contexts(&ubiquitous_language)
            .await?;
            
        // Design aggregates within contexts
        let mut domain_model = DomainModel {
            bounded_contexts: bounded_contexts.clone(),
            aggregates: Vec::new(),
            value_objects: Vec::new(),
            domain_events: Vec::new(),
            context_map: ContextMap::new(),
        };
        
        for context in &bounded_contexts {
            let aggregates = self.aggregate_designer
                .design_aggregates(context)
                .await?;
            domain_model.aggregates.extend(aggregates);
        }
        
        // Map context relationships
        domain_model.context_map = self.bounded_context_mapper
            .map_relationships(&bounded_contexts)
            .await?;
            
        Ok(DomainArchitecture {
            domain_model,
            implementation_strategy: self.derive_implementation_strategy(&domain_model),
            integration_patterns: self.select_integration_patterns(&domain_model.context_map),
        })
    }
}
```

### Cloud-Native Architecture Design

```rust
// Example: Cloud-native architecture specialist
pub struct CloudNativeArchitect {
    container_designer: ContainerDesigner,
    orchestration_planner: OrchestrationPlanner,
    service_mesh_architect: ServiceMeshArchitect,
    observability_designer: ObservabilityDesigner,
}

#[derive(Debug, Clone)]
pub struct CloudNativeArchitecture {
    pub microservices: Vec<Microservice>,
    pub container_specs: Vec<ContainerSpec>,
    pub orchestration_config: OrchestrationConfig,
    pub service_mesh: Option<ServiceMeshConfig>,
    pub observability_stack: ObservabilityStack,
}

impl CloudNativeArchitect {
    pub async fn design_cloud_native_system(
        &mut self,
        requirements: &SystemRequirements,
        cloud_provider: CloudProvider,
    ) -> Result<CloudNativeArchitecture, ArchitectError> {
        // Design microservices
        let microservices = self.design_service_boundaries(requirements)?;
        
        // Create container specifications
        let container_specs = self.container_designer
            .design_containers(&microservices)
            .await?;
            
        // Plan orchestration
        let orchestration_config = self.orchestration_planner
            .plan_orchestration(&container_specs, &cloud_provider)
            .await?;
            
        // Design service mesh if needed
        let service_mesh = if self.needs_service_mesh(&microservices) {
            Some(self.service_mesh_architect
                .design_mesh(&microservices)
                .await?)
        } else {
            None
        };
        
        // Design observability
        let observability_stack = self.observability_designer
            .design_observability(&microservices, &orchestration_config)
            .await?;
            
        Ok(CloudNativeArchitecture {
            microservices,
            container_specs,
            orchestration_config,
            service_mesh,
            observability_stack,
        })
    }
    
    fn needs_service_mesh(&self, services: &[Microservice]) -> bool {
        services.len() > 5 || 
        services.iter().any(|s| s.requires_advanced_networking())
    }
}
```

### Evolutionary Architecture

```rust
// Example: Architecture that evolves over time
pub struct EvolutionaryArchitect {
    fitness_functions: Vec<Box<dyn FitnessFunction>>,
    evolution_strategies: Vec<EvolutionStrategy>,
    architectural_metrics: ArchitecturalMetrics,
}

#[derive(Debug, Clone)]
pub struct EvolvableArchitecture {
    pub current_state: Architecture,
    pub fitness_scores: HashMap<String, f64>,
    pub evolution_path: Vec<ArchitecturalChange>,
    pub next_evolution: Option<PlannedEvolution>,
}

impl EvolutionaryArchitect {
    pub async fn evolve_architecture(
        &mut self,
        current: &Architecture,
        changing_requirements: &ChangingRequirements,
    ) -> Result<EvolvableArchitecture, ArchitectError> {
        // Evaluate current fitness
        let fitness_scores = self.evaluate_fitness(current).await?;
        
        // Identify evolution needs
        let evolution_drivers = self.identify_evolution_drivers(
            &fitness_scores,
            changing_requirements
        )?;
        
        // Generate evolution options
        let evolution_options = self.generate_evolution_options(
            current,
            &evolution_drivers
        )?;
        
        // Select best evolution path
        let selected_evolution = self.select_evolution_path(
            &evolution_options,
            &self.evolution_strategies
        )?;
        
        Ok(EvolvableArchitecture {
            current_state: current.clone(),
            fitness_scores,
            evolution_path: self.trace_evolution_history(current),
            next_evolution: Some(selected_evolution),
        })
    }
    
    async fn evaluate_fitness(
        &self,
        architecture: &Architecture,
    ) -> Result<HashMap<String, f64>, ArchitectError> {
        let mut scores = HashMap::new();
        
        for fitness_fn in &self.fitness_functions {
            let score = fitness_fn.evaluate(architecture).await?;
            scores.insert(fitness_fn.name().to_string(), score);
        }
        
        Ok(scores)
    }
}
```

### Architecture Validation and Testing

```rust
// Example: Architecture validation framework
pub struct ArchitectureValidator {
    structural_validator: StructuralValidator,
    behavioral_validator: BehavioralValidator,
    quality_validator: QualityAttributeValidator,
    compliance_checker: ComplianceChecker,
}

#[derive(Debug, Clone)]
pub struct ValidationReport {
    pub structural_issues: Vec<StructuralIssue>,
    pub behavioral_issues: Vec<BehavioralIssue>,
    pub quality_concerns: Vec<QualityConcern>,
    pub compliance_violations: Vec<ComplianceViolation>,
    pub overall_score: f64,
    pub recommendations: Vec<ValidationRecommendation>,
}

impl ArchitectureValidator {
    pub async fn validate_architecture(
        &mut self,
        architecture: &Architecture,
        requirements: &Requirements,
    ) -> Result<ValidationReport, ValidationError> {
        // Validate structure
        let structural_issues = self.structural_validator
            .validate_structure(architecture)
            .await?;
            
        // Validate behavior
        let behavioral_issues = self.behavioral_validator
            .validate_behavior(architecture, requirements)
            .await?;
            
        // Validate quality attributes
        let quality_concerns = self.quality_validator
            .validate_quality_attributes(architecture, &requirements.quality_attributes)
            .await?;
            
        // Check compliance
        let compliance_violations = self.compliance_checker
            .check_compliance(architecture, &requirements.compliance_requirements)
            .await?;
            
        // Calculate overall score
        let overall_score = self.calculate_validation_score(
            &structural_issues,
            &behavioral_issues,
            &quality_concerns,
            &compliance_violations
        );
        
        // Generate recommendations
        let recommendations = self.generate_recommendations(
            &structural_issues,
            &behavioral_issues,
            &quality_concerns,
            &compliance_violations
        );
        
        Ok(ValidationReport {
            structural_issues,
            behavioral_issues,
            quality_concerns,
            compliance_violations,
            overall_score,
            recommendations,
        })
    }
}
```

### Architectural Decision Records (ADR) Management

```rust
// Example: ADR creation and management
pub struct ADRManager {
    template_engine: ADRTemplateEngine,
    decision_analyzer: DecisionAnalyzer,
    impact_assessor: ImpactAssessor,
}

#[derive(Debug, Clone)]
pub struct ArchitecturalDecisionRecord {
    pub id: String,
    pub title: String,
    pub status: ADRStatus,
    pub context: String,
    pub decision: String,
    pub rationale: String,
    pub consequences: Vec<Consequence>,
    pub alternatives: Vec<Alternative>,
    pub related_decisions: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl ADRManager {
    pub async fn create_adr(
        &mut self,
        decision_context: &DecisionContext,
    ) -> Result<ArchitecturalDecisionRecord, ADRError> {
        // Analyze decision
        let analysis = self.decision_analyzer
            .analyze_decision(decision_context)
            .await?;
            
        // Assess impact
        let impact = self.impact_assessor
            .assess_impact(&analysis)
            .await?;
            
        // Generate ADR
        let adr = self.template_engine.generate_adr(
            decision_context,
            &analysis,
            &impact
        )?;
        
        // Link related decisions
        let related = self.find_related_decisions(&adr)?;
        
        Ok(ArchitecturalDecisionRecord {
            id: self.generate_adr_id(),
            title: adr.title,
            status: ADRStatus::Proposed,
            context: adr.context,
            decision: adr.decision,
            rationale: adr.rationale,
            consequences: impact.consequences,
            alternatives: analysis.alternatives,
            related_decisions: related,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        })
    }
}
```