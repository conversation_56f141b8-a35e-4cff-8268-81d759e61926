# Reviewer Mode

## Purpose and Use Cases

The Reviewer mode specializes in code review, quality assessment, and standards enforcement. Reviewer agents ensure code quality, security, and maintainability through systematic evaluation and constructive feedback.

### Primary Use Cases
- Conducting thorough code reviews
- Enforcing coding standards and best practices
- Identifying security vulnerabilities
- Assessing architectural compliance
- Providing mentorship through feedback

## Rust Code Examples

### SPARC Reviewer Mode Trait Definition

```rust
// Example: Reviewer Mode trait for SPARC system
pub trait ReviewerMode: Send + Sync {
    fn name(&self) -> &'static str { "Reviewer" }
    fn description(&self) -> &'static str { 
        "Code review and quality assessment mode"
    }
    
    // Core review methods
    async fn review_code(&mut self, code: &Code) -> Result<ReviewReport, ModeError>;
    async fn check_standards(&mut self, code: &Code, standards: &Standards) -> Result<ComplianceReport, ModeError>;
    async fn identify_issues(&mut self, code: &Code) -> Result<Vec<Issue>, ModeError>;
    
    // Feedback generation
    async fn generate_feedback(&mut self, issues: &[Issue]) -> Result<Vec<Comment>, ModeError>;
    async fn suggest_improvements(&mut self, code: &Code) -> Result<Vec<Suggestion>, ModeError>;
}

// Review finding representation
#[derive(Debug, Clone)]
pub struct ReviewFinding {
    pub severity: Severity,
    pub category: IssueCategory,
    pub location: CodeLocation,
    pub description: String,
    pub suggestion: Option<String>,
    pub example: Option<CodeSnippet>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum Severity {
    Critical,  // Security vulnerabilities, data loss risks
    Major,     // Bugs, performance issues, design flaws
    Minor,     // Code style, naming, organization
    Info,      // Suggestions and best practices
}

#[derive(Debug, Clone)]
pub enum IssueCategory {
    Security(SecurityIssue),
    Performance(PerformanceIssue),
    Logic(LogicError),
    Style(StyleViolation),
    Maintainability(MaintainabilityIssue),
    Documentation(DocIssue),
}
```

### Review State Machine

```rust
// State machine for code review process
#[derive(Debug, Clone)]
pub enum ReviewState {
    Initializing {
        code_path: PathBuf,
        review_config: ReviewConfig,
    },
    Analyzing {
        code: Code,
        checkers: Vec<Box<dyn Checker>>,
    },
    CollectingIssues {
        code: Code,
        findings: Vec<ReviewFinding>,
    },
    GeneratingFeedback {
        findings: Vec<ReviewFinding>,
        context: ReviewContext,
    },
    Finalizing {
        report: ReviewReport,
        suggestions: Vec<Suggestion>,
    },
    Complete {
        report: ReviewReport,
        metrics: ReviewMetrics,
    },
}

impl ReviewState {
    pub fn transition(&mut self, event: ReviewEvent) -> Result<(), StateError> {
        match (self.clone(), event) {
            (ReviewState::Initializing { code_path, .. }, 
             ReviewEvent::CodeLoaded(code)) => {
                *self = ReviewState::Analyzing {
                    code,
                    checkers: Vec::new(),
                };
                Ok(())
            }
            (ReviewState::Analyzing { code, checkers }, 
             ReviewEvent::AnalysisComplete(findings)) => {
                *self = ReviewState::CollectingIssues {
                    code,
                    findings,
                };
                Ok(())
            }
            // ... other transitions
            _ => Err(StateError::InvalidTransition),
        }
    }
}
```

## Key Behaviors and Characteristics

### Core Behaviors
- **Systematic Review**: Methodical examination of code
- **Pattern Recognition**: Identifies anti-patterns and issues
- **Constructive Feedback**: Provides actionable improvements
- **Standards Enforcement**: Ensures compliance with guidelines
- **Knowledge Sharing**: Educates through review comments

### Unique Characteristics
- Deep understanding of code quality metrics
- Ability to spot subtle bugs and issues
- Balance between perfectionism and pragmatism
- Strong communication skills
- Mentoring and teaching approach

## When to Use This Mode

Deploy Reviewer agents when:
- Code is ready for quality assessment
- Before merging critical changes
- Establishing code quality baselines
- Training team on best practices
- Performing security audits

## Integration Points

### Works Well With
- **Coder**: Reviews implementation quality
- **Architect**: Validates design compliance
- **Tester**: Identifies testing gaps
- **Debugger**: Spots potential issues
- **Documenter**: Ensures documentation quality

### Communication Patterns
- Receives code submissions from coders
- Provides feedback via comments
- Shares findings with orchestrators
- Collaborates with architects on standards
- Updates memory with review outcomes

## Success Criteria

Reviewer success is measured by:
1. **Issue Detection**: Finds real problems early
2. **Feedback Quality**: Clear, actionable comments
3. **Turnaround Time**: Timely review completion
4. **False Positive Rate**: Minimal incorrect flags
5. **Developer Growth**: Team skill improvement

## Best Practices

1. Review with empathy and respect
2. Focus on the code, not the coder
3. Provide specific examples for improvements
4. Acknowledge good patterns and practices
5. Prioritize issues by severity
6. Suggest solutions, not just problems

## Anti-Patterns to Avoid

- Nitpicking: Focus on significant issues
- Delayed Reviews: Provide timely feedback
- Vague Comments: Be specific and clear
- Imposing Style: Respect team conventions
- Review Fatigue: Take breaks for quality
- Missing Context: Understand the requirements

## Review Focus Areas

The Reviewer mode examines:
- **Correctness**: Logic and functionality
- **Security**: Vulnerabilities and risks
- **Performance**: Efficiency concerns
- **Maintainability**: Code clarity and structure
- **Testing**: Coverage and quality
- **Documentation**: Comments and docs

## Review Techniques

Reviewers employ:
- **Static Analysis**: Automated tool integration
- **Security Scanning**: Vulnerability detection
- **Complexity Analysis**: Identifying hotspots
- **Pattern Matching**: Recognizing anti-patterns
- **Dependency Review**: Third-party risks
- **Performance Profiling**: Efficiency analysis

## Feedback Categories

Reviews typically cover:
- **Critical**: Security vulnerabilities, data loss risks
- **Major**: Bugs, performance issues, design flaws
- **Minor**: Code style, naming, organization
- **Suggestions**: Improvements and optimizations
- **Positive**: Good practices to reinforce

The Reviewer mode serves as the quality gate, ensuring that code meets standards while fostering continuous improvement through constructive feedback.

## Review Pattern Examples

### Automated Review Checkers

```rust
// Example: Modular review checker system
pub trait ReviewChecker: Send + Sync {
    fn name(&self) -> &'static str;
    fn category(&self) -> IssueCategory;
    
    async fn check(&self, code: &Code) -> Result<Vec<ReviewFinding>, CheckError>;
}

// Security-focused checker
pub struct SecurityChecker {
    rules: Vec<SecurityRule>,
    vulnerability_db: VulnerabilityDatabase,
}

impl ReviewChecker for SecurityChecker {
    fn name(&self) -> &'static str { "Security Analyzer" }
    
    fn category(&self) -> IssueCategory {
        IssueCategory::Security(SecurityIssue::General)
    }
    
    async fn check(&self, code: &Code) -> Result<Vec<ReviewFinding>, CheckError> {
        let mut findings = Vec::new();
        
        // Check for SQL injection vulnerabilities
        if let Some(sql_risks) = self.check_sql_injection(code).await? {
            findings.extend(sql_risks);
        }
        
        // Check for hardcoded credentials
        if let Some(cred_issues) = self.check_hardcoded_secrets(code).await? {
            findings.extend(cred_issues);
        }
        
        // Check dependencies for known vulnerabilities
        if let Some(dep_vulns) = self.check_dependencies(code).await? {
            findings.extend(dep_vulns);
        }
        
        Ok(findings)
    }
}

// Performance-focused checker
pub struct PerformanceChecker {
    complexity_threshold: u32,
    benchmarks: BenchmarkDatabase,
}

impl ReviewChecker for PerformanceChecker {
    fn name(&self) -> &'static str { "Performance Analyzer" }
    
    fn category(&self) -> IssueCategory {
        IssueCategory::Performance(PerformanceIssue::General)
    }
    
    async fn check(&self, code: &Code) -> Result<Vec<ReviewFinding>, CheckError> {
        let mut findings = Vec::new();
        
        // Check algorithmic complexity
        for function in code.functions() {
            if let Some(complexity) = self.analyze_complexity(function)? {
                if complexity > self.complexity_threshold {
                    findings.push(ReviewFinding {
                        severity: Severity::Major,
                        category: self.category(),
                        location: function.location(),
                        description: format!(
                            "Function '{}' has high complexity: O({})",
                            function.name(),
                            complexity
                        ),
                        suggestion: Some("Consider breaking down into smaller functions".to_string()),
                        example: None,
                    });
                }
            }
        }
        
        Ok(findings)
    }
}
```

### Constructive Feedback Generation

```rust
// Example: Generating helpful review comments
pub struct FeedbackGenerator {
    tone: ReviewTone,
    expertise_level: ExpertiseLevel,
}

#[derive(Debug, Clone)]
pub enum ReviewTone {
    Encouraging,
    Neutral,
    Direct,
}

impl FeedbackGenerator {
    pub fn generate_comment(&self, finding: &ReviewFinding) -> Comment {
        let base_message = match finding.severity {
            Severity::Critical => self.format_critical(finding),
            Severity::Major => self.format_major(finding),
            Severity::Minor => self.format_minor(finding),
            Severity::Info => self.format_info(finding),
        };
        
        Comment {
            line: finding.location.line,
            message: base_message,
            suggestion: finding.suggestion.clone(),
            example: self.generate_example(finding),
            references: self.get_references(finding),
        }
    }
    
    fn format_critical(&self, finding: &ReviewFinding) -> String {
        match self.tone {
            ReviewTone::Encouraging => format!(
                "I found a critical issue that needs attention: {}. \
                 Let's fix this together to ensure system security!",
                finding.description
            ),
            ReviewTone::Neutral => format!(
                "Critical issue detected: {}",
                finding.description
            ),
            ReviewTone::Direct => format!(
                "CRITICAL: {}. This must be fixed before merging.",
                finding.description
            ),
        }
    }
    
    fn generate_example(&self, finding: &ReviewFinding) -> Option<CodeExample> {
        match &finding.category {
            IssueCategory::Security(_) => Some(self.security_example(finding)),
            IssueCategory::Performance(_) => Some(self.performance_example(finding)),
            _ => finding.example.clone().map(|e| CodeExample::from(e)),
        }
    }
}
```

### Review Coordination

```rust
// Example: Coordinating review with other modes
pub struct ReviewCoordinator {
    reviewer_mode: Box<dyn ReviewerMode>,
    tester_mode: Option<Box<dyn TesterMode>>,
    architect_mode: Option<Box<dyn ArchitectMode>>,
}

impl ReviewCoordinator {
    pub async fn comprehensive_review(
        &mut self,
        code: &Code,
    ) -> Result<ComprehensiveReview, CoordinationError> {
        // Step 1: Basic code review
        let review_report = self.reviewer_mode
            .review_code(code)
            .await?;
            
        // Step 2: Check test coverage if tester available
        let test_coverage = if let Some(tester) = &mut self.tester_mode {
            Some(tester.analyze_test_coverage(code).await?)
        } else {
            None
        };
        
        // Step 3: Verify architectural compliance if architect available
        let arch_compliance = if let Some(architect) = &mut self.architect_mode {
            Some(architect.check_architecture_compliance(code).await?)
        } else {
            None
        };
        
        // Step 4: Combine all findings
        Ok(ComprehensiveReview {
            code_issues: review_report.findings,
            test_gaps: test_coverage.map(|tc| tc.uncovered_paths),
            architecture_violations: arch_compliance.map(|ac| ac.violations),
            overall_score: self.calculate_score(&review_report),
            recommendations: self.generate_recommendations(&review_report),
        })
    }
}
```

### Review Metrics and Analytics

```rust
// Example: Tracking review metrics for continuous improvement
#[derive(Debug, Clone)]
pub struct ReviewMetrics {
    pub total_issues: usize,
    pub issues_by_severity: HashMap<Severity, usize>,
    pub issues_by_category: HashMap<String, usize>,
    pub review_duration: Duration,
    pub lines_reviewed: usize,
    pub complexity_score: f64,
}

impl ReviewMetrics {
    pub fn calculate_quality_score(&self) -> f64 {
        let severity_weights = HashMap::from([
            (Severity::Critical, 10.0),
            (Severity::Major, 5.0),
            (Severity::Minor, 1.0),
            (Severity::Info, 0.1),
        ]);
        
        let weighted_issues: f64 = self.issues_by_severity
            .iter()
            .map(|(sev, count)| {
                severity_weights.get(sev).unwrap_or(&1.0) * (*count as f64)
            })
            .sum();
            
        let base_score = 100.0;
        let penalty_per_kloc = weighted_issues / (self.lines_reviewed as f64 / 1000.0);
        
        (base_score - penalty_per_kloc).max(0.0)
    }
    
    pub fn generate_insights(&self) -> Vec<Insight> {
        let mut insights = Vec::new();
        
        // Identify problem areas
        if let Some((category, count)) = self.issues_by_category.iter().max_by_key(|&(_, v)| v) {
            insights.push(Insight {
                title: "Most Common Issue Type".to_string(),
                description: format!(
                    "{} issues found in category '{}', consider team training",
                    count, category
                ),
                priority: InsightPriority::High,
            });
        }
        
        // Review efficiency
        let review_speed = self.lines_reviewed as f64 / self.review_duration.as_secs() as f64;
        if review_speed < 10.0 {
            insights.push(Insight {
                title: "Slow Review Speed".to_string(),
                description: "Consider using more automated checkers".to_string(),
                priority: InsightPriority::Medium,
            });
        }
        
        insights
    }
}
```