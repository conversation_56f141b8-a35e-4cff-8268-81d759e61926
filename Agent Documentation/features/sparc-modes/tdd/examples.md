# TDD Mode Usage Examples and Workflows

## Basic TDD Workflow Examples
Based on real claude-flow SPARC TDD implementations documented in context7.

## Example 1: User Authentication System
Complete TDD implementation of authentication following the claude-flow TDD guide.

### Step 1: Architecture Design
```bash
# Design authentication system architecture first
../claude-flow sparc run architect \
  "Design authentication service architecture:
   - API endpoints
   - Database schema
   - Security layers
   - Integration points"
```

### Step 2: TDD Implementation
```bash
# Start TDD implementation using architecture specifications
../claude-flow sparc tdd \
  "Implement user authentication system" \
  --spec ./output/auth-spec.md \
  --architecture ./output/auth-architecture.md
```

**Expected Output Structure:**
```
tdd-output/
├── tests/
│   ├── user-auth.test.ts          # RED phase - failing tests
│   ├── integration.test.ts        # Integration test suite
│   └── security.test.ts           # Security-specific tests
├── implementation/
│   ├── auth-service.ts            # GREEN phase - minimal implementation
│   ├── user-repository.ts         # Data layer
│   └── jwt-manager.ts             # Token management
├── refactored/
│   ├── auth-service-v2.ts         # REFACTOR phase - improved code
│   └── security-middleware.ts     # Additional security layer
└── reports/
    ├── tdd-session-report.md      # Complete TDD cycle report
    ├── coverage-report.html       # Test coverage analysis
    └── quality-metrics.json       # Code quality metrics
```

### RED Phase Example Output
```typescript
// Generated failing test (auth-service.test.ts)
describe('AuthService', () => {
  test('should hash password with bcrypt', async () => {
    const authService = new AuthService();
    const hashedPassword = await authService.hashPassword('password123');
    
    expect(hashedPassword).toBeDefined();
    expect(hashedPassword).not.toBe('password123');
    expect(await bcrypt.compare('password123', hashedPassword)).toBe(true);
  });
  
  test('should validate user credentials', async () => {
    const authService = new AuthService();
    const result = await authService.validateCredentials('<EMAIL>', 'password123');
    
    expect(result).toHaveProperty('token');
    expect(result).toHaveProperty('user');
    expect(result.user.email).toBe('<EMAIL>');
  });
});
```

### GREEN Phase Example Output
```typescript
// Minimal implementation to pass tests (auth-service.ts)
import bcrypt from 'bcrypt';

export class AuthService {
  async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(password, salt);
  }
  
  async validateCredentials(email: string, password: string) {
    // Minimal implementation to pass tests
    const user = await this.findUserByEmail(email);
    if (!user) throw new Error('User not found');
    
    const isValid = await bcrypt.compare(password, user.hashedPassword);
    if (!isValid) throw new Error('Invalid password');
    
    return {
      token: 'mock-jwt-token',
      user: { email: user.email, id: user.id }
    };
  }
  
  private async findUserByEmail(email: string) {
    // Mock implementation for GREEN phase
    return {
      id: 1,
      email,
      hashedPassword: await this.hashPassword('password123')
    };
  }
}
```

## Example 2: REST API with Pattern Implementation
Using claude-flow's pattern system for CRUD operations.

```bash
# Implement user CRUD with REST API pattern
../claude-flow sparc tdd "implement user CRUD" \
  --pattern "rest-api" \
  --with-validation
```

**Generated Test Suite:**
```typescript
describe('User CRUD API', () => {
  describe('POST /api/v1/users', () => {
    test('should create user with valid data', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'securePassword123'
      };
      
      const response = await request(app)
        .post('/api/v1/users')
        .send(userData)
        .expect(201);
      
      expect(response.body).toHaveProperty('id');
      expect(response.body.email).toBe(userData.email);
      expect(response.body).not.toHaveProperty('password');
    });
    
    test('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/v1/users')
        .send({})
        .expect(400);
      
      expect(response.body.errors).toContain('Name is required');
      expect(response.body.errors).toContain('Email is required');
    });
  });
});
```

## Example 3: Event-Driven System with Pub-Sub Pattern
```bash
# Implement event handlers with pub-sub pattern
../claude-flow sparc tdd "implement event handlers" \
  --pattern "pub-sub" \
  --async
```

**Generated Event Handler Tests:**
```typescript
describe('Event Handlers', () => {
  test('should process user registration events', async () => {
    const eventBus = new EventBus();
    const userHandler = new UserRegistrationHandler();
    
    eventBus.subscribe('user.registered', userHandler.handle);
    
    const event = {
      type: 'user.registered',
      payload: { userId: 123, email: '<EMAIL>' }
    };
    
    await eventBus.publish(event);
    
    expect(userHandler.processedEvents).toContain(event);
  });
  
  test('should handle event processing failures gracefully', async () => {
    const eventBus = new EventBus();
    const faultyHandler = new FaultyHandler();
    
    eventBus.subscribe('test.event', faultyHandler.handle);
    
    const event = { type: 'test.event', payload: {} };
    
    await expect(eventBus.publish(event)).not.toThrow();
    expect(eventBus.getFailedEvents()).toContain(event);
  });
});
```

## Example 4: Login Flow Implementation
```bash
# Implement login flow with authentication pattern
../claude-flow sparc tdd "implement login flow" \
  --pattern "request-validate-authenticate-respond"
```

**Complete Login Flow Test:**
```typescript
describe('Login Flow', () => {
  test('should complete full authentication flow', async () => {
    // Request Phase
    const loginRequest = {
      email: '<EMAIL>',
      password: 'password123'
    };
    
    // Validate Phase
    const validation = await validateLoginRequest(loginRequest);
    expect(validation.isValid).toBe(true);
    
    // Authenticate Phase
    const authResult = await authenticateUser(loginRequest);
    expect(authResult.success).toBe(true);
    expect(authResult.user).toBeDefined();
    
    // Respond Phase
    const response = await generateLoginResponse(authResult);
    expect(response.token).toBeDefined();
    expect(response.user).not.toHaveProperty('password');
    expect(response.expiresAt).toBeDefined();
  });
});
```

## Example 5: Parallel TDD Execution
Multiple TDD sessions running in parallel using batchtool.

```bash
# Run multiple TDD implementations in parallel
batchtool run --parallel \
  "npx claude-flow sparc tdd 'user authentication' --non-interactive" \
  "npx claude-flow sparc tdd 'payment processing' --non-interactive" \
  "npx claude-flow sparc tdd 'notification system' --non-interactive"
```

**Coordination Example:**
```bash
# Develop microservices concurrently with TDD
batchtool run --parallel --tag "microservices" \
  "npx claude-flow sparc tdd 'user-service' --output ./services/user" \
  "npx claude-flow sparc tdd 'auth-service' --output ./services/auth" \
  "npx claude-flow sparc tdd 'notification-service' --output ./services/notifications" \
  "npx claude-flow sparc tdd 'payment-service' --output ./services/payment"

# Then integrate with sequential execution
batchtool run --sequential \
  "npx claude-flow sparc run integration 'integrate all microservices'" \
  "npx claude-flow sparc run tdd 'end-to-end testing'" \
  "npx claude-flow sparc run security-review 'security audit'"
```

## Example 6: Full Development Workflow Integration
Complete development pipeline with TDD as central component.

```bash
# Complete application development with SPARC + TDD
batchtool orchestrate --name "full-app-development" --boomerang \
  --phase1-parallel "Research Phase" \
    "npx claude-flow sparc run researcher 'research authentication best practices'" \
    "npx claude-flow sparc run researcher 'analyze security requirements'" \
  --phase2-sequential "Design Phase" \
    "npx claude-flow sparc run architect 'design system architecture'" \
    "npx claude-flow sparc run spec-pseudocode 'create detailed specifications'" \
  --phase3-parallel "Implementation Phase" \
    "npx claude-flow sparc tdd 'implement user authentication'" \
    "npx claude-flow sparc tdd 'implement payment processing'" \
    "npx claude-flow sparc tdd 'implement notification system'" \
  --phase4-sequential "Integration Phase" \
    "npx claude-flow sparc run integration 'integrate all components'" \
    "npx claude-flow sparc tdd 'comprehensive integration testing'" \
  --phase5-parallel "Quality Phase" \
    "npx claude-flow sparc run security-review 'security audit'" \
    "npx claude-flow sparc run optimizer 'performance optimization'" \
    "npx claude-flow sparc run docs-writer 'complete documentation'"
```

## Example 7: Memory-Driven TDD Coordination
Using claude-flow's memory bank for cross-session coordination.

```bash
# Store architecture decisions for TDD reference
./claude-flow memory store "system_architecture" "Microservices with API Gateway pattern"

# Implement user service referencing stored architecture
./claude-flow sparc tdd "Implement user service based on system_architecture in memory"

# Create integration tests based on architecture
./claude-flow sparc tdd "Create integration tests for microservices architecture"
```

**Memory Integration Example:**
```typescript
// TDD session with memory coordination
const tddSession = await claudeFlow.sparc.tdd({
  prompt: "implement user service",
  memoryContext: {
    architectureKey: "system_architecture",
    requirementsKey: "user_service_requirements",
    patternsKey: "established_patterns"
  },
  storeResults: {
    key: "user_service_tdd_session",
    namespace: "development",
    includeArtifacts: true
  }
});
```

## Example 8: Performance-Focused TDD
TDD with performance testing and optimization.

```bash
# TDD with performance requirements
./claude-flow sparc tdd "implement high-performance data processor" \
  --performance-requirements "throughput:10000/sec,latency:<10ms" \
  --include-benchmarks \
  --optimize-for-speed
```

**Performance Test Example:**
```typescript
describe('Data Processor Performance', () => {
  test('should process 10,000 items per second', async () => {
    const processor = new DataProcessor();
    const testData = generateTestData(10000);
    
    const startTime = performance.now();
    await processor.processBatch(testData);
    const endTime = performance.now();
    
    const duration = endTime - startTime;
    const throughput = testData.length / (duration / 1000);
    
    expect(throughput).toBeGreaterThan(10000);
  });
  
  test('should maintain low latency under load', async () => {
    const processor = new DataProcessor();
    const latencies = [];
    
    for (let i = 0; i < 1000; i++) {
      const start = performance.now();
      await processor.processItem(generateTestItem());
      const end = performance.now();
      latencies.push(end - start);
    }
    
    const averageLatency = latencies.reduce((a, b) => a + b) / latencies.length;
    expect(averageLatency).toBeLessThan(10); // < 10ms
  });
});
```

## Example 9: Security-First TDD
TDD with integrated security testing.

```bash
# TDD with security focus
./claude-flow sparc tdd "implement secure API endpoints" \
  --security-patterns \
  --include-penetration-tests \
  --compliance OWASP
```

**Security Test Examples:**
```typescript
describe('API Security', () => {
  test('should prevent SQL injection', async () => {
    const maliciousInput = "'; DROP TABLE users; --";
    
    const response = await request(app)
      .get(`/api/users?search=${maliciousInput}`)
      .expect(400);
    
    expect(response.body.error).toContain('Invalid input');
  });
  
  test('should enforce rate limiting', async () => {
    const requests = Array(101).fill().map(() => 
      request(app).get('/api/users')
    );
    
    const responses = await Promise.all(requests);
    const rateLimitedResponses = responses.filter(r => r.status === 429);
    
    expect(rateLimitedResponses.length).toBeGreaterThan(0);
  });
});
```

## Example 10: CI/CD Integration
TDD in continuous integration pipelines.

```yaml
# GitHub Actions workflow with TDD
name: TDD Development Pipeline
on: [push, pull_request]

jobs:
  tdd-implementation:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Install Claude Flow
        run: npm install -g claude-flow
      
      - name: Run TDD Implementation
        run: |
          claude-flow sparc tdd "implement feature from issue" \
            --ci-mode \
            --non-interactive \
            --coverage-threshold 90 \
            --export-results ./tdd-results.json
      
      - name: Upload TDD Results
        uses: actions/upload-artifact@v2
        with:
          name: tdd-results
          path: ./tdd-results.json
```

## Common TDD Patterns and Templates

### Pattern: Microservice TDD
```bash
# Template for microservice TDD implementation
./claude-flow sparc tdd "implement ${SERVICE_NAME} microservice" \
  --template microservice \
  --include-health-checks \
  --include-metrics \
  --docker-integration \
  --k8s-ready
```

### Pattern: Database-First TDD
```bash
# TDD with database integration
./claude-flow sparc tdd "implement data access layer" \
  --database-first \
  --migrations-included \
  --transaction-tests \
  --connection-pooling
```

### Pattern: API-First TDD
```bash
# TDD starting with API specification
./claude-flow sparc tdd "implement REST API" \
  --openapi-spec ./api-spec.yaml \
  --contract-testing \
  --mock-external-dependencies
```