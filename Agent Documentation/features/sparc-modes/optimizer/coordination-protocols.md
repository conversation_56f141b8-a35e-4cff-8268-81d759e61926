# Optimizer Mode - Coordination Protocols

## Inter-Mode Communication Framework

### Memory-Driven Coordination Architecture

The Optimizer mode coordinates through structured memory patterns that enable asynchronous, semantic communication with other SPARC modes.

#### Memory Namespace Organization
```
optimizer/
├── context/          # Optimization context and requirements
├── analysis/         # Performance analysis results
├── strategies/       # Optimization plans and approaches
├── implementations/  # Executed optimizations
├── validation/       # Performance validation results
└── feedback/         # Lessons learned and improvements
```

### Coordination Event Types

#### Inbound Events (Optimizer Consumes)
- **performance:bottleneck_identified** - From Analyzer mode
- **code:complexity_detected** - From Coder mode  
- **test:performance_failure** - From Tester mode
- **monitor:threshold_exceeded** - From system monitoring
- **user:performance_complaint** - From user feedback systems

#### Outbound Events (Optimizer Produces)
- **optimization:plan_created** - To coordinating modes
- **optimization:implementation_ready** - To Coder mode
- **optimization:validation_required** - To Tester mode
- **optimization:completed** - To documentation and monitoring

### Protocol Specifications

#### Protocol 1: Performance Analysis Request
```javascript
// Memory Entry Pattern
{
  namespace: "optimizer/context",
  key: "analysis_request_[session_id]",
  data: {
    target_system: string,
    performance_concerns: array,
    current_metrics: object,
    business_constraints: object,
    timeline: string,
    priority: "high" | "medium" | "low"
  },
  tags: ["request", "analysis", "performance"],
  metadata: {
    requester: "analyzer|architect|orchestrator",
    urgency: number,
    dependencies: array
  }
}
```

#### Protocol 2: Optimization Plan Distribution
```javascript
// Memory Entry Pattern
{
  namespace: "optimizer/strategies",
  key: "optimization_plan_[component]_[timestamp]",
  data: {
    target_component: string,
    optimization_strategy: string,
    expected_impact: object,
    implementation_phases: array,
    resource_requirements: object,
    risk_assessment: object,
    validation_criteria: array
  },
  tags: ["plan", "strategy", "coordination"],
  metadata: {
    stakeholders: array,
    approval_required: boolean,
    dependencies: array
  }
}
```

#### Protocol 3: Implementation Coordination
```javascript
// Memory Entry Pattern
{
  namespace: "optimizer/implementations",
  key: "implementation_[optimization_id]",
  data: {
    optimization_id: string,
    implementation_status: "planned|in_progress|completed|failed",
    assigned_agents: array,
    code_changes: object,
    test_requirements: array,
    deployment_plan: object,
    rollback_strategy: object
  },
  tags: ["implementation", "coordination", "tracking"],
  metadata: {
    started_at: timestamp,
    estimated_completion: timestamp,
    progress_indicators: array
  }
}
```

## Agent Interaction Patterns

### Optimizer ↔ Analyzer Coordination

#### Information Flow
1. **Analyzer** provides performance metrics and bottleneck analysis
2. **Optimizer** processes analysis and identifies optimization opportunities  
3. **Optimizer** requests additional profiling data if needed
4. **Analyzer** provides deeper analysis for targeted optimization areas

#### Coordination Memory Pattern
```javascript
{
  namespace: "coordination/optimizer_analyzer",
  key: "session_[id]",
  data: {
    analysis_results: object,      // From Analyzer
    optimization_requests: array,  // From Optimizer
    collaborative_insights: array, // Joint findings
    next_analysis_targets: array   // Optimization-driven analysis needs
  }
}
```

### Optimizer ↔ Coder Coordination

#### Implementation Handoff Protocol
1. **Optimizer** creates detailed implementation specifications
2. **Coder** reviews feasibility and provides implementation feedback
3. **Optimizer** adjusts plan based on technical constraints
4. **Coder** implements optimizations with progress updates
5. **Optimizer** validates implementation against performance goals

#### Specification Format
```javascript
{
  namespace: "coordination/optimizer_coder",
  key: "implementation_spec_[id]",
  data: {
    optimization_objective: string,
    target_components: array,
    implementation_approach: object,
    code_patterns: array,
    performance_targets: object,
    testing_requirements: array,
    review_criteria: array
  }
}
```

### Optimizer ↔ Tester Coordination

#### Performance Validation Protocol
1. **Optimizer** defines performance test requirements
2. **Tester** designs and implements performance test suite
3. **Optimizer** provides baseline metrics and success criteria
4. **Tester** executes tests and reports results
5. **Optimizer** analyzes results and determines next steps

#### Test Specification Pattern
```javascript
{
  namespace: "coordination/optimizer_tester",
  key: "test_spec_[optimization_id]",
  data: {
    test_objectives: array,
    performance_metrics: object,
    test_scenarios: array,
    baseline_requirements: object,
    success_criteria: object,
    regression_checks: array
  }
}
```

## Coordination State Management

### Session Coordination Context
```javascript
{
  namespace: "optimizer/sessions",
  key: "session_[id]",
  data: {
    session_id: string,
    optimization_objective: string,
    participating_agents: array,
    current_phase: string,
    shared_context: object,
    coordination_history: array,
    next_actions: array
  }
}
```

### Cross-Mode Dependencies
- **Architecture Dependencies**: Optimization must respect system design
- **Testing Dependencies**: Performance tests must validate optimizations
- **Documentation Dependencies**: Optimization changes need documentation
- **Deployment Dependencies**: Optimization rollout requires coordination

### Conflict Resolution Protocols

#### Performance vs. Maintainability Conflicts
1. **Detection**: Optimization improves performance but reduces code clarity
2. **Escalation**: Flag for Architect review and decision
3. **Resolution**: Architect provides design guidance for balanced solution
4. **Implementation**: Optimizer adjusts approach based on architectural constraints

#### Resource Allocation Conflicts
1. **Detection**: Multiple optimization requests compete for resources
2. **Prioritization**: Apply impact vs. cost decision framework
3. **Coordination**: Negotiate with other modes for resource sharing
4. **Resolution**: Implement highest-impact optimizations first

## Feedback Loop Integration

### Performance Monitoring Integration
```javascript
{
  namespace: "optimizer/monitoring",
  key: "performance_tracking_[component]",
  data: {
    baseline_metrics: object,
    current_metrics: object,
    optimization_history: array,
    trend_analysis: object,
    alert_conditions: array,
    improvement_opportunities: array
  }
}
```

### Learning from Coordination Outcomes
- **Successful Coordination Patterns**: Document effective interaction patterns
- **Communication Failures**: Analyze and improve coordination protocols
- **Decision Quality**: Track optimization decision effectiveness
- **Adaptation**: Evolve coordination approaches based on experience

This coordination framework ensures the Optimizer mode can effectively collaborate with other SPARC modes while maintaining clear communication protocols and shared understanding of optimization objectives and constraints.