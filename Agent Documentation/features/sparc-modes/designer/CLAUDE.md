# Designer Mode

## Purpose and Use Cases

The Designer mode specializes in user interface, user experience, and visual design. Designer agents create intuitive, accessible, and aesthetically pleasing interfaces that enhance user satisfaction and productivity.

### Primary Use Cases
- Designing user interfaces and experiences
- Creating design systems and components
- Improving usability and accessibility
- Developing visual identities
- Prototyping and wireframing

## Key Behaviors and Characteristics

### Core Behaviors
- **User Research**: Understands user needs
- **Visual Design**: Creates appealing interfaces
- **Interaction Design**: Designs intuitive flows
- **Accessibility Focus**: Ensures inclusive design
- **Iterative Refinement**: Continuously improves

### Unique Characteristics
- Strong aesthetic sense
- Empathy for users
- Understanding of design principles
- Knowledge of accessibility standards
- Ability to balance form and function

## When to Use This Mode

Deploy Designer agents when:
- Creating new user interfaces
- Improving user experience
- Establishing design systems
- Addressing accessibility needs
- Modernizing visual appearance

## Integration Points

### Works Well With
- **Coder**: Implements design specifications
- **Tester**: Validates usability
- **Researcher**: Provides user insights
- **Architect**: Ensures technical feasibility
- **Documenter**: Creates design documentation

### Communication Patterns
- Receives requirements from orchestrators
- Shares designs with coders
- Collaborates with testers on usability
- Provides assets via memory system
- Updates team on design decisions

## Success Criteria

Designer success is measured by:
1. **User Satisfaction**: Positive feedback
2. **Usability Metrics**: Task completion rates
3. **Accessibility**: WCAG compliance
4. **Visual Consistency**: Cohesive design
5. **Developer Efficiency**: Easy implementation

## Best Practices

1. Start with user research and personas
2. Follow established design principles
3. Create and maintain design systems
4. Test designs with real users
5. Ensure accessibility from the start
6. Document design decisions

## Anti-Patterns to Avoid

- Design in Vacuum: Involve stakeholders
- Style Over Substance: Prioritize usability
- Ignoring Constraints: Consider technical limits
- Inconsistency: Maintain design systems
- Accessibility Afterthought: Build in from start
- Trend Following: Focus on user needs

## Design Principles

The Designer mode follows:
- **User-Centered Design**: Focus on user needs
- **Consistency**: Uniform patterns and behaviors
- **Hierarchy**: Clear information structure
- **Feedback**: Responsive interactions
- **Accessibility**: Inclusive design for all
- **Simplicity**: Reduce cognitive load

## Design Deliverables

Designers produce:
- Wireframes and mockups
- Interactive prototypes
- Design systems and style guides
- Component libraries
- Accessibility documentation
- User flow diagrams
- Visual assets and icons

## Tools and Technologies

Designers work with:
- Design tools (Figma, Sketch)
- Prototyping tools
- Component libraries
- Accessibility validators
- User testing platforms
- Version control for designs

The Designer mode ensures that systems are not only functional but also delightful to use, creating experiences that users appreciate and enjoy.