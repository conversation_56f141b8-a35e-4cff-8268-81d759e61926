# Researcher Mode - Mode Transitions

## Overview
The researcher mode typically operates at the beginning of workflows, providing foundational knowledge and recommendations that inform subsequent development phases.

## Incoming Transitions

### 1. From Orchestrator Mode
```typescript
// Initial research request
interface OrchestratorToResearcherTransition {
    trigger: "Need for investigation or analysis";
    inputs: {
        researchQuestion: "What needs to be researched";
        constraints: "Time, budget, or technical limitations";
        context: "Project requirements and goals";
        priorities: "What matters most";
    };
    expectedOutput: "Comprehensive research report";
}
```

### 2. From Debugger Mode
```typescript
// Research for debugging solutions
const debuggerToResearcherFlow = {
    trigger: "Unknown error or complex issue",
    inputs: {
        errorDescription: "Nature of the problem",
        stackTrace: "Technical error details",
        environment: "System configuration",
        attempts: "What has been tried"
    },
    research: "Find similar issues and solutions"
};
```

### 3. From Architect Mode
```bash
# Architecture decision research
./claude-flow sparc run architect "Initial system design"
# Needs more information
./claude-flow sparc run researcher "Research microservices vs serverless for our use case"
```

### 4. From Reviewer Mode
```python
# Security concern research
transition_from_reviewer = {
    "security_concern": "Potential vulnerability identified",
    "research_needed": "Latest mitigation strategies",
    "compliance_check": "Regulatory requirements",
    "best_practices": "Industry standards"
}
```

## Outgoing Transitions

### 1. To Architect Mode
```typescript
// Research informs architecture
const researcherToArchitectTransition = {
    trigger: "Research complete, ready for design",
    outputs: {
        recommendations: "Technology choices",
        patterns: "Recommended architectures",
        constraints: "Technical limitations discovered",
        risks: "Identified challenges"
    },
    nextAction: "Design system architecture"
};
```

### 2. To Coder Mode
```bash
# Direct implementation after research
./claude-flow sparc run researcher "Research best practices for JWT implementation"
# Then implement
./claude-flow sparc run coder "Implement JWT authentication following researched best practices"
```

### 3. To TDD Mode
```python
# Research informing test strategy
tdd_handoff = {
    "testing_strategies": "Recommended testing approaches",
    "coverage_targets": "Industry standard coverage",
    "tools": "Suggested testing frameworks",
    "patterns": "Testing best practices"
}
```

### 4. To Designer Mode
```typescript
// UI/UX research transition
interface ResearcherToDesignerFlow {
    userResearch: {
        patterns: "Common UI patterns researched";
        accessibility: "WCAG compliance requirements";
        trends: "Current design trends";
        competitors: "Competitive analysis";
    };
    recommendations: "Design direction suggestions";
}
```

### 5. To Memory Manager Mode
```bash
# Store research findings
./claude-flow sparc run researcher "Research modern web frameworks"
# Store findings
./claude-flow memory store "framework_research" "Key findings and recommendations"
```

## Parallel Transitions

### 1. Multi-Domain Research
```bash
# Parallel research streams
batchtool run --parallel \
  "npx claude-flow sparc run researcher 'Research frontend frameworks'" \
  "npx claude-flow sparc run researcher 'Research backend technologies'" \
  "npx claude-flow sparc run researcher 'Research deployment options'"
```

### 2. Comprehensive Analysis
```typescript
// Coordinated research efforts
const parallelResearch = {
    technical: "Technology evaluation",
    market: "Competitive analysis",
    security: "Security assessment",
    cost: "Financial analysis",
    convergence: "Synthesize all findings"
};
```

## Sequential Transitions

### 1. Research-Design-Build Flow
```mermaid
graph LR
    A[Researcher] --> B[Architect]
    B --> C[Coder]
    C --> D[Tester]
    D --> E[Reviewer]
```

### 2. Iterative Research
```mermaid
graph LR
    A[Initial Research] --> B[Architect]
    B --> C{Need More Info?}
    C -->|Yes| D[Deeper Research]
    D --> B
    C -->|No| E[Coder]
```

## Conditional Transitions

### 1. Confidence-Based Transitions
```python
# Transition based on research confidence
research_confidence = assess_research_quality()

if research_confidence >= 0.8:
    transition_to("architect")  # High confidence, proceed
elif research_confidence >= 0.6:
    transition_to("researcher")  # More research needed
else:
    transition_to("innovator")  # Need creative solutions
```

### 2. Complexity-Based Routing
```typescript
// Route based on findings
const complexityRouting = {
    simple: "researcher → coder",
    moderate: "researcher → architect → coder",
    complex: "researcher → architect → designer → coder",
    unknown: "researcher → innovator → architect"
};
```

## Workflow Integration Examples

### 1. Feature Development Flow
```bash
# Complete feature development workflow
./claude-flow sparc run researcher "Research real-time collaboration techniques"
./claude-flow sparc run architect "Design real-time collaboration system"
./claude-flow sparc run coder "Implement WebSocket-based collaboration"
./claude-flow sparc run tester "Test real-time features"
```

### 2. Technology Migration Flow
```bash
# Migration research and planning
./claude-flow sparc run researcher "Research migration from MongoDB to PostgreSQL"
./claude-flow sparc run architect "Design migration architecture"
./claude-flow sparc run workflow-manager "Create migration workflow"
```

### 3. Security Enhancement Flow
```python
# Security-focused workflow
workflow = [
    ("researcher", "Research latest security threats"),
    ("researcher", "Analyze our vulnerability surface"),
    ("architect", "Design security enhancements"),
    ("coder", "Implement security measures"),
    ("tester", "Security testing"),
    ("reviewer", "Security audit")
]
```

## Transition Commands

### 1. Direct Transitions
```bash
# Explicit transition with context
./claude-flow sparc transition \
  --from researcher \
  --to architect \
  --context "Research complete, frameworks selected"
```

### 2. Conditional Transitions
```bash
# Transition with conditions
./claude-flow sparc run researcher "Evaluate cloud providers" \
  --on-complete "architect" \
  --on-incomplete "researcher" \
  --confidence-threshold 0.75
```

## State Management During Transitions

### 1. Research Context Preservation
```typescript
// Maintaining research context
const researchContext = {
    findings: {
        primary: "Main recommendations",
        alternatives: "Other viable options",
        rejected: "Options ruled out with reasons"
    },
    evidence: {
        sources: "Research sources",
        data: "Supporting data",
        confidence: "Confidence levels"
    },
    constraints: "Discovered limitations"
};
```

### 2. Memory Bank Storage
```bash
# Store research for future reference
./claude-flow memory store "research_findings" '{
  "topic": "Authentication methods",
  "recommendations": ["JWT", "OAuth2"],
  "pros_cons": {...},
  "implementation_notes": {...}
}'
```

## Transition Patterns

### 1. Exploratory Pattern
```mermaid
graph TD
    A[Researcher] --> B{Clear Direction?}
    B -->|Yes| C[Architect]
    B -->|No| D[More Research]
    D --> A
    B -->|Innovative| E[Innovator]
```

### 2. Validation Pattern
```python
# Research validation flow
validation_pattern = {
    "initial_research": "Broad investigation",
    "architect_review": "Feasibility check",
    "focused_research": "Deep dive if needed",
    "final_validation": "Confirm approach"
}
```

### 3. Continuous Learning Pattern
```typescript
// Ongoing research integration
const continuousLearning = {
    phases: [
        "Initial research",
        "Implementation",
        "Feedback collection",
        "Research updates",
        "Iteration"
    ],
    triggers: [
        "New technology releases",
        "Security advisories",
        "Performance issues",
        "User feedback"
    ]
};
```

## Best Practices for Transitions

### 1. Complete Handoff Checklist
```python
# Research completion checklist
handoff_checklist = {
    "executive_summary": True,
    "detailed_findings": True,
    "recommendations": True,
    "risks_identified": True,
    "references": True,
    "confidence_levels": True,
    "next_steps": True
}
```

### 2. Transition Quality Gates
```typescript
// Quality requirements for transition
const qualityGates = {
    coverage: "All research questions answered",
    evidence: "Sufficient supporting data",
    clarity: "Clear recommendations",
    actionable: "Next steps defined",
    documented: "Findings properly recorded"
};
```

### 3. Feedback Loops
```bash
# Enable feedback from downstream modes
./claude-flow sparc run researcher "API gateway research" \
  --enable-feedback \
  --feedback-to "researcher" \
  --feedback-trigger "implementation-issues"
```