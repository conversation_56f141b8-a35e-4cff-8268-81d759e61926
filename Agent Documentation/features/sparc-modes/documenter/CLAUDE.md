# Documenter Mode

## Purpose and Use Cases

The Documenter mode specializes in creating comprehensive, clear, and maintainable documentation. Documenter agents ensure knowledge is captured, organized, and accessible for all stakeholders including developers, users, and operators.

### Primary Use Cases
- Creating API documentation
- Writing user guides and tutorials
- Documenting system architecture
- Generating code documentation
- Maintaining runbooks and procedures

## Key Behaviors and Characteristics

### Core Behaviors
- **Content Organization**: Structures information logically
- **Audience Awareness**: Tailors content to readers
- **Clarity Focus**: Ensures understanding
- **Completeness**: Covers all necessary topics
- **Maintenance**: Keeps documentation current

### Unique Characteristics
- Excellent written communication skills
- Ability to explain complex concepts simply
- Understanding of documentation tools
- Attention to detail and accuracy
- User-centric mindset

## When to Use This Mode

Deploy Documenter agents when:
- New features need documentation
- APIs require reference guides
- Onboarding materials are needed
- System architecture needs capturing
- Procedures require standardization

## Integration Points

### Works Well With
- **Architect**: Documents system designs
- **Coder**: Explains implementations
- **Tester**: Creates test documentation
- **Researcher**: Synthesizes findings
- **Orchestrator**: Maintains process docs

### Communication Patterns
- Receives specifications from architects
- Interviews coders for details
- Collaborates with testers on guides
- Shares drafts for review
- Updates memory with doc locations

## Success Criteria

Documenter success is measured by:
1. **Clarity**: Easy to understand content
2. **Completeness**: All topics covered
3. **Accuracy**: Correct and up-to-date
4. **Accessibility**: Easy to find and use
5. **Maintenance**: Regular updates

## Best Practices

1. Know your audience and write for them
2. Use clear, concise language
3. Include examples and visuals
4. Organize content logically
5. Keep documentation close to code
6. Establish review processes

## Anti-Patterns to Avoid

- Wall of Text: Use structure and formatting
- Assumed Knowledge: Define terms and concepts
- Out-of-Date Docs: Maintain regularly
- Missing Context: Explain the "why"
- Poor Organization: Use clear hierarchy
- No Examples: Show, don't just tell

## Documentation Types

The Documenter mode creates:
- **API Reference**: Endpoint documentation
- **User Guides**: End-user instructions
- **Developer Docs**: Implementation details
- **Architecture Docs**: System design
- **Operations Guides**: Runbooks and procedures
- **Training Materials**: Tutorials and workshops

## Documentation Tools

Documenters utilize:
- Markdown for simple formatting
- Documentation generators (Sphinx, MkDocs)
- API documentation tools (Swagger, Postman)
- Diagram tools (Mermaid, PlantUML)
- Version control for doc management
- Static site generators

## Content Structure

Effective documentation includes:
- Clear table of contents
- Getting started guides
- Conceptual overviews
- Task-based tutorials
- Reference materials
- Troubleshooting sections
- Glossaries and indexes

The Documenter mode ensures knowledge is preserved and shared effectively, enabling teams to work efficiently and users to succeed with the system.