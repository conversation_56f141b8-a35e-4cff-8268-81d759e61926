# CLI Configuration and Environment Management

## Overview

This document details configuration management patterns from claude-code-flow, showing how to implement hierarchical configuration, environment detection, and dynamic settings management for CLI applications.

## Rust Configuration Management

### Using config-rs for Hierarchical Configuration

```rust
use config::{Config, ConfigError, Environment, File, FileFormat, Value};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::{Arc, RwLock};
use notify::{Watcher, RecursiveMode, watcher};
use directories::{ProjectDirs, UserDirs};

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct AppConfig {
    pub version: String,
    pub log_level: LogLevel,
    pub max_concurrent_processes: u32,
    pub auto_save: bool,
    pub session_timeout: u64,
    pub memory_manager: MemoryConfig,
    pub mcp: McpConfig,
    pub sparc: SparcConfig,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[serde(rename_all = "lowercase")]
pub enum LogLevel {
    Debug,
    Info,
    Warn,
    Error,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct MemoryConfig {
    pub backend: MemoryBackend,
    pub max_entries: usize,
    pub persist_interval: u64,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[serde(rename_all = "lowercase")]
pub enum MemoryBackend {
    Memory,
    File,
    Hybrid,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct McpConfig {
    pub transport: McpTransport,
    pub port: u16,
    pub timeout: u64,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[serde(rename_all = "lowercase")]
pub enum McpTransport {
    Stdio,
    Http,
    Websocket,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct SparcConfig {
    pub default_mode: SparcMode,
    pub test_coverage: u8,
    pub sequential: bool,
    pub auto_commit: CommitFrequency,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[serde(rename_all = "kebab-case")]
pub enum SparcMode {
    Full,
    BackendOnly,
    FrontendOnly,
    ApiOnly,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[serde(rename_all = "lowercase")]
pub enum CommitFrequency {
    Phase,
    Feature,
    Manual,
}

// Configuration manager with hot reloading
pub struct ConfigManager {
    config: Arc<RwLock<AppConfig>>,
    watchers: Vec<notify::RecommendedWatcher>,
    config_sources: Vec<ConfigSource>,
}

struct ConfigSource {
    name: String,
    path: PathBuf,
    priority: u8,
}

impl ConfigManager {
    pub fn new() -> Result<Self, ConfigError> {
        let mut builder = Config::builder();
        let mut config_sources = Vec::new();

        // Layer 1: Built-in defaults
        builder = builder.add_source(Config::try_from(&Self::default_config())?);

        // Layer 2: System configuration
        if let Some(path) = Self::system_config_path() {
            if path.exists() {
                builder = builder.add_source(File::from(path.clone()));
                config_sources.push(ConfigSource {
                    name: "system".to_string(),
                    path,
                    priority: 2,
                });
            }
        }

        // Layer 3: User configuration
        if let Some(path) = Self::user_config_path() {
            if path.exists() {
                builder = builder.add_source(File::from(path.clone()));
                config_sources.push(ConfigSource {
                    name: "user".to_string(),
                    path,
                    priority: 3,
                });
            }
        }

        // Layer 4: Project configuration
        if let Some(path) = Self::project_config_path() {
            if path.exists() {
                builder = builder.add_source(File::from(path.clone()));
                config_sources.push(ConfigSource {
                    name: "project".to_string(),
                    path,
                    priority: 4,
                });
            }
        }

        // Layer 5: Environment variables
        builder = builder.add_source(
            Environment::with_prefix("CLAUDE_FLOW")
                .separator("__")
                .try_parsing(true)
        );

        // Build configuration
        let config: AppConfig = builder.build()?.try_deserialize()?;

        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            watchers: Vec::new(),
            config_sources,
        })
    }

    fn default_config() -> AppConfig {
        AppConfig {
            version: env!("CARGO_PKG_VERSION").to_string(),
            log_level: LogLevel::Info,
            max_concurrent_processes: 5,
            auto_save: true,
            session_timeout: 3600000, // 1 hour
            memory_manager: MemoryConfig {
                backend: MemoryBackend::Hybrid,
                max_entries: 1000,
                persist_interval: 300000, // 5 minutes
            },
            mcp: McpConfig {
                transport: McpTransport::Stdio,
                port: 3000,
                timeout: 30000,
            },
            sparc: SparcConfig {
                default_mode: SparcMode::Full,
                test_coverage: 80,
                sequential: true,
                auto_commit: CommitFrequency::Phase,
            },
        }
    }

    fn system_config_path() -> Option<PathBuf> {
        // Try multiple system locations
        let paths = vec![
            PathBuf::from("/etc/claude-flow/config.toml"),
            PathBuf::from("/usr/local/etc/claude-flow/config.toml"),
        ];

        paths.into_iter().find(|p| p.exists())
    }

    fn user_config_path() -> Option<PathBuf> {
        UserDirs::new()
            .map(|dirs| dirs.home_dir().join(".claude-flow/config.toml"))
    }

    fn project_config_path() -> Option<PathBuf> {
        let paths = vec![
            PathBuf::from(".claude-flow.toml"),
            PathBuf::from(".claude-flow/config.toml"),
            PathBuf::from("claude-flow.config.toml"),
        ];

        paths.into_iter().find(|p| p.exists())
    }

    pub fn get(&self) -> Arc<RwLock<AppConfig>> {
        self.config.clone()
    }

    pub fn reload(&mut self) -> Result<(), ConfigError> {
        let new_config = Self::new()?;
        *self.config.write().unwrap() = new_config.config.read().unwrap().clone();
        Ok(())
    }
}
```

## Hierarchical Configuration System

### Configuration Layer Priority

The system implements a multi-layer configuration hierarchy with clear precedence:

```typescript
interface ConfigurationLayer {
  name: string;
  priority: number;
  source: 'default' | 'file' | 'environment' | 'cli' | 'runtime';
  data: Record<string, any>;
  readonly?: boolean;
}

class ConfigurationManager {
  private layers: ConfigurationLayer[] = [];
  private watchers: Map<string, FileWatcher> = new Map();
  
  constructor() {
    this.initializeDefaultLayers();
  }
  
  private initializeDefaultLayers(): void {
    // Layer 1: Built-in defaults (lowest priority)
    this.addLayer({
      name: 'defaults',
      priority: 1,
      source: 'default',
      data: {
        version: '1.0.0',
        logLevel: 'info',
        maxConcurrentProcesses: 5,
        autoSave: true,
        sessionTimeout: 3600000, // 1 hour
        memoryManager: {
          backend: 'hybrid',
          maxEntries: 1000,
          persistInterval: 300000 // 5 minutes
        },
        mcp: {
          transport: 'stdio',
          port: 3000,
          timeout: 30000
        },
        sparc: {
          defaultMode: 'full',
          testCoverage: 80,
          sequential: true,
          autoCommit: 'phase'
        }
      },
      readonly: true
    });
    
    // Layer 2: System configuration file
    this.loadSystemConfiguration();
    
    // Layer 3: User configuration file
    this.loadUserConfiguration();
    
    // Layer 4: Project configuration file
    this.loadProjectConfiguration();
    
    // Layer 5: Environment variables (highest priority)
    this.loadEnvironmentConfiguration();
  }
}
```

### Configuration File Loading

Loading configuration from multiple file sources with validation:

```typescript
async function loadSystemConfiguration(): Promise<void> {
  const systemConfigPaths = [
    '/etc/claude-flow/config.json',
    '/usr/local/etc/claude-flow/config.json',
    path.join(os.homedir(), '.config', 'claude-flow', 'system.json')
  ];
  
  for (const configPath of systemConfigPaths) {
    try {
      if (await fs.access(configPath).then(() => true).catch(() => false)) {
        const configData = await this.loadConfigurationFile(configPath);
        this.addLayer({
          name: 'system',
          priority: 2,
          source: 'file',
          data: configData,
          readonly: false
        });
        
        // Watch for changes
        this.watchConfigurationFile(configPath, 'system');
        break;
      }
    } catch (error) {
      console.warn(colors.yellow(`Warning: Failed to load system config from ${configPath}: ${error}`));
    }
  }
}

async function loadUserConfiguration(): Promise<void> {
  const userConfigPath = path.join(os.homedir(), '.claude-flow', 'config.json');
  
  try {
    if (await fs.access(userConfigPath).then(() => true).catch(() => false)) {
      const configData = await this.loadConfigurationFile(userConfigPath);
      this.addLayer({
        name: 'user',
        priority: 3,
        source: 'file',
        data: configData
      });
      
      this.watchConfigurationFile(userConfigPath, 'user');
    }
  } catch (error) {
    console.warn(colors.yellow(`Warning: Failed to load user config: ${error}`));
  }
}

async function loadProjectConfiguration(): Promise<void> {
  const projectConfigPaths = [
    '.claude-flow.json',
    '.claude-flow/config.json',
    'claude-flow.config.json'
  ];
  
  for (const configPath of projectConfigPaths) {
    try {
      if (await fs.access(configPath).then(() => true).catch(() => false)) {
        const configData = await this.loadConfigurationFile(configPath);
        this.addLayer({
          name: 'project',
          priority: 4,
          source: 'file',
          data: configData
        });
        
        this.watchConfigurationFile(configPath, 'project');
        break;
      }
    } catch (error) {
      console.warn(colors.yellow(`Warning: Failed to load project config from ${configPath}: ${error}`));
    }
  }
}

private async loadConfigurationFile(filePath: string): Promise<Record<string, any>> {
  const content = await fs.readFile(filePath, 'utf8');
  const config = JSON.parse(content);
  
  // Validate configuration schema
  const validationResult = await this.validateConfiguration(config);
  if (!validationResult.valid) {
    throw new Error(`Configuration validation failed: ${validationResult.errors.join(', ')}`);
  }
  
  return config;
}
```

## Environment Variable Integration

### Environment Variable Mapping

Automatic mapping of environment variables to configuration:

```typescript
function loadEnvironmentConfiguration(): void {
  const envConfig: Record<string, any> = {};
  
  // Define environment variable mappings
  const envMappings = {
    'CLAUDE_FLOW_LOG_LEVEL': 'logLevel',
    'CLAUDE_FLOW_MAX_PROCESSES': 'maxConcurrentProcesses',
    'CLAUDE_FLOW_AUTO_SAVE': 'autoSave',
    'CLAUDE_FLOW_SESSION_TIMEOUT': 'sessionTimeout',
    'CLAUDE_FLOW_MCP_PORT': 'mcp.port',
    'CLAUDE_FLOW_MCP_TRANSPORT': 'mcp.transport',
    'CLAUDE_FLOW_MCP_TIMEOUT': 'mcp.timeout',
    'CLAUDE_FLOW_MEMORY_BACKEND': 'memoryManager.backend',
    'CLAUDE_FLOW_MEMORY_MAX_ENTRIES': 'memoryManager.maxEntries',
    'CLAUDE_FLOW_SPARC_MODE': 'sparc.defaultMode',
    'CLAUDE_FLOW_TEST_COVERAGE': 'sparc.testCoverage',
    'CLAUDE_FLOW_AUTO_COMMIT': 'sparc.autoCommit'
  };
  
  // Process environment variables
  for (const [envVar, configPath] of Object.entries(envMappings)) {
    const value = process.env[envVar];
    if (value !== undefined) {
      const parsedValue = this.parseEnvironmentValue(value, configPath);
      this.setNestedValue(envConfig, configPath, parsedValue);
    }
  }
  
  // Add special handling for prefixed environment variables
  for (const [key, value] of Object.entries(process.env)) {
    if (key.startsWith('CLAUDE_FLOW_')) {
      const configKey = this.envKeyToConfigKey(key);
      if (!envMappings[key]) {
        const parsedValue = this.parseEnvironmentValue(value, configKey);
        this.setNestedValue(envConfig, configKey, parsedValue);
      }
    }
  }
  
  if (Object.keys(envConfig).length > 0) {
    this.addLayer({
      name: 'environment',
      priority: 5,
      source: 'environment',
      data: envConfig,
      readonly: true
    });
  }
}

private parseEnvironmentValue(value: string, configPath: string): any {
  // Type inference based on config path and value format
  if (value.toLowerCase() === 'true' || value.toLowerCase() === 'false') {
    return value.toLowerCase() === 'true';
  }
  
  if (/^\d+$/.test(value)) {
    return parseInt(value, 10);
  }
  
  if (/^\d+\.\d+$/.test(value)) {
    return parseFloat(value);
  }
  
  // JSON parsing for complex values
  if ((value.startsWith('{') && value.endsWith('}')) || 
      (value.startsWith('[') && value.endsWith(']'))) {
    try {
      return JSON.parse(value);
    } catch {
      return value; // Return as string if JSON parsing fails
    }
  }
  
  return value;
}

private envKeyToConfigKey(envKey: string): string {
  return envKey
    .replace(/^CLAUDE_FLOW_/, '')
    .toLowerCase()
    .replace(/_/g, '.');
}

private setNestedValue(obj: Record<string, any>, path: string, value: any): void {
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
}
```

## Rust Dynamic Configuration with Hot Reloading

### File Watching and Live Updates

```rust
use notify::{DebouncedEvent, RecommendedWatcher, RecursiveMode, Watcher};
use std::sync::mpsc::{channel, Receiver};
use std::time::Duration;
use tokio::sync::broadcast;

impl ConfigManager {
    pub fn enable_hot_reload(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let (tx, rx) = channel();
        let config = self.config.clone();
        
        // Create a watcher for each config source
        for source in &self.config_sources {
            let mut watcher: RecommendedWatcher = Watcher::new(tx.clone(), Duration::from_secs(1))?;
            watcher.watch(&source.path, RecursiveMode::NonRecursive)?;
            self.watchers.push(watcher);
        }

        // Spawn a thread to handle file change events
        std::thread::spawn(move || {
            Self::handle_config_changes(rx, config);
        });

        Ok(())
    }

    fn handle_config_changes(rx: Receiver<DebouncedEvent>, config: Arc<RwLock<AppConfig>>) {
        for event in rx {
            match event {
                DebouncedEvent::Write(path) | DebouncedEvent::Create(path) => {
                    println!("Configuration file changed: {:?}", path);
                    if let Err(e) = Self::reload_from_path(&path, &config) {
                        eprintln!("Failed to reload configuration: {}", e);
                    }
                }
                _ => {}
            }
        }
    }

    fn reload_from_path(path: &Path, config: &Arc<RwLock<AppConfig>>) -> Result<(), ConfigError> {
        // Reload just the changed file and merge with existing config
        let file_config = Config::builder()
            .add_source(File::from(path))
            .build()?;
        
        // Merge with current config
        let mut current = config.write().unwrap();
        // Apply partial updates from file
        if let Ok(partial) = file_config.try_deserialize::<HashMap<String, Value>>() {
            Self::merge_config(&mut *current, partial);
        }
        
        println!("✓ Configuration reloaded from {:?}", path);
        Ok(())
    }

    fn merge_config(current: &mut AppConfig, updates: HashMap<String, Value>) {
        // Manual merge logic for known fields
        for (key, value) in updates {
            match key.as_str() {
                "log_level" => {
                    if let Ok(level) = value.clone().into_string() {
                        current.log_level = serde_json::from_str(&format!("\"{}\"", level))
                            .unwrap_or(current.log_level.clone());
                    }
                }
                "max_concurrent_processes" => {
                    if let Ok(num) = value.clone().into_int() {
                        current.max_concurrent_processes = num as u32;
                    }
                }
                "auto_save" => {
                    if let Ok(b) = value.clone().into_bool() {
                        current.auto_save = b;
                    }
                }
                // Add more fields as needed
                _ => {}
            }
        }
    }
}

// Configuration change notifications
pub struct ConfigNotifier {
    sender: broadcast::Sender<ConfigChangeEvent>,
}

#[derive(Clone, Debug)]
pub struct ConfigChangeEvent {
    pub path: String,
    pub old_value: serde_json::Value,
    pub new_value: serde_json::Value,
}

impl ConfigNotifier {
    pub fn new() -> Self {
        let (sender, _) = broadcast::channel(100);
        Self { sender }
    }

    pub fn subscribe(&self) -> broadcast::Receiver<ConfigChangeEvent> {
        self.sender.subscribe()
    }

    pub fn notify(&self, event: ConfigChangeEvent) {
        let _ = self.sender.send(event);
    }
}
```

### Runtime Configuration API

```rust
use std::sync::Mutex;
use once_cell::sync::Lazy;

// Global configuration instance
static CONFIG: Lazy<Mutex<ConfigManager>> = Lazy::new(|| {
    Mutex::new(ConfigManager::new().expect("Failed to initialize configuration"))
});

// Configuration API
pub struct ConfigApi;

impl ConfigApi {
    pub fn get<T: serde::de::DeserializeOwned>(path: &str) -> Result<T, ConfigError> {
        let config = CONFIG.lock().unwrap();
        let value = config.get_value(path)?;
        value.try_deserialize()
    }

    pub fn set<T: serde::Serialize>(path: &str, value: T) -> Result<(), ConfigError> {
        let mut config = CONFIG.lock().unwrap();
        config.set_value(path, value)
    }

    pub fn update_runtime<T: serde::Serialize>(
        path: &str, 
        value: T,
        persist: bool
    ) -> Result<(), ConfigError> {
        let mut config = CONFIG.lock().unwrap();
        config.update_value(path, value)?;
        
        if persist {
            config.persist_to_user_config()?;
        }
        
        Ok(())
    }

    pub fn watch<F>(path: &str, callback: F) -> WatchHandle
    where
        F: Fn(&serde_json::Value, &serde_json::Value) + Send + 'static
    {
        let mut config = CONFIG.lock().unwrap();
        config.add_watcher(path, Box::new(callback))
    }
}

pub struct WatchHandle {
    id: uuid::Uuid,
}

impl Drop for WatchHandle {
    fn drop(&mut self) {
        let mut config = CONFIG.lock().unwrap();
        config.remove_watcher(self.id);
    }
}
```

## Dynamic Configuration Management

### Runtime Configuration Updates

Supporting live configuration updates during application execution:

```typescript
class DynamicConfigurationManager extends ConfigurationManager {
  private changeListeners: Map<string, Array<(newValue: any, oldValue: any) => void>> = new Map();
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  
  // Watch for configuration file changes
  private watchConfigurationFile(filePath: string, layerName: string): void {
    const watcher = fs.watch(filePath, { persistent: false }, (eventType, filename) => {
      if (eventType === 'change') {
        this.debouncedReload(layerName, filePath);
      }
    });
    
    this.watchers.set(layerName, watcher);
  }
  
  private debouncedReload(layerName: string, filePath: string): void {
    // Clear existing timer
    const existingTimer = this.debounceTimers.get(layerName);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }
    
    // Set new timer
    const timer = setTimeout(async () => {
      try {
        console.log(colors.blue(`Reloading configuration from ${filePath}...`));
        
        const oldConfig = this.getLayerData(layerName);
        const newConfigData = await this.loadConfigurationFile(filePath);
        
        this.updateLayer(layerName, newConfigData);
        
        // Notify listeners of changes
        this.notifyConfigurationChanges(oldConfig, newConfigData);
        
        console.log(colors.green(`✓ Configuration reloaded from ${filePath}`));
        
      } catch (error) {
        console.error(colors.red(`Failed to reload configuration from ${filePath}:`), error);
      }
    }, 1000); // 1 second debounce
    
    this.debounceTimers.set(layerName, timer);
  }
  
  // Configuration change listeners
  public onChange(configPath: string, listener: (newValue: any, oldValue: any) => void): void {
    if (!this.changeListeners.has(configPath)) {
      this.changeListeners.set(configPath, []);
    }
    this.changeListeners.get(configPath)!.push(listener);
  }
  
  private notifyConfigurationChanges(oldConfig: any, newConfig: any): void {
    for (const [configPath, listeners] of this.changeListeners.entries()) {
      const oldValue = this.getNestedValue(oldConfig, configPath);
      const newValue = this.getNestedValue(newConfig, configPath);
      
      if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        for (const listener of listeners) {
          try {
            listener(newValue, oldValue);
          } catch (error) {
            console.error(colors.red(`Configuration change listener error:`), error);
          }
        }
      }
    }
  }
  
  // Runtime configuration updates
  public async updateConfiguration(configPath: string, value: any, persistent = false): Promise<void> {
    const oldValue = this.get(configPath);
    
    // Update runtime layer
    this.updateRuntimeValue(configPath, value);
    
    // Optionally persist to user configuration
    if (persistent) {
      await this.persistConfigurationChange(configPath, value);
    }
    
    // Notify listeners
    const listeners = this.changeListeners.get(configPath) || [];
    for (const listener of listeners) {
      try {
        listener(value, oldValue);
      } catch (error) {
        console.error(colors.red(`Configuration change listener error:`), error);
      }
    }
    
    console.log(colors.green(`✓ Configuration updated: ${configPath} = ${JSON.stringify(value)}`));
  }
  
  private updateRuntimeValue(configPath: string, value: any): void {
    // Get or create runtime layer
    let runtimeLayer = this.layers.find(layer => layer.name === 'runtime');
    if (!runtimeLayer) {
      runtimeLayer = {
        name: 'runtime',
        priority: 10, // Highest priority
        source: 'runtime',
        data: {}
      };
      this.addLayer(runtimeLayer);
    }
    
    this.setNestedValue(runtimeLayer.data, configPath, value);
  }
  
  private async persistConfigurationChange(configPath: string, value: any): Promise<void> {
    const userConfigPath = path.join(os.homedir(), '.claude-flow', 'config.json');
    
    try {
      // Load existing user configuration
      let userConfig = {};
      try {
        const content = await fs.readFile(userConfigPath, 'utf8');
        userConfig = JSON.parse(content);
      } catch {
        // File doesn't exist or is invalid, start with empty config
      }
      
      // Update the configuration
      this.setNestedValue(userConfig, configPath, value);
      
      // Ensure directory exists
      await fs.mkdir(path.dirname(userConfigPath), { recursive: true });
      
      // Write updated configuration
      await fs.writeFile(userConfigPath, JSON.stringify(userConfig, null, 2), 'utf8');
      
      console.log(colors.gray(`Configuration persisted to ${userConfigPath}`));
      
    } catch (error) {
      console.error(colors.red(`Failed to persist configuration:`), error);
    }
  }
}
```

## Configuration Validation and Schema

### Schema-Based Validation

Implementing comprehensive configuration validation:

```typescript
interface ConfigurationSchema {
  type: 'object' | 'string' | 'number' | 'boolean' | 'array';
  properties?: Record<string, ConfigurationSchema>;
  items?: ConfigurationSchema;
  required?: string[];
  minimum?: number;
  maximum?: number;
  pattern?: string;
  enum?: any[];
  default?: any;
  description?: string;
}

const configurationSchema: ConfigurationSchema = {
  type: 'object',
  properties: {
    version: {
      type: 'string',
      pattern: '^\\d+\\.\\d+\\.\\d+$',
      description: 'Application version'
    },
    logLevel: {
      type: 'string',
      enum: ['debug', 'info', 'warn', 'error'],
      default: 'info',
      description: 'Logging level'
    },
    maxConcurrentProcesses: {
      type: 'number',
      minimum: 1,
      maximum: 50,
      default: 5,
      description: 'Maximum number of concurrent processes'
    },
    autoSave: {
      type: 'boolean',
      default: true,
      description: 'Enable automatic session saving'
    },
    sessionTimeout: {
      type: 'number',
      minimum: 60000, // 1 minute
      maximum: 86400000, // 24 hours
      default: 3600000,
      description: 'Session timeout in milliseconds'
    },
    memoryManager: {
      type: 'object',
      properties: {
        backend: {
          type: 'string',
          enum: ['memory', 'file', 'hybrid'],
          default: 'hybrid',
          description: 'Memory storage backend'
        },
        maxEntries: {
          type: 'number',
          minimum: 100,
          maximum: 10000,
          default: 1000,
          description: 'Maximum memory entries'
        },
        persistInterval: {
          type: 'number',
          minimum: 30000,
          maximum: 3600000,
          default: 300000,
          description: 'Memory persistence interval in milliseconds'
        }
      },
      required: ['backend'],
      description: 'Memory manager configuration'
    },
    mcp: {
      type: 'object',
      properties: {
        transport: {
          type: 'string',
          enum: ['stdio', 'http', 'websocket'],
          default: 'stdio',
          description: 'MCP transport type'
        },
        port: {
          type: 'number',
          minimum: 1024,
          maximum: 65535,
          default: 3000,
          description: 'MCP server port'
        },
        timeout: {
          type: 'number',
          minimum: 5000,
          maximum: 300000,
          default: 30000,
          description: 'MCP operation timeout in milliseconds'
        }
      },
      description: 'MCP server configuration'
    },
    sparc: {
      type: 'object',
      properties: {
        defaultMode: {
          type: 'string',
          enum: ['full', 'backend-only', 'frontend-only', 'api-only'],
          default: 'full',
          description: 'Default SPARC development mode'
        },
        testCoverage: {
          type: 'number',
          minimum: 0,
          maximum: 100,
          default: 80,
          description: 'Target test coverage percentage'
        },
        sequential: {
          type: 'boolean',
          default: true,
          description: 'Execute SPARC phases sequentially'
        },
        autoCommit: {
          type: 'string',
          enum: ['phase', 'feature', 'manual'],
          default: 'phase',
          description: 'Automatic commit frequency'
        }
      },
      description: 'SPARC methodology configuration'
    }
  },
  required: ['version'],
  description: 'Claude-Flow configuration schema'
};

class ConfigurationValidator {
  private schema: ConfigurationSchema;
  
  constructor(schema: ConfigurationSchema) {
    this.schema = schema;
  }
  
  public validate(config: any): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    this.validateRecursive(config, this.schema, '', errors, warnings);
    
    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
  
  private validateRecursive(
    value: any, 
    schema: ConfigurationSchema, 
    path: string, 
    errors: string[], 
    warnings: string[]
  ): void {
    
    // Type validation
    if (!this.validateType(value, schema.type)) {
      errors.push(`${path}: Expected ${schema.type}, got ${typeof value}`);
      return;
    }
    
    // Enum validation
    if (schema.enum && !schema.enum.includes(value)) {
      errors.push(`${path}: Value must be one of: ${schema.enum.join(', ')}`);
    }
    
    // Number validations
    if (schema.type === 'number') {
      if (schema.minimum !== undefined && value < schema.minimum) {
        errors.push(`${path}: Value ${value} is below minimum ${schema.minimum}`);
      }
      if (schema.maximum !== undefined && value > schema.maximum) {
        errors.push(`${path}: Value ${value} is above maximum ${schema.maximum}`);
      }
    }
    
    // String validations
    if (schema.type === 'string' && schema.pattern) {
      const regex = new RegExp(schema.pattern);
      if (!regex.test(value)) {
        errors.push(`${path}: Value does not match pattern ${schema.pattern}`);
      }
    }
    
    // Object validations
    if (schema.type === 'object' && schema.properties) {
      // Check required properties
      if (schema.required) {
        for (const requiredProp of schema.required) {
          if (!(requiredProp in value)) {
            errors.push(`${path}: Missing required property '${requiredProp}'`);
          }
        }
      }
      
      // Validate properties
      for (const [propName, propSchema] of Object.entries(schema.properties)) {
        if (propName in value) {
          const propPath = path ? `${path}.${propName}` : propName;
          this.validateRecursive(value[propName], propSchema, propPath, errors, warnings);
        }
      }
      
      // Check for unknown properties
      for (const propName of Object.keys(value)) {
        if (!(propName in schema.properties)) {
          warnings.push(`${path}: Unknown property '${propName}'`);
        }
      }
    }
    
    // Array validations
    if (schema.type === 'array' && schema.items) {
      if (Array.isArray(value)) {
        value.forEach((item, index) => {
          const itemPath = `${path}[${index}]`;
          this.validateRecursive(item, schema.items!, itemPath, errors, warnings);
        });
      }
    }
  }
  
  private validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'array':
        return Array.isArray(value);
      default:
        return false;
    }
  }
}
```

## Rust Configuration CLI Commands

### Configuration Subcommands with clap

```rust
use clap::{Parser, Subcommand};
use dialoguer::{Select, Input, Confirm, theme::ColorfulTheme};
use serde_json;
use serde_yaml;
use comfy_table::{Table, Cell, Attribute};

#[derive(Subcommand)]
pub enum ConfigCommands {
    /// Show current configuration
    Show {
        /// Output format
        #[clap(long, value_enum, default_value = "table")]
        format: OutputFormat,
        
        /// Show specific configuration layer
        #[clap(long)]
        layer: Option<String>,
    },
    
    /// Get a configuration value
    Get {
        /// Configuration key (dot notation)
        key: String,
        
        /// Default value if key not found
        #[clap(long)]
        default: Option<String>,
    },
    
    /// Set a configuration value
    Set {
        /// Configuration key
        key: String,
        
        /// Value to set
        value: String,
        
        /// Persist to user configuration file
        #[clap(long)]
        persist: bool,
        
        /// Value type
        #[clap(long, value_enum, default_value = "auto")]
        r#type: ValueType,
    },
    
    /// Edit configuration interactively
    Edit {
        /// Edit specific section
        #[clap(long)]
        section: Option<String>,
    },
    
    /// Validate current configuration
    Validate {
        /// Attempt to fix validation errors
        #[clap(long)]
        fix: bool,
    },
    
    /// Reset configuration to defaults
    Reset {
        /// Reset specific section only
        #[clap(long)]
        section: Option<String>,
        
        /// Skip confirmation prompt
        #[clap(long)]
        confirm: bool,
    },
}

#[derive(clap::ValueEnum, Clone)]
pub enum OutputFormat {
    Json,
    Yaml,
    Table,
}

#[derive(clap::ValueEnum, Clone)]
pub enum ValueType {
    Auto,
    String,
    Number,
    Boolean,
    Json,
}

pub async fn handle_config_command(cmd: ConfigCommands) -> anyhow::Result<()> {
    match cmd {
        ConfigCommands::Show { format, layer } => {
            show_configuration(format, layer).await
        }
        ConfigCommands::Get { key, default } => {
            get_configuration_value(&key, default).await
        }
        ConfigCommands::Set { key, value, persist, r#type } => {
            set_configuration_value(&key, &value, persist, r#type).await
        }
        ConfigCommands::Edit { section } => {
            edit_configuration_interactively(section).await
        }
        ConfigCommands::Validate { fix } => {
            validate_configuration(fix).await
        }
        ConfigCommands::Reset { section, confirm } => {
            reset_configuration(section, confirm).await
        }
    }
}

async fn show_configuration(format: OutputFormat, layer: Option<String>) -> anyhow::Result<()> {
    let config_manager = CONFIG.lock().unwrap();
    
    if let Some(layer_name) = layer {
        // Show specific layer
        let layer_config = config_manager.get_layer(&layer_name)
            .ok_or_else(|| anyhow::anyhow!("Configuration layer '{}' not found", layer_name))?;
        
        println!("Configuration Layer: {}", layer_name);
        println!("{}", "─".repeat(50));
        
        match format {
            OutputFormat::Json => {
                println!("{}", serde_json::to_string_pretty(&layer_config)?);
            }
            OutputFormat::Yaml => {
                println!("{}", serde_yaml::to_string(&layer_config)?);
            }
            OutputFormat::Table => {
                print_config_table(&layer_config, 0);
            }
        }
    } else {
        // Show merged configuration
        let config = config_manager.get_merged_config();
        
        match format {
            OutputFormat::Json => {
                println!("{}", serde_json::to_string_pretty(&config)?);
            }
            OutputFormat::Yaml => {
                println!("{}", serde_yaml::to_string(&config)?);
            }
            OutputFormat::Table => {
                print_config_table(&config, 0);
            }
        }
    }
    
    Ok(())
}

fn print_config_table(config: &serde_json::Value, indent: usize) {
    if let serde_json::Value::Object(map) = config {
        for (key, value) in map {
            let indent_str = "  ".repeat(indent);
            
            match value {
                serde_json::Value::Object(_) => {
                    println!("{}{}:", indent_str, colored::Colorize::cyan(key));
                    print_config_table(value, indent + 1);
                }
                serde_json::Value::Array(arr) => {
                    let value_str = format!("[{}]", arr.iter()
                        .map(|v| v.to_string())
                        .collect::<Vec<_>>()
                        .join(", "));
                    println!("{}{}: {}", indent_str, key, colored::Colorize::gray(&value_str));
                }
                _ => {
                    println!("{}{}: {}", indent_str, key, colored::Colorize::gray(&value.to_string()));
                }
            }
        }
    }
}

async fn edit_configuration_interactively(section: Option<String>) -> anyhow::Result<()> {
    let theme = ColorfulTheme::default();
    let mut config_manager = CONFIG.lock().unwrap();
    
    if let Some(section_name) = section {
        // Edit specific section
        let section_config = config_manager.get_section(&section_name)
            .ok_or_else(|| anyhow::anyhow!("Configuration section '{}' not found", section_name))?;
        
        println!("Editing Configuration Section: {}", section_name);
        println!("{}", "─".repeat(50));
        
        let updated = edit_object_interactively(section_config, &theme)?;
        
        if let Some(new_config) = updated {
            config_manager.update_section(&section_name, new_config)?;
            println!("✓ Configuration section '{}' updated", section_name);
        }
    } else {
        // Full configuration edit
        let sections = config_manager.get_sections();
        
        let selection = Select::with_theme(&theme)
            .with_prompt("Select configuration section to edit")
            .items(&sections)
            .default(0)
            .interact()?;
        
        let section_name = &sections[selection];
        edit_configuration_interactively(Some(section_name.to_string())).await?;
    }
    
    Ok(())
}

fn edit_object_interactively(
    obj: serde_json::Value,
    theme: &ColorfulTheme
) -> anyhow::Result<Option<serde_json::Value>> {
    if let serde_json::Value::Object(mut map) = obj {
        let mut changed = false;
        
        for (key, value) in map.clone() {
            println!("\n{}: {}", key, serde_json::to_string(&value)?);
            
            let choices = vec!["Keep current value", "Edit value", "Delete key"];
            let action = Select::with_theme(theme)
                .with_prompt("What would you like to do?")
                .items(&choices)
                .default(0)
                .interact()?;
            
            match action {
                1 => { // Edit value
                    let new_value = prompt_for_value(&key, &value, theme)?;
                    if new_value != value {
                        map.insert(key, new_value);
                        changed = true;
                    }
                }
                2 => { // Delete key
                    map.remove(&key);
                    changed = true;
                }
                _ => {} // Keep current value
            }
        }
        
        // Option to add new keys
        if Confirm::with_theme(theme)
            .with_prompt("Add new configuration keys?")
            .default(false)
            .interact()?
        {
            loop {
                let new_key = Input::<String>::with_theme(theme)
                    .with_prompt("Enter new key name (empty to finish)")
                    .allow_empty(true)
                    .interact()?;
                
                if new_key.is_empty() {
                    break;
                }
                
                let new_value = prompt_for_value(&new_key, &serde_json::Value::Null, theme)?;
                map.insert(new_key, new_value);
                changed = true;
            }
        }
        
        Ok(if changed { Some(serde_json::Value::Object(map)) } else { None })
    } else {
        Ok(None)
    }
}

fn prompt_for_value(
    key: &str,
    current: &serde_json::Value,
    theme: &ColorfulTheme
) -> anyhow::Result<serde_json::Value> {
    let value_types = vec!["String", "Number", "Boolean", "JSON Object/Array"];
    let value_type = Select::with_theme(theme)
        .with_prompt(&format!("Select type for '{}'", key))
        .items(&value_types)
        .default(0)
        .interact()?;
    
    match value_type {
        0 => { // String
            let input = Input::<String>::with_theme(theme)
                .with_prompt(&format!("Enter string value for '{}'", key))
                .with_initial_text(current.as_str().unwrap_or(""))
                .interact()?;
            Ok(serde_json::Value::String(input))
        }
        1 => { // Number
            let input = Input::<String>::with_theme(theme)
                .with_prompt(&format!("Enter number value for '{}'", key))
                .with_initial_text(&current.as_f64().unwrap_or(0.0).to_string())
                .validate_with(|input: &String| -> Result<(), &str> {
                    input.parse::<f64>().map(|_| ()).map_err(|_| "Please enter a valid number")
                })
                .interact()?;
            Ok(serde_json::Value::Number(
                serde_json::Number::from_f64(input.parse().unwrap()).unwrap()
            ))
        }
        2 => { // Boolean
            let value = Confirm::with_theme(theme)
                .with_prompt(&format!("Set '{}' to true?", key))
                .default(current.as_bool().unwrap_or(false))
                .interact()?;
            Ok(serde_json::Value::Bool(value))
        }
        3 => { // JSON
            let input = Input::<String>::with_theme(theme)
                .with_prompt(&format!("Enter JSON value for '{}'", key))
                .with_initial_text(&serde_json::to_string_pretty(current).unwrap_or_default())
                .validate_with(|input: &String| -> Result<(), &str> {
                    serde_json::from_str::<serde_json::Value>(input)
                        .map(|_| ())
                        .map_err(|_| "Please enter valid JSON")
                })
                .interact()?;
            Ok(serde_json::from_str(&input)?)
        }
        _ => Ok(current.clone()),
    }
}
```

## Configuration CLI Commands

### Interactive Configuration Management

CLI commands for managing configuration through interactive prompts:

```typescript
export const configCommand = new Command()
  .description('Manage Claude-Flow configuration')
  .action(() => {
    configCommand.showHelp();
  })
  .command('show', Command)
    .description('Show current configuration')
    .option('--format <format:string>', 'Output format (json, yaml, table)', { default: 'table' })
    .option('--layer <layer:string>', 'Show specific configuration layer')
    .action(async (options: any) => {
      await showConfiguration(options);
    })
  .command('get', Command)
    .description('Get a configuration value')
    .arguments('<key:string>')
    .option('--default <value:string>', 'Default value if key not found')
    .action(async (options: any, key: string) => {
      await getConfigurationValue(key, options);
    })
  .command('set', Command)
    .description('Set a configuration value')
    .arguments('<key:string> <value:string>')
    .option('--persist', 'Persist to user configuration file')
    .option('--type <type:string>', 'Value type (string, number, boolean, json)', { default: 'auto' })
    .action(async (options: any, key: string, value: string) => {
      await setConfigurationValue(key, value, options);
    })
  .command('edit', Command)
    .description('Edit configuration interactively')
    .option('--section <section:string>', 'Edit specific configuration section')
    .action(async (options: any) => {
      await editConfigurationInteractively(options);
    })
  .command('validate', Command)
    .description('Validate current configuration')
    .option('--fix', 'Attempt to fix validation errors')
    .action(async (options: any) => {
      await validateConfiguration(options);
    })
  .command('reset', Command)
    .description('Reset configuration to defaults')
    .option('--section <section:string>', 'Reset specific section only')
    .option('--confirm', 'Skip confirmation prompt')
    .action(async (options: any) => {
      await resetConfiguration(options);
    });

async function showConfiguration(options: any): Promise<void> {
  const configManager = getConfigurationManager();
  
  if (options.layer) {
    const layerData = configManager.getLayerData(options.layer);
    if (!layerData) {
      console.error(colors.red(`Configuration layer '${options.layer}' not found`));
      return;
    }
    
    console.log(colors.cyan.bold(`Configuration Layer: ${options.layer}`));
    console.log('─'.repeat(50));
    console.log(JSON.stringify(layerData, null, 2));
    return;
  }
  
  const config = configManager.getAll();
  
  switch (options.format) {
    case 'json':
      console.log(JSON.stringify(config, null, 2));
      break;
      
    case 'yaml':
      // Would use a YAML library in production
      console.log(colors.yellow('YAML output not implemented, showing JSON:'));
      console.log(JSON.stringify(config, null, 2));
      break;
      
    case 'table':
    default:
      displayConfigurationTable(config);
      break;
  }
}

function displayConfigurationTable(config: any, prefix = '', indent = 0): void {
  const indentStr = '  '.repeat(indent);
  
  for (const [key, value] of Object.entries(config)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      console.log(`${indentStr}${colors.cyan.bold(key)}:`);
      displayConfigurationTable(value, fullKey, indent + 1);
    } else {
      const displayValue = Array.isArray(value) 
        ? `[${value.join(', ')}]`
        : JSON.stringify(value);
      console.log(`${indentStr}${colors.white(key)}: ${colors.gray(displayValue)}`);
    }
  }
}

async function editConfigurationInteractively(options: any): Promise<void> {
  const configManager = getConfigurationManager();
  
  if (options.section) {
    await editConfigurationSection(configManager, options.section);
  } else {
    await editFullConfiguration(configManager);
  }
}

async function editConfigurationSection(configManager: any, section: string): Promise<void> {
  const sectionConfig = configManager.get(section);
  
  if (!sectionConfig) {
    console.error(colors.red(`Configuration section '${section}' not found`));
    return;
  }
  
  console.log(colors.cyan.bold(`Editing Configuration Section: ${section}`));
  console.log('─'.repeat(50));
  
  const updatedConfig = await editObjectInteractively(sectionConfig, section);
  
  if (updatedConfig !== null) {
    await configManager.updateConfiguration(section, updatedConfig, true);
    console.log(colors.green(`✓ Configuration section '${section}' updated`));
  }
}

async function editObjectInteractively(obj: any, path: string): Promise<any | null> {
  const result = { ...obj };
  let hasChanges = false;
  
  for (const [key, value] of Object.entries(obj)) {
    const fullPath = `${path}.${key}`;
    
    console.log(`\n${colors.white(key)}: ${colors.gray(JSON.stringify(value))}`);
    
    const action = await Select.prompt({
      message: 'What would you like to do?',
      options: [
        { name: 'Keep current value', value: 'keep' },
        { name: 'Edit value', value: 'edit' },
        { name: 'Delete key', value: 'delete' }
      ]
    });
    
    switch (action) {
      case 'edit':
        const newValue = await promptForValue(key, value);
        if (newValue !== value) {
          result[key] = newValue;
          hasChanges = true;
        }
        break;
        
      case 'delete':
        delete result[key];
        hasChanges = true;
        break;
        
      case 'keep':
      default:
        // Keep existing value
        break;
    }
  }
  
  // Option to add new keys
  const addMore = await Confirm.prompt({
    message: 'Add new configuration keys?',
    default: false
  });
  
  if (addMore) {
    while (true) {
      const newKey = await Input.prompt({
        message: 'Enter new key name (empty to finish):',
        validate: (input) => input.trim().length > 0 || input.trim() === ''
      });
      
      if (!newKey.trim()) break;
      
      const newValue = await promptForValue(newKey, '');
      result[newKey] = newValue;
      hasChanges = true;
    }
  }
  
  return hasChanges ? result : null;
}

async function promptForValue(key: string, currentValue: any): Promise<any> {
  const valueType = await Select.prompt({
    message: `Select type for '${key}':`,
    options: [
      { name: 'String', value: 'string' },
      { name: 'Number', value: 'number' },
      { name: 'Boolean', value: 'boolean' },
      { name: 'JSON Object/Array', value: 'json' }
    ]
  });
  
  switch (valueType) {
    case 'string':
      return await Input.prompt({
        message: `Enter string value for '${key}':`,
        default: typeof currentValue === 'string' ? currentValue : ''
      });
      
    case 'number':
      const numberInput = await Input.prompt({
        message: `Enter number value for '${key}':`,
        default: typeof currentValue === 'number' ? String(currentValue) : '0',
        validate: (input) => !isNaN(Number(input)) || 'Please enter a valid number'
      });
      return Number(numberInput);
      
    case 'boolean':
      return await Confirm.prompt({
        message: `Set '${key}' to true?`,
        default: Boolean(currentValue)
      });
      
    case 'json':
      const jsonInput = await Input.prompt({
        message: `Enter JSON value for '${key}':`,
        default: JSON.stringify(currentValue, null, 2),
        validate: (input) => {
          try {
            JSON.parse(input);
            return true;
          } catch {
            return 'Please enter valid JSON';
          }
        }
      });
      return JSON.parse(jsonInput);
      
    default:
      return currentValue;
  }
}
```

## Implementation Guidelines

### Configuration Design Principles

1. **Hierarchical Override**: Clear precedence order with environment taking priority over files
2. **Schema Validation**: Comprehensive validation with helpful error messages
3. **Hot Reloading**: Support for live configuration updates without restart
4. **Type Safety**: Proper type conversion and validation for configuration values
5. **User-Friendly**: Interactive configuration editing with clear prompts

### Best Practices

- Use environment variables for deployment-specific configuration
- Implement comprehensive schema validation with clear error messages
- Support hot reloading for development convenience
- Provide interactive configuration editing for complex setups
- Use atomic file operations for configuration persistence
- Implement proper default values and fallback mechanisms
- Support cross-platform configuration paths and formats
- Provide configuration migration utilities for version updates

This configuration system provides a robust foundation for managing complex CLI application settings with flexibility, validation, and user-friendly interaction patterns.