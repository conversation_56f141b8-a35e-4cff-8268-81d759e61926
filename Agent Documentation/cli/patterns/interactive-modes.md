# Interactive Modes and REPL Patterns

## Overview

This document details interactive CLI patterns from claude-code-flow, showing how to implement REPL modes, interactive menus, and user input collection systems.

## Interactive Menu System

### Key-Based Navigation Pattern

The start command implements an interactive menu with real-time key processing:

```typescript
// Interactive mode (default)
else {
  console.log(colors.cyan('Starting in interactive mode...'));
  console.log();

  // Show available options
  console.log(colors.white.bold('Quick Actions:'));
  console.log('  [1] Start all processes');
  console.log('  [2] Start core processes only');
  console.log('  [3] Launch process management UI');
  console.log('  [4] Show system status');
  console.log('  [q] Quit');
  console.log();
  console.log(colors.gray('Press a key to select an option...'));

  // Handle user input
  while (true) {
    const key = await new Promise<string>((resolve) => {
      process.stdin.once('data', (data) => resolve(data.toString().trim()[0] || ''));
      process.stdin.resume();
    });

    switch (key) {
      case '1':
        console.log(colors.cyan('\nStarting all processes...'));
        await startWithProgress(processManager, 'all');
        console.log(colors.green.bold('✓'), 'All processes started');
        break;

      case '2':
        console.log(colors.cyan('\nStarting core processes...'));
        await startWithProgress(processManager, 'core');
        console.log(colors.green.bold('✓'), 'Core processes started');
        break;

      case '3':
        const ui = new ProcessUI(processManager);
        await ui.start();
        break;

      case '4':
        process.stdout.write('\x1Bc'); // Clear screen
        systemMonitor.printSystemHealth();
        console.log();
        systemMonitor.printEventLog(10);
        console.log();
        console.log(colors.gray('Press any key to continue...'));
        await new Promise<void>((resolve) => {
          process.stdin.once('data', () => resolve());
          process.stdin.resume();
        });
        break;

      case 'q':
      case 'Q':
        console.log(colors.yellow('\nShutting down...'));
        await processManager.stopAll();
        systemMonitor.stop();
        console.log(colors.green.bold('✓'), 'Shutdown complete');
        process.exit(0);
        break;
    }

    // Redraw menu after each action
    process.stdout.write('\x1Bc'); // Clear screen
    console.log(colors.cyan('🧠 Claude-Flow Interactive Mode'));
    console.log(colors.gray('─'.repeat(60)));
    
    // Show current status
    const stats = processManager.getSystemStats();
    console.log(colors.white('System Status:'), 
      colors.green(`${stats.runningProcesses}/${stats.totalProcesses} processes running`));
    console.log();
    
    console.log(colors.white.bold('Quick Actions:'));
    console.log('  [1] Start all processes');
    console.log('  [2] Start core processes only');
    console.log('  [3] Launch process management UI');
    console.log('  [4] Show system status');
    console.log('  [q] Quit');
    console.log();
    console.log(colors.gray('Press a key to select an option...'));
  }
}
```

### Screen Management Utilities

Helper functions for managing interactive screen updates:

```typescript
export const interact = {
  // Clear screen
  clear: (): void => {
    process.stdout.write('\x1b[2J\x1b[0f');
  },
  
  // Alternative clear screen method
  clearScreen: (): void => {
    process.stdout.write('\x1Bc');
  },
  
  // Wait for user input
  waitForKey: async (): Promise<string> => {
    return new Promise<string>((resolve) => {
      process.stdin.once('data', (data) => {
        resolve(data.toString().trim()[0] || '');
      });
      process.stdin.resume();
    });
  },
  
  // Wait for Enter key
  waitForEnter: async (): Promise<void> => {
    return new Promise<void>((resolve) => {
      process.stdin.once('data', () => resolve());
      process.stdin.resume();
    });
  }
};
```

## Prompt-Based Interaction Patterns

### Input Collection with Validation

The session management system demonstrates comprehensive input collection:

```typescript
async function saveSession(name: string | undefined, options: any): Promise<void> {
  if (!name) {
    if (options.auto) {
      name = `session-${new Date().toISOString().split('T')[0]}-${Date.now().toString().slice(-4)}`;
    } else {
      name = await Input.prompt({
        message: 'Enter session name:',
        default: `session-${new Date().toISOString().split('T')[0]}`,
      });
    }
  }

  // Additional prompts for optional information
  if (!options.description) {
    options.description = await Input.prompt({
      message: 'Enter session description (optional):',
      default: ''
    });
  }

  if (!options.tags) {
    const tagInput = await Input.prompt({
      message: 'Enter tags (comma-separated, optional):',
      default: ''
    });
    options.tags = tagInput ? tagInput.split(',').map((t: string) => t.trim()) : [];
  }
}
```

### Confirmation Patterns

Safe confirmation flows for destructive operations:

```typescript
async function deleteSession(sessionId: string, options: any): Promise<void> {
  try {
    const session = await loadSession(sessionId);
    
    if (!session) {
      console.error(colors.red(`Session '${sessionId}' not found`));
      return;
    }

    // Show what will be deleted
    if (!options.force) {
      console.log(`${colors.white('Session:')} ${session.name}`);
      console.log(`${colors.white('Created:')} ${session.createdAt.toLocaleString()}`);
      console.log(`${colors.white('Agents:')} ${session.state.agents.length}`);
      console.log(`${colors.white('Tasks:')} ${session.state.tasks.length}`);
      console.log();
      
      const confirmed = await Confirm.prompt({
        message: 'Are you sure you want to delete this session?',
        default: false,
      });

      if (!confirmed) {
        console.log(colors.gray('Delete cancelled'));
        return;
      }
    }

    // Perform deletion
    const filePath = paths.join(SESSION_DIR, `${session.id}.json`);
    await fs.unlink(filePath);

    console.log(colors.green('✓ Session deleted successfully'));
  } catch (error) {
    console.error(colors.red('Failed to delete session:'), (error as Error).message);
  }
}
```

### Multi-Step Confirmation Flows

Complex confirmation with additional safety checks:

```typescript
async function restoreSession(sessionId: string, options: any): Promise<void> {
  try {
    const session = await loadSession(sessionId);
    
    if (!session) {
      console.error(colors.red(`Session '${sessionId}' not found`));
      return;
    }

    // Show session info
    console.log(colors.cyan.bold('Session to restore:'));
    console.log(`${colors.white('Name:')} ${session.name}`);
    console.log(`${colors.white('Description:')} ${session.description || 'None'}`);
    console.log(`${colors.white('Agents:')} ${session.state.agents.length}`);
    console.log(`${colors.white('Tasks:')} ${session.state.tasks.length}`);
    console.log(`${colors.white('Created:')} ${session.createdAt.toLocaleString()}`);

    // First confirmation
    if (!options.force) {
      const action = options.merge ? 'merge with current session' : 'replace current session';
      const confirmed = await Confirm.prompt({
        message: `Are you sure you want to ${action}?`,
        default: false,
      });

      if (!confirmed) {
        console.log(colors.gray('Restore cancelled'));
        return;
      }
    }

    // Validate session integrity
    const expectedChecksum = await calculateChecksum(session.state);
    if (session.metadata.checksum !== expectedChecksum) {
      console.log(colors.yellow('⚠ Warning: Session checksum mismatch. Data may be corrupted.'));
      
      // Second confirmation for corrupted data
      if (!options.force) {
        const proceed = await Confirm.prompt({
          message: 'Continue anyway?',
          default: false,
        });
        
        if (!proceed) {
          console.log(colors.gray('Restore cancelled'));
          return;
        }
      }
    }

    // Proceed with restoration
    console.log(colors.yellow('Restoring session...'));
    await performSessionRestore(session, options);

  } catch (error) {
    console.error(colors.red('Failed to restore session:'), (error as Error).message);
  }
}
```

## Progressive Workflow Patterns

### Sequential Interactive Execution

The SPARC TDD workflow demonstrates progressive interactive execution:

```typescript
async function runTddFlow(ctx: CommandContext): Promise<void> {
  const workflow = [
    { mode: "spec-pseudocode", phase: "Specification" },
    { mode: "tdd", phase: "Red" },
    { mode: "code", phase: "Green" },
    { mode: "refinement-optimization-mode", phase: "Refactor" },
    { mode: "integration", phase: "Integration" }
  ];

  success("Starting SPARC TDD Workflow");
  console.log("Following Test-Driven Development with SPARC methodology");
  console.log();

  for (let i = 0; i < workflow.length; i++) {
    const step = workflow[i];
    
    info(`Phase ${i + 1}/5: ${step.phase} (${mode.name})`);
    console.log(`📋 ${step.description}`);
    console.log();

    // Execute the phase
    await executePhase(step, ctx);

    // Interactive progression control
    if (ctx.flags.sequential !== false && i < workflow.length - 1) {
      console.log();
      console.log(colors.yellow('Phase completed.'));
      console.log('Options:');
      console.log('  [Enter] Continue to next phase');
      console.log('  [s] Skip remaining phases');
      console.log('  [r] Restart current phase');
      console.log('  [q] Quit workflow');
      console.log();

      const choice = await Input.prompt({
        message: 'Choose action:',
        default: 'continue'
      });

      switch (choice.toLowerCase()) {
        case 's':
        case 'skip':
          console.log(colors.gray('Skipping remaining phases...'));
          return;
        case 'r':
        case 'restart':
          console.log(colors.blue('Restarting current phase...'));
          i--; // Restart current phase
          continue;
        case 'q':
        case 'quit':
          console.log(colors.gray('Workflow cancelled'));
          return;
        default:
          // Continue to next phase
          break;
      }
    }
  }

  success("SPARC TDD Workflow completed!");
}
```

### Interactive Configuration

Dynamic configuration collection during command execution:

```typescript
async function configureSparcWorkflow(baseConfig: any): Promise<any> {
  console.log(colors.cyan('🛠️  SPARC Workflow Configuration'));
  console.log();

  // Collect development mode
  const developmentMode = await Select.prompt({
    message: 'Select development mode:',
    options: [
      { name: 'Full Stack Development', value: 'full' },
      { name: 'Backend Only', value: 'backend-only' },
      { name: 'Frontend Only', value: 'frontend-only' },
      { name: 'API Only', value: 'api-only' }
    ],
    default: baseConfig.developmentMode || 'full'
  });

  // Collect test coverage target
  const testCoverage = await Input.prompt({
    message: 'Test coverage target (0-100):',
    default: String(baseConfig.testCoverage || 100),
    validate: (value) => {
      const num = parseInt(value);
      if (isNaN(num) || num < 0 || num > 100) {
        return 'Coverage must be a number between 0 and 100';
      }
      return true;
    }
  });

  // Collect optional features
  const features = await Checkbox.prompt({
    message: 'Select optional features:',
    options: [
      { name: 'Enable verbose logging', value: 'verbose' },
      { name: 'Skip research phase', value: 'skip-research' },
      { name: 'Skip test generation', value: 'skip-tests' },
      { name: 'Enable parallel execution', value: 'parallel' },
      { name: 'Auto-commit phases', value: 'auto-commit' }
    ]
  });

  return {
    ...baseConfig,
    developmentMode,
    testCoverage: parseInt(testCoverage),
    verbose: features.includes('verbose'),
    skipResearch: features.includes('skip-research'),
    skipTests: features.includes('skip-tests'),
    parallel: features.includes('parallel'),
    autoCommit: features.includes('auto-commit')
  };
}
```

## Progress and Loading Patterns

### Spinner and Progress Indicators

Visual feedback during long-running operations:

```typescript
export const patterns = {
  // Loading spinner simulation
  loading: async (message: string, fn: () => Promise<void>): Promise<void> => {
    const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    let i = 0;
    
    const interval = setInterval(() => {
      process.stdout.write(`\r${chalk.blue(frames[i++ % frames.length])} ${message}`);
    }, 100);
    
    try {
      await fn();
      clearInterval(interval);
      process.stdout.write(`\r${format.success(message)}\n`);
    } catch (error) {
      clearInterval(interval);
      process.stdout.write(`\r${format.error(message)}\n`);
      throw error;
    }
  },
  
  // Progress bar
  progressBar: (current: number, total: number, message?: string): void => {
    const progress = format.progress(current, total);
    const msg = message ? ` ${message}` : '';
    process.stdout.write(`\r${progress}${msg}`);
    
    if (current >= total) {
      process.stdout.write('\n');
    }
  }
};

// Usage in interactive flows
async function startWithProgress(processManager: ProcessManager, mode: 'all' | 'core'): Promise<void> {
  const processes = mode === 'all' 
    ? ['event-bus', 'memory-manager', 'terminal-pool', 'coordinator', 'mcp-server', 'orchestrator']
    : ['event-bus', 'memory-manager', 'mcp-server'];
  
  for (let i = 0; i < processes.length; i++) {
    const processId = processes[i];
    const progress = `[${i + 1}/${processes.length}]`;
    
    console.log(colors.gray(`${progress} Starting ${processId}...`));
    
    await patterns.loading(`Starting ${processId}`, async () => {
      await processManager.startProcess(processId);
    });
    
    // Brief delay between starts for user feedback
    if (i < processes.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
}
```

### Real-Time Status Updates

Dynamic status display with live updates:

```typescript
class InteractiveSystemMonitor {
  private updateInterval: NodeJS.Timeout | null = null;
  
  start(): void {
    this.updateInterval = setInterval(() => {
      this.refreshDisplay();
    }, 1000);
  }
  
  stop(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }
  
  private refreshDisplay(): void {
    // Save cursor position
    process.stdout.write('\x1b[s');
    
    // Move to top of display area
    process.stdout.write('\x1b[H');
    
    // Clear the display area
    process.stdout.write('\x1b[J');
    
    // Redraw status
    this.printSystemHealth();
    this.printRunningProcesses();
    this.printRecentEvents();
    
    // Restore cursor position
    process.stdout.write('\x1b[u');
  }
  
  private printSystemHealth(): void {
    const stats = this.processManager.getSystemStats();
    console.log(colors.cyan.bold('System Health'));
    console.log('─'.repeat(40));
    console.log(`Running Processes: ${colors.green(stats.runningProcesses)}/${stats.totalProcesses}`);
    console.log(`Memory Usage: ${colors.yellow(this.formatMemoryUsage())}`);
    console.log(`CPU Usage: ${colors.blue(this.formatCpuUsage())}`);
    console.log(`Uptime: ${colors.gray(this.formatUptime())}`);
    console.log();
  }
}
```

## Error Handling in Interactive Modes

### Graceful Degradation Patterns

Handling errors while maintaining interactive flow:

```typescript
async function interactiveCommandLoop(): Promise<void> {
  while (true) {
    try {
      const command = await Input.prompt({
        message: colors.cyan('claude-flow> '),
        validate: (input) => input.trim().length > 0 || 'Command cannot be empty'
      });

      if (command.toLowerCase() === 'exit' || command.toLowerCase() === 'quit') {
        console.log(colors.gray('Goodbye!'));
        break;
      }

      await executeInteractiveCommand(command);
      
    } catch (error) {
      if (error instanceof CLIError) {
        console.error(colors.red(`Error: ${error.message}`));
        if (error.showUsage) {
          console.log(colors.gray('Type "help" for usage information'));
        }
      } else {
        console.error(colors.red(`Unexpected error: ${error instanceof Error ? error.message : String(error)}`));
        console.log(colors.gray('The interactive session will continue...'));
      }
      console.log(); // Add spacing after error
    }
  }
}

async function executeInteractiveCommand(command: string): Promise<void> {
  const args = command.trim().split(/\s+/);
  const commandName = args[0];
  const commandArgs = args.slice(1);

  // Handle built-in interactive commands
  switch (commandName) {
    case 'help':
      showInteractiveHelp();
      return;
    case 'clear':
      interact.clear();
      return;
    case 'status':
      await showSystemStatus();
      return;
    default:
      // Execute regular commands in interactive context
      await registry.execute(commandName, commandArgs, {});
  }
}
```

## Implementation Guidelines

### Interactive Design Principles

1. **Immediate Feedback**: Always provide immediate visual feedback for user actions
2. **Clear Navigation**: Make it obvious how to move through interactive flows
3. **Safe Exits**: Provide clear ways to cancel or exit at any point
4. **State Preservation**: Maintain context across interactive sessions
5. **Error Recovery**: Handle errors gracefully without losing user progress

### Best Practices

- Use consistent key bindings and interaction patterns
- Provide visual cues for interactive elements (colors, symbols)
- Implement proper terminal cleanup on exit
- Handle terminal resize events gracefully
- Support both keyboard and programmatic interaction
- Validate user input early and provide helpful error messages
- Use progressive disclosure to avoid overwhelming users
- Implement undo/redo capabilities for complex workflows

This interactive system provides a rich foundation for building engaging command-line interfaces that users can navigate efficiently and safely.