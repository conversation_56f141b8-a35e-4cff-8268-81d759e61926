# Command Chaining and Pipeline Patterns

## Overview

This document details command composition and chaining patterns from claude-code-flow, showing how commands can be orchestrated together for complex workflows and data pipelines.

## Sequential Command Execution Patterns

### SPARC Workflow Orchestration

The SPARC system demonstrates sophisticated sequential command execution:

```typescript
async function runFullSparcDevelopment(ctx: CommandContext): Promise<void> {
  const objective = ctx.args.join(" ").trim();
  
  const options = {
    projectName: ctx.flags.project as string || "sparc-project",
    verbose: ctx.flags.verbose as boolean || false,
    dryRun: ctx.flags['dry-run'] as boolean || false,
    skipResearch: ctx.flags['skip-research'] as boolean || false,
    skipTests: ctx.flags['skip-tests'] as boolean || false,
    developmentMode: ctx.flags.mode as string || "full",
    parallel: ctx.flags.parallel !== false,
    commitFrequency: ctx.flags['commit-freq'] as string || "phase"
  };

  try {
    // Sequential phase execution with dependency management
    if (!options.skipResearch) {
      await executeResearchPhase(objective, options);
    }

    await executeSpecificationPhase(objective, options);
    await executePseudocodePhase(objective, options);
    await executeArchitecturePhase(objective, options);
    await executeRefinementPhase(objective, options);
    await executeCompletionPhase(objective, options);

    success("✅ SPARC development cycle completed successfully!");
    
  } catch (err) {
    error(`SPARC execution failed: ${err instanceof Error ? err.message : String(err)}`);
    // Implement rollback logic here
    await performRollback(options);
  }
}
```

### Phase Dependency Management

Each phase can depend on outputs from previous phases:

```typescript
async function executeSpecificationPhase(objective: string, options: any): Promise<void> {
  info("📋 Phase 1: Specification");
  
  // Check for research phase outputs
  let researchContext = {};
  if (!options.skipResearch) {
    researchContext = await loadPhaseOutput('research') || {};
  }
  
  const specificationContext = {
    objective,
    researchFindings: researchContext,
    developmentMode: options.developmentMode
  };
  
  console.log("  • Extracting functional requirements...");
  const requirements = await extractRequirements(specificationContext);
  
  console.log("  • Defining acceptance criteria...");
  const acceptanceCriteria = await defineAcceptanceCriteria(requirements);
  
  console.log("  • Creating user stories...");
  const userStories = await createUserStories(requirements, acceptanceCriteria);
  
  // Save phase output for next phases
  const phaseOutput = {
    requirements,
    acceptanceCriteria,
    userStories,
    timestamp: Date.now()
  };
  
  await savePhaseOutput('specification', phaseOutput);
  
  if (options.commitFrequency === "phase") {
    await commitPhaseChanges("Specification complete", phaseOutput);
  }
}
```

### Conditional Workflow Execution

Complex branching logic based on phase results:

```typescript
async function executeRefinementPhase(objective: string, options: any): Promise<void> {
  info("🔄 Phase 4: Refinement (TDD)");
  
  const architectureOutput = await loadPhaseOutput('architecture');
  const specificationOutput = await loadPhaseOutput('specification');
  
  if (!options.skipTests) {
    // TDD workflow
    console.log("  🔴 Red: Writing failing tests...");
    await executeTddPhase('red', {
      requirements: specificationOutput.requirements,
      architecture: architectureOutput.components
    });
    
    console.log("  🟢 Green: Implementing minimal code...");
    await executeTddPhase('green', {
      tests: await loadPhaseOutput('red-tests'),
      architecture: architectureOutput.components
    });
    
    console.log("  🔵 Refactor: Optimizing implementation...");
    await executeTddPhase('refactor', {
      implementation: await loadPhaseOutput('green-implementation'),
      targetCoverage: options.testCoverage
    });
    
  } else {
    // Direct implementation path
    console.log("  • Implementing core functionality...");
    await implementDirectly(architectureOutput, specificationOutput);
    
    console.log("  • Adding error handling...");
    await addErrorHandling();
    
    console.log("  • Optimizing performance...");
    await optimizeImplementation();
  }
  
  // Validate phase completion
  const validationResult = await validateRefinementPhase(options.testCoverage);
  if (!validationResult.success) {
    throw new Error(`Refinement phase validation failed: ${validationResult.errors.join(', ')}`);
  }
}
```

## Parallel Command Execution Patterns

### Concurrent Process Management

Running multiple commands in parallel with coordination:

```typescript
async function startWithProgress(processManager: ProcessManager, mode: 'all' | 'core'): Promise<void> {
  const processes = mode === 'all' 
    ? ['event-bus', 'memory-manager', 'terminal-pool', 'coordinator', 'mcp-server', 'orchestrator']
    : ['event-bus', 'memory-manager', 'mcp-server'];
  
  // Parallel startup with dependency management
  const dependencyGraph = {
    'event-bus': [],
    'memory-manager': ['event-bus'],
    'terminal-pool': ['event-bus'],
    'coordinator': ['event-bus', 'memory-manager'],
    'mcp-server': ['event-bus', 'memory-manager'],
    'orchestrator': ['coordinator', 'mcp-server', 'terminal-pool']
  };
  
  const started = new Set<string>();
  const starting = new Set<string>();
  
  async function startProcess(processId: string): Promise<void> {
    if (started.has(processId) || starting.has(processId)) {
      return;
    }
    
    starting.add(processId);
    
    // Wait for dependencies
    const deps = dependencyGraph[processId] || [];
    await Promise.all(deps.map(dep => startProcess(dep)));
    
    console.log(colors.gray(`Starting ${processId}...`));
    try {
      await processManager.startProcess(processId);
      started.add(processId);
      starting.delete(processId);
      console.log(colors.green(`✓ ${processId} started`));
    } catch (error: unknown) {
      starting.delete(processId);
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log(colors.red(`✗ ${processId} failed: ${errorMessage}`));
      throw error;
    }
  }
  
  // Start all processes in parallel, respecting dependencies
  await Promise.all(processes.map(processId => startProcess(processId)));
  
  // Save system state after all processes are started
  await processManager.saveSystemState();
}
```

### Parallel Workflow Execution

Executing independent tasks concurrently:

```typescript
async function executeParallelWorkflow(workflow: WorkflowDefinition, options: any): Promise<void> {
  const parallelGroups = groupTasksByDependencies(workflow.tasks);
  
  for (const group of parallelGroups) {
    console.log(`\nExecuting parallel group: ${group.map(t => t.name).join(', ')}`);
    
    // Execute all tasks in this group concurrently
    const groupPromises = group.map(async (task) => {
      const taskContext = {
        task,
        workflow,
        options,
        previousResults: await loadPreviousResults(task.dependencies)
      };
      
      return executeWorkflowTask(taskContext);
    });
    
    // Wait for all tasks in the group to complete
    const groupResults = await Promise.allSettled(groupPromises);
    
    // Handle any failures
    const failures = groupResults
      .map((result, index) => ({ result, task: group[index] }))
      .filter(({ result }) => result.status === 'rejected');
    
    if (failures.length > 0) {
      console.error(colors.red(`${failures.length} tasks failed in parallel group:`));
      for (const { task, result } of failures) {
        console.error(colors.red(`  • ${task.name}: ${result.reason}`));
      }
      
      if (options.stopOnError) {
        throw new Error(`Parallel execution failed: ${failures.length} tasks failed`);
      }
    }
    
    // Save results for next group
    const successes = groupResults
      .map((result, index) => ({ result, task: group[index] }))
      .filter(({ result }) => result.status === 'fulfilled');
    
    for (const { task, result } of successes) {
      await saveTaskResult(task.name, result.value);
    }
  }
}

function groupTasksByDependencies(tasks: Task[]): Task[][] {
  const groups: Task[][] = [];
  const processed = new Set<string>();
  
  while (processed.size < tasks.length) {
    const currentGroup = tasks.filter(task => 
      !processed.has(task.name) && 
      task.dependencies.every(dep => processed.has(dep))
    );
    
    if (currentGroup.length === 0) {
      throw new Error('Circular dependency detected in workflow tasks');
    }
    
    groups.push(currentGroup);
    currentGroup.forEach(task => processed.add(task.name));
  }
  
  return groups;
}
```

## Command Pipeline Patterns

### Data Flow Between Commands

Commands can pass data through a pipeline:

```typescript
interface PipelineStage {
  name: string;
  command: string;
  transform?: (input: any) => any;
  validate?: (output: any) => boolean;
}

async function executePipeline(stages: PipelineStage[], initialInput: any): Promise<any> {
  let currentData = initialInput;
  
  for (let i = 0; i < stages.length; i++) {
    const stage = stages[i];
    console.log(`\n📋 Pipeline Stage ${i + 1}: ${stage.name}`);
    
    try {
      // Transform input if needed
      const stageInput = stage.transform ? stage.transform(currentData) : currentData;
      
      // Execute the command with the transformed input
      const stageOutput = await executeStageCommand(stage.command, stageInput);
      
      // Validate output if validation is provided
      if (stage.validate && !stage.validate(stageOutput)) {
        throw new Error(`Stage output validation failed: ${stage.name}`);
      }
      
      currentData = stageOutput;
      
      console.log(colors.green(`✓ Stage ${i + 1} completed: ${stage.name}`));
      
      // Optional: Save intermediate results
      await savePipelineStageResult(i, stage.name, currentData);
      
    } catch (error) {
      console.error(colors.red(`✗ Stage ${i + 1} failed: ${stage.name}`));
      console.error(colors.red(`Error: ${error instanceof Error ? error.message : String(error)}`));
      
      // Pipeline failure handling
      throw new PipelineError(`Pipeline failed at stage ${i + 1}: ${stage.name}`, {
        stage: i + 1,
        stageName: stage.name,
        input: currentData,
        error
      });
    }
  }
  
  return currentData;
}

// Example pipeline definition
const codeGenerationPipeline: PipelineStage[] = [
  {
    name: 'Research Requirements',
    command: 'sparc run researcher',
    transform: (input) => ({ objective: input.task, depth: 'detailed' }),
    validate: (output) => output.requirements && output.requirements.length > 0
  },
  {
    name: 'Generate Specification',
    command: 'sparc run spec-pseudocode',
    transform: (input) => ({ requirements: input.requirements }),
    validate: (output) => output.specification && output.pseudocode
  },
  {
    name: 'Create Architecture',
    command: 'sparc run architect',
    transform: (input) => ({ specification: input.specification }),
    validate: (output) => output.architecture && output.components
  },
  {
    name: 'Implement Code',
    command: 'sparc run code',
    transform: (input) => ({ 
      architecture: input.architecture,
      specification: input.specification 
    }),
    validate: (output) => output.implementation && output.tests
  }
];
```

### Stream Processing Pipeline

Handling continuous data streams through command chains:

```typescript
class StreamingPipeline {
  private stages: StreamStage[] = [];
  private running = false;
  
  addStage(stage: StreamStage): this {
    this.stages.push(stage);
    return this;
  }
  
  async start(inputStream: ReadableStream): Promise<void> {
    this.running = true;
    let currentStream = inputStream;
    
    for (const stage of this.stages) {
      if (!this.running) break;
      
      console.log(`Starting stream stage: ${stage.name}`);
      currentStream = await this.processStreamStage(currentStream, stage);
    }
    
    return this.consumeOutputStream(currentStream);
  }
  
  private async processStreamStage(
    input: ReadableStream, 
    stage: StreamStage
  ): Promise<ReadableStream> {
    
    return new ReadableStream({
      async start(controller) {
        const reader = input.getReader();
        
        try {
          while (true) {
            const { done, value } = await reader.read();
            
            if (done) {
              controller.close();
              break;
            }
            
            // Process the chunk through the stage
            const processedValue = await stage.process(value);
            if (processedValue !== undefined) {
              controller.enqueue(processedValue);
            }
          }
        } catch (error) {
          controller.error(error);
        } finally {
          reader.releaseLock();
        }
      }
    });
  }
}

// Example usage
const logAnalysisPipeline = new StreamingPipeline()
  .addStage({
    name: 'Parse Log Lines',
    process: async (chunk: string) => {
      return chunk.split('\n').map(line => parseLogLine(line));
    }
  })
  .addStage({
    name: 'Filter Errors',
    process: async (logEntries: LogEntry[]) => {
      return logEntries.filter(entry => entry.level === 'ERROR');
    }
  })
  .addStage({
    name: 'Aggregate by Source',
    process: async (errorEntries: LogEntry[]) => {
      return aggregateErrorsBySource(errorEntries);
    }
  });
```

## Memory-Driven Command Chaining

### Shared State Management

Using the memory system to coordinate between commands:

```typescript
async function executeMemoryDrivenWorkflow(workflowId: string, steps: WorkflowStep[]): Promise<void> {
  const workflowMemoryNamespace = `workflow-${workflowId}`;
  
  // Initialize workflow state
  await storeInMemory(`${workflowMemoryNamespace}:state`, {
    id: workflowId,
    status: 'running',
    currentStep: 0,
    startTime: Date.now(),
    steps: steps.map(s => ({ name: s.name, status: 'pending' }))
  });
  
  try {
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      
      // Update workflow state
      await updateWorkflowState(workflowMemoryNamespace, i, 'running');
      
      console.log(`\n📋 Step ${i + 1}/${steps.length}: ${step.name}`);
      
      // Load context from previous steps
      const previousStepOutputs = await loadPreviousStepOutputs(workflowMemoryNamespace, i);
      
      // Create step context
      const stepContext = {
        workflowId,
        stepIndex: i,
        stepName: step.name,
        memoryNamespace: workflowMemoryNamespace,
        previousOutputs: previousStepOutputs,
        ...step.config
      };
      
      // Execute the step
      const stepResult = await executeWorkflowStep(step, stepContext);
      
      // Store step output in memory
      await storeInMemory(`${workflowMemoryNamespace}:step-${i}:output`, stepResult);
      
      // Update step status
      await updateWorkflowState(workflowMemoryNamespace, i, 'completed');
      
      console.log(colors.green(`✓ Step ${i + 1} completed: ${step.name}`));
    }
    
    // Mark workflow as completed
    await updateWorkflowStatus(workflowMemoryNamespace, 'completed');
    
  } catch (error) {
    await updateWorkflowStatus(workflowMemoryNamespace, 'failed');
    throw error;
  }
}

async function executeWorkflowStep(step: WorkflowStep, context: any): Promise<any> {
  // Commands can access shared memory through the context
  const enhancedContext = {
    ...context,
    // Memory access helpers
    storeOutput: (key: string, value: any) => 
      storeInMemory(`${context.memoryNamespace}:${key}`, value),
    loadInput: (key: string) => 
      loadFromMemory(`${context.memoryNamespace}:${key}`),
    queryPrevious: (query: string) => 
      queryMemory(`${context.memoryNamespace}:*`, query)
  };
  
  // Execute the step command with enhanced context
  return await step.execute(enhancedContext);
}
```

### Cross-Command Data Sharing

Commands sharing data through standardized memory patterns:

```typescript
// Example SPARC mode that stores and retrieves shared data
function buildSparcPrompt(mode: SparcMode, taskDescription: string, flags: any): string {
  const memoryNamespace = flags.namespace || mode.slug || "default";
  
  return `# SPARC Development Mode: ${mode.name}

## Your Task
${taskDescription}

## SPARC Development Environment

### Memory Persistence Commands
Use these commands to coordinate with other SPARC modes:

\`\`\`bash
# Store your progress and findings
npx claude-flow memory store ${memoryNamespace}_progress "Current status and findings"

# Check for previous work from other modes
npx claude-flow memory query ${memoryNamespace}

# Store phase-specific results
npx claude-flow memory store ${memoryNamespace}_${flags.tddPhase || 'results'} "Phase output and decisions"

# Load inputs from previous phases
npx claude-flow memory get ${memoryNamespace}_requirements
npx claude-flow memory get ${memoryNamespace}_architecture
\`\`\`

### Integration with Other SPARC Modes
When working with other SPARC modes, use memory to:
- Share findings with spec-pseudocode mode: Store as \`${memoryNamespace}_specification\`
- Pass requirements to architect mode: Store as \`${memoryNamespace}_requirements\`
- Coordinate with code and tdd modes: Store as \`${memoryNamespace}_implementation_plan\`
- Communicate results to integration mode: Store as \`${memoryNamespace}_completion_status\`

### Workflow State Management
${flags.workflowStep ? `
**Current Workflow Step**: ${flags.workflowStep} of ${flags.totalSteps}
- Review previous steps: \`npx claude-flow memory query ${memoryNamespace}_step_*\`
- Store this step's output: \`npx claude-flow memory store ${memoryNamespace}_step_${flags.workflowStep}_output "<results>"\`
` : ''}

Now proceed with your task following the SPARC methodology and your specific role instructions.`;
}
```

## Error Handling and Recovery in Chains

### Rollback Mechanisms

Implementing rollback for failed command chains:

```typescript
interface ChainCheckpoint {
  stepIndex: number;
  stepName: string;
  state: any;
  timestamp: number;
}

class CommandChain {
  private checkpoints: ChainCheckpoint[] = [];
  private rollbackActions: Array<() => Promise<void>> = [];
  
  async executeWithRollback(steps: ChainStep[]): Promise<any> {
    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        
        // Create checkpoint before step execution
        const checkpoint = await this.createCheckpoint(i, step.name);
        this.checkpoints.push(checkpoint);
        
        console.log(`\n📋 Executing step ${i + 1}: ${step.name}`);
        
        // Execute the step
        const result = await step.execute();
        
        // Register rollback action if provided
        if (step.rollback) {
          this.rollbackActions.push(step.rollback);
        }
        
        console.log(colors.green(`✓ Step ${i + 1} completed`));
      }
      
      return { success: true, checkpoints: this.checkpoints };
      
    } catch (error) {
      console.error(colors.red(`\n✗ Command chain failed at step ${this.checkpoints.length + 1}`));
      console.error(colors.red(`Error: ${error instanceof Error ? error.message : String(error)}`));
      
      // Perform rollback
      await this.performRollback();
      
      throw error;
    }
  }
  
  private async performRollback(): Promise<void> {
    console.log(colors.yellow('\n🔄 Performing rollback...'));
    
    // Execute rollback actions in reverse order
    for (let i = this.rollbackActions.length - 1; i >= 0; i--) {
      try {
        console.log(colors.gray(`Rolling back step ${i + 1}...`));
        await this.rollbackActions[i]();
        console.log(colors.green(`✓ Step ${i + 1} rolled back`));
      } catch (rollbackError) {
        console.error(colors.red(`✗ Failed to rollback step ${i + 1}: ${rollbackError}`));
        // Continue with other rollback actions
      }
    }
    
    // Restore to last known good state
    if (this.checkpoints.length > 0) {
      const lastCheckpoint = this.checkpoints[this.checkpoints.length - 1];
      await this.restoreFromCheckpoint(lastCheckpoint);
    }
    
    console.log(colors.yellow('Rollback completed'));
  }
}
```

## Implementation Guidelines

### Command Chain Design Principles

1. **Composability**: Commands should be designed to work well together
2. **State Management**: Use consistent patterns for sharing state between commands
3. **Error Propagation**: Implement proper error handling and recovery mechanisms
4. **Resource Management**: Ensure proper cleanup of resources in chain execution
5. **Monitoring**: Provide visibility into chain execution progress and status

### Best Practices

- Design commands with clear input/output contracts
- Use memory systems for coordination between commands
- Implement proper dependency management for parallel execution
- Provide rollback mechanisms for complex operations
- Use checkpointing for long-running command chains
- Monitor resource usage during parallel execution
- Implement circuit breakers for external service dependencies
- Provide clear progress feedback during chain execution

This command chaining system provides a robust foundation for orchestrating complex workflows and data processing pipelines through CLI interfaces.