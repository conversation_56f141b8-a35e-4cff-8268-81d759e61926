# Argument Parsing and Parameter Handling

## Overview

This document details the argument parsing patterns from claude-code-flow, showing how command-line parameters are parsed, validated, and processed.

## Rust Argument Parsing with clap

### Basic Argument Types

```rust
use clap::{Parser, ValueEnum};

#[derive(Parser)]
#[clap(author, version, about)]
pub struct Cli {
    /// Positional argument example
    #[clap(value_name = "FILE")]
    pub input: Option<String>,

    /// Required option with short and long forms
    #[clap(short, long, required = true)]
    pub name: String,

    /// Optional flag (boolean)
    #[clap(short, long)]
    pub verbose: bool,

    /// Counter flag (can be used multiple times)
    #[clap(short = 'v', long, parse(from_occurrences))]
    pub verbosity: u8,

    /// Option with default value
    #[clap(short, long, default_value = "3000")]
    pub port: u16,

    /// Multiple values (Vec)
    #[clap(short, long, value_delimiter = ',')]
    pub tags: Vec<String>,

    /// Enum value with validation
    #[clap(long, value_enum)]
    pub output_format: OutputFormat,
}

#[derive(Clone, Debug, ValueEnum)]
pub enum OutputFormat {
    Json,
    Yaml,
    Table,
    Csv,
}
```

### Advanced Argument Validation

```rust
use clap::{Parser, Args};
use std::path::PathBuf;

#[derive(Parser)]
pub struct Cli {
    #[clap(flatten)]
    pub global_opts: GlobalOptions,

    #[clap(subcommand)]
    pub command: Commands,
}

#[derive(Args)]
pub struct GlobalOptions {
    /// Configuration file path
    #[clap(
        short,
        long,
        value_name = "FILE",
        value_parser = validate_config_path
    )]
    pub config: Option<PathBuf>,

    /// Port number with range validation
    #[clap(
        short,
        long,
        default_value = "3000",
        value_parser = clap::value_parser!(u16).range(1..=65535)
    )]
    pub port: u16,

    /// Custom validation function
    #[clap(long, value_parser = validate_email)]
    pub email: Option<String>,
}

// Custom validators
fn validate_config_path(s: &str) -> Result<PathBuf, String> {
    let path = PathBuf::from(s);
    if path.extension() == Some("toml".as_ref()) || path.extension() == Some("yaml".as_ref()) {
        Ok(path)
    } else {
        Err("Config file must be .toml or .yaml".to_string())
    }
}

fn validate_email(s: &str) -> Result<String, String> {
    if s.contains('@') && s.contains('.') {
        Ok(s.to_string())
    } else {
        Err("Invalid email format".to_string())
    }
}
```

### Argument Groups and Conflicts

```rust
#[derive(Parser)]
pub struct Cli {
    /// Mutually exclusive argument group
    #[clap(group = "version", conflicts_with_all = &["minor", "patch"])]
    #[clap(long)]
    pub major: bool,

    #[clap(group = "version")]
    #[clap(long)]
    pub minor: bool,

    #[clap(group = "version")]
    #[clap(long)]
    pub patch: bool,

    /// Required when using --config
    #[clap(long, requires = "config")]
    pub spec_in: Option<String>,

    /// Config file
    #[clap(short, long)]
    pub config: Option<String>,

    /// Conditional requirements
    #[clap(
        value_name = "INPUT_FILE",
        required_unless_present = "spec_in"
    )]
    pub input: Option<String>,
}
```

## Command Compatibility Layer

### Cliffy-to-Commander Mapping

The system uses a compatibility layer that maps Cliffy-style commands to Commander.js:

```typescript
export class Command {
  private commander: CommanderCommand;
  private optionConfigs: Map<string, OptionOptions> = new Map();

  constructor(name?: string) {
    this.commander = new CommanderCommand(name);
  }

  option(flags: string, description: string, options?: OptionOptions): this {
    let commanderOption = this.commander.option(flags, description);
    
    if (options) {
      if (options.default !== undefined) {
        commanderOption = this.commander.option(flags, description, options.default);
      }
      this.optionConfigs.set(flags, options);
    }
    
    return this;
  }

  arguments(args: string): this {
    this.commander.arguments(args);
    return this;
  }

  action(handler: ActionHandler): this {
    this.commander.action(async (...args) => {
      // In cliffy, options come first, then arguments
      // In commander, arguments come first, then options
      const options = args[args.length - 1];
      const cmdArgs = args.slice(0, -1);
      await handler(options, ...cmdArgs);
    });
    return this;
  }
}
```

## Option Definition Patterns

### Standard Option Types

The system supports multiple option types with this interface:

```typescript
interface OptionOptions {
  default?: any;
  required?: boolean;
  global?: boolean;
  hidden?: boolean;
  conflicts?: string[];
  depends?: string[];
  collect?: boolean;
  value?: (value: any, previous?: any) => any;
}

interface CommandOption {
  flag: string;
  description: string;
  hasValue?: boolean;
  defaultValue?: any;
  aliases?: string[];
}
```

### Real Option Examples

From the `start` command implementation:

```typescript
export const startCommand = new Command()
  .description('Start the Claude-Flow orchestration system')
  .option('-d, --daemon', 'Run as daemon in background')
  .option('-p, --port <port:number>', 'MCP server port', { default: 3000 })
  .option('--mcp-transport <transport:string>', 'MCP transport type (stdio, http)', {
    default: 'stdio',
  })
  .option('-u, --ui', 'Launch interactive process management UI')
  .option('-v, --verbose', 'Enable verbose logging')
  .option('--auto-start', 'Automatically start all processes')
  .option('--config <path:string>', 'Configuration file path')
  .option('--force', 'Force start even if already running')
  .option('--health-check', 'Perform health checks before starting')
  .option('--timeout <seconds:number>', 'Startup timeout in seconds', { default: 60 })
```

### Session Command Option Patterns

Complex option combinations from session management:

```typescript
.command('list', Command)
  .description('List all saved sessions')
  .option('-a, --active', 'Show only active sessions')
  .option('--format <format:string>', 'Output format (table, json)', { default: 'table' })

.command('save', Command)
  .description('Save current session state')
  .arguments('[name:string]')
  .option('-d, --description <desc:string>', 'Session description')
  .option('-t, --tags <tags:string>', 'Comma-separated tags')
  .option('--auto', 'Auto-generate session name')

.command('export', Command)
  .description('Export session to file')
  .arguments('<session-id:string> <output-file:string>')
  .option('--format <format:string>', 'Export format (json, yaml)', { default: 'json' })
  .option('--include-memory', 'Include agent memory in export')
```

## Rust Argument Processing Patterns

### Environment Variable Integration

```rust
#[derive(Parser)]
pub struct Cli {
    /// Database URL (can also be set via DATABASE_URL env var)
    #[clap(long, env = "DATABASE_URL")]
    pub database_url: Option<String>,

    /// API key with fallback to env
    #[clap(
        long,
        env = "API_KEY",
        hide_env_values = true,
        help = "API key for authentication"
    )]
    pub api_key: Option<String>,

    /// Multiple environment sources
    #[clap(
        long,
        env = "LOG_LEVEL",
        default_value = "info",
        possible_values = &["debug", "info", "warn", "error"]
    )]
    pub log_level: String,
}
```

### Complex Subcommand Processing

```rust
use clap::{Parser, Subcommand};
use anyhow::Result;

#[derive(Parser)]
pub struct SessionCli {
    #[clap(subcommand)]
    pub command: SessionCommands,
}

#[derive(Subcommand)]
pub enum SessionCommands {
    /// List all saved sessions
    List {
        /// Show only active sessions
        #[clap(short, long)]
        active: bool,

        /// Output format
        #[clap(long, default_value = "table", possible_values = &["table", "json"])]
        format: String,
    },

    /// Save current session state
    Save {
        /// Session name (optional)
        name: Option<String>,

        /// Session description
        #[clap(short, long)]
        description: Option<String>,

        /// Comma-separated tags
        #[clap(short, long, value_delimiter = ',')]
        tags: Vec<String>,

        /// Auto-generate session name
        #[clap(long)]
        auto: bool,
    },

    /// Export session to file
    Export {
        /// Session ID
        session_id: String,

        /// Output file path
        output_file: String,

        /// Export format
        #[clap(long, default_value = "json", possible_values = &["json", "yaml"])]
        format: String,

        /// Include agent memory
        #[clap(long)]
        include_memory: bool,
    },
}

// Processing implementation
pub async fn handle_session_command(cmd: SessionCommands) -> Result<()> {
    match cmd {
        SessionCommands::List { active, format } => {
            let sessions = if active {
                list_active_sessions().await?
            } else {
                list_all_sessions().await?
            };
            
            match format.as_str() {
                "json" => print_json(&sessions)?,
                "table" => print_table(&sessions)?,
                _ => unreachable!(),
            }
        }
        
        SessionCommands::Save { name, description, tags, auto } => {
            let session_name = if auto {
                generate_session_name()
            } else {
                name.unwrap_or_else(|| prompt_for_name())
            };
            
            save_session(SessionConfig {
                name: session_name,
                description,
                tags,
            }).await?;
        }
        
        SessionCommands::Export { session_id, output_file, format, include_memory } => {
            let session = load_session(&session_id).await?;
            let export_data = if include_memory {
                session.with_memory()
            } else {
                session.without_memory()
            };
            
            match format.as_str() {
                "json" => export_json(&export_data, &output_file)?,
                "yaml" => export_yaml(&export_data, &output_file)?,
                _ => unreachable!(),
            }
        }
    }
    
    Ok(())
}
```

### Variadic Arguments and Raw Args

```rust
#[derive(Parser)]
#[clap(trailing_var_arg = true)]
pub struct ExecCli {
    /// Command to execute
    command: String,
    
    /// Arguments to pass to the command
    #[clap(raw = true)]
    args: Vec<String>,
}

// Example: claude-flow exec docker run -it ubuntu bash
// command = "docker"
// args = ["run", "-it", "ubuntu", "bash"]

#[derive(Parser)]
pub struct SparcCli {
    /// SPARC mode or task description
    #[clap(required = true, min_values = 1)]
    pub task_parts: Vec<String>,
    
    /// Optional flags after --
    #[clap(last = true)]
    pub passthrough_args: Vec<String>,
}

// Example: claude-flow sparc run coder implement auth system -- --skip-tests --verbose
// task_parts = ["run", "coder", "implement", "auth", "system"]
// passthrough_args = ["--skip-tests", "--verbose"]
```

## Argument Processing Patterns

### Context-Based Argument Access

Arguments are processed through the CommandContext:

```typescript
interface CommandContext {
  args: string[];
  flags: Record<string, any>;
  workingDir: string;
  config: any;
  runtime: RuntimeAdapter;
}
```

### Argument Parsing in Action Handlers

From the SPARC command implementation:

```typescript
export async function sparcAction(ctx: CommandContext): Promise<void> {
  const subcommand = ctx.args[0];

  // Default behavior when no subcommand
  if (!subcommand || (subcommand && !['modes', 'info', 'run', 'tdd', 'workflow'].includes(subcommand))) {
    await runFullSparcDevelopment(ctx);
    return;
  }

  switch (subcommand) {
    case "run":
      await runSparcMode(ctx);
      break;
    // ... other cases
  }
}

async function runSparcMode(ctx: CommandContext): Promise<void> {
  const modeSlug = ctx.args[1];
  const taskDescription = ctx.args.slice(2).join(" ");

  if (!modeSlug || !taskDescription) {
    error("Usage: sparc run <mode-slug> <task-description>");
    return;
  }

  // Process flags
  const options = {
    dryRun: ctx.flags.dryRun || ctx.flags["dry-run"],
    namespace: ctx.flags.namespace,
    verbose: ctx.flags.verbose,
    config: ctx.flags.config
  };
}
```

### Complex Flag Processing

From the full SPARC development system:

```typescript
async function runFullSparcDevelopment(ctx: CommandContext): Promise<void> {
  const objective = ctx.args.join(" ").trim();
  
  const options = {
    projectName: ctx.flags.project as string || "sparc-project",
    readmePath: ctx.flags.readme as string || "README.md",
    verbose: ctx.flags.verbose as boolean || false,
    dryRun: ctx.flags['dry-run'] as boolean || false,
    skipResearch: ctx.flags['skip-research'] as boolean || false,
    skipTests: ctx.flags['skip-tests'] as boolean || false,
    developmentMode: ctx.flags.mode as string || "full",
    testCoverage: ctx.flags.coverage as number || 100,
    parallel: ctx.flags.parallel !== false,
    commitFrequency: ctx.flags['commit-freq'] as string || "phase",
    researchDepth: ctx.flags['research-depth'] as string || "standard",
    outputFormat: ctx.flags.output as string || "text",
  };

  if (!validateSparcOptions(options)) {
    return;
  }
}
```

## Validation Patterns

### Option Validation Functions

Structured validation with clear error reporting:

```typescript
function validateSparcOptions(options: any): boolean {
  // Validate development mode
  const validModes = ["full", "backend-only", "frontend-only", "api-only"];
  if (!validModes.includes(options.developmentMode)) {
    error(`Invalid development mode: ${options.developmentMode}`);
    console.log(`Valid modes: ${validModes.join(", ")}`);
    return false;
  }

  // Validate commit frequency
  const validCommitFreqs = ["phase", "feature", "manual"];
  if (!validCommitFreqs.includes(options.commitFrequency)) {
    error(`Invalid commit frequency: ${options.commitFrequency}`);
    console.log(`Valid frequencies: ${validCommitFreqs.join(", ")}`);
    return false;
  }

  // Validate coverage target
  if (options.testCoverage < 0 || options.testCoverage > 100) {
    error(`Invalid coverage target: ${options.testCoverage} (must be 0-100)`);
    return false;
  }

  return true;
}
```

### Required Argument Checking

Pattern for validating required arguments:

```typescript
async function showModeInfo(ctx: CommandContext): Promise<void> {
  const modeSlug = ctx.args[1];
  if (!modeSlug) {
    error("Usage: sparc info <mode-slug>");
    return;
  }

  // Continue with processing...
}

async function exportSession(sessionId: string, outputFile: string, options: any): Promise<void> {
  // Arguments are already validated by the command structure
  // Additional validation can be performed here
  if (!sessionId || !outputFile) {
    throw new Error("Session ID and output file are required");
  }
}
```

## Type Coercion and Conversion

### Automatic Type Handling

The system handles type coercion for common types:

```typescript
// Number conversion with defaults
.option('--timeout <seconds:number>', 'Startup timeout in seconds', { default: 60 })
.option('-p, --port <port:number>', 'MCP server port', { default: 3000 })

// Boolean flags
.option('-v, --verbose', 'Enable verbose logging')
.option('--force', 'Force start even if already running')

// String arrays (comma-separated)
.option('-t, --tags <tags:string>', 'Comma-separated tags')

// Processing in handler:
const tags = options.tags ? options.tags.split(',').map((t: string) => t.trim()) : [];
```

### Custom Value Processing

For complex value transformations:

```typescript
interface StartOptions {
  daemon?: boolean;
  port?: number;
  mcpTransport?: string;
  ui?: boolean;
  verbose?: boolean;
  autoStart?: boolean;
  config?: string;
  force?: boolean;
  healthCheck?: boolean;
  timeout?: number;
}

.action(async (options: StartOptions) => {
  // Type-safe access to parsed options
  const processOptions = {
    port: options.port || 3000,
    transport: options.mcpTransport || 'stdio',
    timeout: (options.timeout || 30) * 1000, // Convert to milliseconds
    daemon: Boolean(options.daemon),
    ui: Boolean(options.ui)
  };
})
```

## Rust Validation Patterns

### Comprehensive Validation Example

```rust
use clap::Parser;
use anyhow::{Result, bail};
use std::path::PathBuf;

#[derive(Parser)]
pub struct SparcOptions {
    /// Project name
    #[clap(long, default_value = "sparc-project")]
    pub project: String,

    /// README file path
    #[clap(long, default_value = "README.md")]
    pub readme: PathBuf,

    /// Development mode
    #[clap(
        long,
        default_value = "full",
        possible_values = &["full", "backend-only", "frontend-only", "api-only"]
    )]
    pub mode: String,

    /// Test coverage percentage
    #[clap(long, default_value = "100", value_parser = validate_coverage)]
    pub coverage: u8,

    /// Commit frequency
    #[clap(
        long,
        default_value = "phase",
        possible_values = &["phase", "feature", "manual"]
    )]
    pub commit_freq: String,

    /// Skip research phase
    #[clap(long)]
    pub skip_research: bool,

    /// Skip test generation
    #[clap(long)]
    pub skip_tests: bool,

    /// Enable parallel execution
    #[clap(long)]
    pub parallel: bool,
}

// Custom validator for coverage
fn validate_coverage(s: &str) -> Result<u8, String> {
    let coverage: u8 = s.parse()
        .map_err(|_| "Coverage must be a number".to_string())?;
    
    if coverage > 100 {
        Err("Coverage cannot exceed 100%".to_string())
    } else {
        Ok(coverage)
    }
}

impl SparcOptions {
    pub fn validate(&self) -> Result<()> {
        // Additional validation beyond clap's built-in
        if self.skip_tests && self.coverage > 0 {
            bail!("Cannot specify coverage when skipping tests");
        }

        if self.mode == "api-only" && self.skip_research {
            bail!("API-only mode requires research phase for endpoint discovery");
        }

        if !self.readme.exists() && self.readme != PathBuf::from("README.md") {
            bail!("Specified README file does not exist: {:?}", self.readme);
        }

        Ok(())
    }
}

// Usage in command handler
pub async fn handle_sparc_command(opts: SparcOptions) -> Result<()> {
    // Validate options
    opts.validate()?;
    
    // Process command
    println!("Starting SPARC development with:");
    println!("  Project: {}", opts.project);
    println!("  Mode: {}", opts.mode);
    println!("  Coverage: {}%", opts.coverage);
    println!("  Parallel: {}", opts.parallel);
    
    Ok(())
}
```

### Type Conversions and Custom Parsers

```rust
use chrono::{DateTime, Local};
use std::net::IpAddr;
use url::Url;

#[derive(Parser)]
pub struct AdvancedCli {
    /// IP address with automatic parsing
    #[clap(long, value_parser)]
    pub bind: IpAddr,

    /// URL with validation
    #[clap(long, value_parser = parse_url)]
    pub endpoint: Url,

    /// Duration in human-readable format
    #[clap(long, value_parser = parse_duration)]
    pub timeout: std::time::Duration,

    /// Date/time parsing
    #[clap(long, value_parser = parse_datetime)]
    pub since: Option<DateTime<Local>>,

    /// Custom type with FromStr
    #[clap(long)]
    pub log_level: LogLevel,
}

// Custom parsers
fn parse_url(s: &str) -> Result<Url, String> {
    Url::parse(s).map_err(|e| format!("Invalid URL: {}", e))
}

fn parse_duration(s: &str) -> Result<std::time::Duration, String> {
    humantime::parse_duration(s)
        .map_err(|e| format!("Invalid duration: {}", e))
}

fn parse_datetime(s: &str) -> Result<DateTime<Local>, String> {
    DateTime::parse_from_rfc3339(s)
        .map(|dt| dt.with_timezone(&Local))
        .or_else(|_| {
            // Try other formats
            chrono::NaiveDateTime::parse_from_str(s, "%Y-%m-%d %H:%M:%S")
                .map(|ndt| Local.from_local_datetime(&ndt).unwrap())
        })
        .map_err(|e| format!("Invalid datetime: {}", e))
}

// Custom type with FromStr implementation
#[derive(Debug, Clone)]
pub enum LogLevel {
    Debug,
    Info,
    Warn,
    Error,
}

impl std::str::FromStr for LogLevel {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "debug" => Ok(LogLevel::Debug),
            "info" => Ok(LogLevel::Info),
            "warn" | "warning" => Ok(LogLevel::Warn),
            "error" => Ok(LogLevel::Error),
            _ => Err(format!("Invalid log level: {}", s)),
        }
    }
}
```

## Interactive Argument Collection

### Prompt-Based Input

When arguments are missing, the system can prompt for input:

```typescript
async function saveSession(name: string | undefined, options: any): Promise<void> {
  if (!name) {
    if (options.auto) {
      name = `session-${new Date().toISOString().split('T')[0]}-${Date.now().toString().slice(-4)}`;
    } else {
      name = await Input.prompt({
        message: 'Enter session name:',
        default: `session-${new Date().toISOString().split('T')[0]}`,
      });
    }
  }
}
```

### Confirmation Prompts

For destructive operations:

```typescript
async function deleteSession(sessionId: string, options: any): Promise<void> {
  const session = await loadSession(sessionId);
  
  if (!session) {
    console.error(colors.red(`Session '${sessionId}' not found`));
    return;
  }

  // Confirmation unless --force flag is used
  if (!options.force) {
    console.log(`${colors.white('Session:')} ${session.name}`);
    console.log(`${colors.white('Created:')} ${session.createdAt.toLocaleString()}`);
    
    const confirmed = await Confirm.prompt({
      message: 'Are you sure you want to delete this session?',
      default: false,
    });

    if (!confirmed) {
      console.log(colors.gray('Delete cancelled'));
      return;
    }
  }
}
```

## Error Handling for Arguments

### Standard Error Patterns

```typescript
class CLIError extends Error implements CLIError {
  constructor(
    message: string,
    public code: string = 'CLI_ERROR',
    public exitCode?: number,
    public showUsage?: boolean
  ) {
    super(message);
    this.name = 'CLIError';
    if (exitCode !== undefined) this.exitCode = exitCode;
    if (showUsage !== undefined) this.showUsage = showUsage;
  }
}

// Usage in argument validation:
if (!handler) {
  throw new CLIError(
    `Unknown command: ${commandName}`,
    'UNKNOWN_COMMAND',
    1,
    true  // Show usage information
  );
}
```

## Implementation Guidelines

### Best Practices for Argument Design

1. **Consistent Naming**: Use full words for long options (`--verbose`, not `--verb`)
2. **Logical Grouping**: Group related options together in command definitions
3. **Sensible Defaults**: Provide reasonable defaults for optional parameters
4. **Type Safety**: Use TypeScript interfaces for option structures
5. **Validation Early**: Validate arguments before starting expensive operations
6. **Helpful Errors**: Provide specific error messages with valid option lists

### Flag Naming Conventions

- Single letter flags for common options (`-v` for verbose)
- Double-dash for full words (`--verbose`)
- Kebab-case for multi-word flags (`--dry-run`, `--skip-tests`)
- Boolean flags should not require values
- Value flags should use angle brackets in help (`<port>`)

This argument parsing system provides a robust foundation for handling complex command-line interfaces with proper validation and user-friendly error reporting.