# Help Generation and Documentation Systems

## Overview

This document details the help generation patterns from claude-code-flow, showing how dynamic help text is created, formatted, and presented to users.

## Core Help Generation Architecture

### Unified Help System

The UnifiedCommandRegistry provides centralized help generation:

```typescript
export class UnifiedCommandRegistry {
  /**
   * Get help text for a command
   */
  getHelp(commandName?: string): string {
    if (commandName) {
      return this.getCommandHelp(commandName);
    }
    
    return this.getGlobalHelp();
  }

  /**
   * Get help for a specific command
   */
  private getCommandHelp(commandName: string): string {
    const handler = this.commands.get(commandName);
    
    if (!handler) {
      return `Unknown command: ${commandName}`;
    }

    let help = `${chalk.cyan(commandName)} - ${handler.description}\n\n`;

    // Add usage section
    help += `${chalk.yellow('USAGE:')}\n`;
    help += `  claude-flow ${commandName}`;
    
    if (handler.options && handler.options.length > 0) {
      help += ' [options]';
    }
    
    if (handler.subcommands && Object.keys(handler.subcommands).length > 0) {
      help += ' <subcommand>';
    }
    
    help += '\n\n';

    // Add options section
    if (handler.options && handler.options.length > 0) {
      help += `${chalk.yellow('OPTIONS:')}\n`;
      for (const option of handler.options) {
        const aliases = option.aliases ? option.aliases.join(', ') : '';
        const flags = aliases ? `${option.flag}, ${aliases}` : option.flag;
        help += `  ${chalk.green(flags.padEnd(20))} ${option.description}\n`;
      }
      help += '\n';
    }

    // Add subcommands section
    if (handler.subcommands && Object.keys(handler.subcommands).length > 0) {
      help += `${chalk.yellow('SUBCOMMANDS:')}\n`;
      for (const [name, subHandler] of Object.entries(handler.subcommands)) {
        help += `  ${chalk.green(name.padEnd(20))} ${subHandler.description}\n`;
      }
      help += '\n';
    }

    // Add examples section
    if (handler.examples && handler.examples.length > 0) {
      help += `${chalk.yellow('EXAMPLES:')}\n`;
      for (const example of handler.examples) {
        help += `  ${chalk.gray(example)}\n`;
      }
      help += '\n';
    }

    return help;
  }
}
```

### Global Help Overview

System-wide help that lists all available commands:

```typescript
private getGlobalHelp(): string {
  let help = `${chalk.cyan('Claude-Flow')} - Advanced AI Agent Orchestration System\n\n`;
  
  help += `${chalk.yellow('AVAILABLE COMMANDS:')}\n`;
  
  // Group commands by category (can be enhanced with command metadata)
  const sortedCommands = Array.from(this.commands.entries()).sort(([a], [b]) => a.localeCompare(b));
  
  for (const [name, handler] of sortedCommands) {
    help += `  ${chalk.green(name.padEnd(20))} ${handler.description}\n`;
  }
  
  help += '\n';
  help += `${chalk.yellow('USAGE:')}\n`;
  help += `  claude-flow <command> [options]\n`;
  help += `  claude-flow help <command>  # Get help for a specific command\n\n`;
  
  return help;
}
```

## Remote CLI Help System

### Lightweight Help for Remote Execution

The remote CLI provides a simplified help system for quick reference:

```typescript
function printHelp() {
  console.log(`
🧠 Claude-Flow v${VERSION} - Advanced AI Agent Orchestration System

USAGE:
  claude-flow [COMMAND] [OPTIONS]

COMMANDS:
  init                  Initialize Claude Code integration files
  start                 Start the orchestration system
  agent                 Manage agents (spawn, list, terminate, info)
  task                  Manage tasks (create, list, status, cancel, workflow)
  memory               Manage memory (query, export, import, stats, cleanup)
  mcp                  Manage MCP server (status, tools, start, stop)
  config               Manage configuration (show, get, set, init, validate)
  status               Show system status
  monitor              Monitor system in real-time
  session              Manage terminal sessions
  workflow             Execute workflow files
  claude               Spawn Claude instances with specific configurations
  version              Show version information
  help                 Show this help message

OPTIONS:
  -c, --config <path>   Path to configuration file
  -v, --verbose         Enable verbose logging
  --help                Show help for any command

EXAMPLES:
  claude-flow init                    # Initialize Claude Code integration
  claude-flow start                   # Start orchestration system
  claude-flow agent spawn researcher  # Spawn a research agent
  claude-flow task create research "Analyze authentication patterns"
  claude-flow memory store key "value"
  claude-flow status                  # Check system status

For more info: https://github.com/ruvnet/claude-code-flow
`);
}
```

## Command-Specific Help Patterns

### Complex Command Help - SPARC System

The SPARC command provides contextual help with examples and methodology explanation:

```typescript
async function showSparcHelp(): Promise<void> {
  console.log(`${cyan("SPARC")} - ${green("Specification, Pseudocode, Architecture, Refinement, Completion")}`);
  console.log();
  console.log("SPARC development methodology with TDD and multi-agent coordination.");
  console.log();
  console.log(blue("Commands:"));
  console.log("  modes                    List all available SPARC modes");
  console.log("  info <mode>              Show detailed information about a mode");
  console.log("  run <mode> <task>        Execute a task using a specific SPARC mode");
  console.log("  tdd <task>               Run full TDD workflow using SPARC methodology");
  console.log("  workflow <file>          Execute a custom SPARC workflow from JSON file");
  console.log();
  console.log(blue("Common Modes:"));
  console.log("  spec-pseudocode          Create specifications and pseudocode");
  console.log("  architect                Design system architecture");
  console.log("  code                     Implement code solutions");
  console.log("  tdd                      Test-driven development");
  console.log("  debug                    Debug and troubleshoot issues");
  console.log("  security-review          Security analysis and review");
  console.log("  docs-writer              Documentation creation");
  console.log("  integration              System integration and testing");
  console.log();
  console.log(blue("Options:"));
  console.log("  --namespace <ns>         Memory namespace for this session");
  console.log("  --no-permissions         Skip permission prompts");
  console.log("  --config <file>          MCP configuration file");
  console.log("  --verbose               Enable verbose output");
  console.log("  --dry-run               Preview what would be executed");
  console.log("  --sequential            Wait between workflow steps (default: true)");
  console.log();
  console.log(blue("Examples:"));
  console.log(`  ${yellow("claude-flow sparc modes")}                              # List all modes`);
  console.log(`  ${yellow("claude-flow sparc run code")} "implement user auth"      # Run specific mode`);
  console.log(`  ${yellow("claude-flow sparc tdd")} "payment processing system"    # Full TDD workflow`);
  console.log(`  ${yellow("claude-flow sparc workflow")} project-workflow.json     # Custom workflow`);
  console.log();
  console.log(blue("TDD Workflow:"));
  console.log("  1. Specification - Define requirements and create pseudocode");
  console.log("  2. Red Phase - Write failing tests");
  console.log("  3. Green Phase - Implement minimal code to pass tests");
  console.log("  4. Refactor Phase - Optimize and clean up code");
  console.log("  5. Integration - Verify complete solution");
  console.log();
  console.log("For more information: https://github.com/ruvnet/claude-code-flow/docs/sparc.md");
}
```

### Interactive Help Mode

Commands can provide interactive help based on current state:

```typescript
// Default action for session command - shows help when no subcommand
export const sessionCommand = new Command()
  .description('Manage Claude-Flow sessions')
  .action(() => {
    sessionCommand.showHelp();  // Automatically show help
  })
```

## Dynamic Help Generation

### Context-Aware Help

Help text can be generated based on system state and available options:

```typescript
async function listSparcModes(ctx: CommandContext): Promise<void> {
  try {
    const config = await loadSparcConfig();
    const verbose = ctx.flags.verbose as boolean;

    success("Available SPARC Modes:");
    console.log();

    for (const mode of config.customModes) {
      console.log(`${cyan("•")} ${green(mode.name)} ${blue(`(${mode.slug})`)}`);
      if (verbose) {
        console.log(`  ${mode.roleDefinition}`);
        console.log(`  Tools: ${mode.groups.join(", ")}`);
        console.log();
      }
    }

    if (!verbose) {
      console.log();
      info("Use --verbose for detailed descriptions");
    }
  } catch (err) {
    error(`Failed to list SPARC modes: ${err instanceof Error ? err.message : String(err)}`);
  }
}
```

### Detailed Mode Information

Individual command information with comprehensive details:

```typescript
async function showModeInfo(ctx: CommandContext): Promise<void> {
  const modeSlug = ctx.args[1];
  if (!modeSlug) {
    error("Usage: sparc info <mode-slug>");
    return;
  }

  try {
    const config = await loadSparcConfig();
    const mode = config.customModes.find(m => m.slug === modeSlug);

    if (!mode) {
      error(`Mode not found: ${modeSlug}`);
      console.log("Available modes:");
      for (const m of config.customModes) {
        console.log(`  ${m.slug} - ${m.name}`);
      }
      return;
    }

    success(`SPARC Mode: ${mode.name}`);
    console.log();
    console.log(blue("Role Definition:"));
    console.log(mode.roleDefinition);
    console.log();
    console.log(blue("Custom Instructions:"));
    console.log(mode.customInstructions);
    console.log();
    console.log(blue("Tool Groups:"));
    console.log(mode.groups.join(", "));
    console.log();
    console.log(blue("Source:"));
    console.log(mode.source);

  } catch (err) {
    error(`Failed to show mode info: ${err instanceof Error ? err.message : String(err)}`);
  }
}
```

## Color and Formatting Patterns

### Standardized Color Scheme

The system uses consistent colors for different types of information:

```typescript
const colors = {
  red: (text: string) => `\x1b[31m${text}\x1b[0m`,      // Errors
  green: (text: string) => `\x1b[32m${text}\x1b[0m`,    // Success
  yellow: (text: string) => `\x1b[33m${text}\x1b[0m`,   // Warnings
  blue: (text: string) => `\x1b[34m${text}\x1b[0m`,     // Info
  gray: (text: string) => `\x1b[90m${text}\x1b[0m`,     // Secondary
  bold: (text: string) => `\x1b[1m${text}\x1b[0m`,      // Emphasis
};

// Usage in help text
console.log(colors.cyan("SPARC"));
console.log(colors.green("Specification, Pseudocode, Architecture, Refinement, Completion"));
console.log(colors.blue("Commands:"));
console.log(colors.yellow("Examples:"));
```

### Consistent Formatting Utilities

Standardized formatting for common CLI elements:

```typescript
export const format = {
  success: (message: string): string => chalk.green(`✓ ${message}`),
  error: (message: string): string => chalk.red(`✗ ${message}`),
  warning: (message: string): string => chalk.yellow(`⚠ ${message}`),
  info: (message: string): string => chalk.blue(`ℹ ${message}`),
  command: (cmd: string): string => chalk.cyan(cmd),
  path: (path: string): string => chalk.magenta(path),
  value: (value: string | number): string => chalk.yellow(String(value)),
  header: (text: string): string => chalk.bold.underline(text),
  subheader: (text: string): string => chalk.bold(text),
  dim: (text: string): string => chalk.gray(text)
};
```

## Dry Run and Preview Modes

### Configuration Preview Help

Commands can show what they would do without executing:

```typescript
function showSparcDryRun(objective: string, options: any): void {
  warning("🚀 DRY RUN - SPARC Configuration");
  console.log("═".repeat(60));
  console.log();
  console.log(`📋 Objective: ${objective}`);
  console.log(`🏗️  Project Name: ${options.projectName}`);
  console.log(`📄 README Path: ${options.readmePath}`);
  console.log();
  console.log("🛠️  Configuration:");
  console.log(`  • Development Mode: ${options.developmentMode}`);
  console.log(`  • Test Coverage Target: ${options.testCoverage}%`);
  console.log(`  • Research Depth: ${options.researchDepth}`);
  console.log(`  • Parallel Execution: ${options.parallel ? "✅" : "❌"}`);
  console.log(`  • Skip Research: ${options.skipResearch ? "✅" : "❌"}`);
  console.log(`  • Skip Tests: ${options.skipTests ? "✅" : "❌"}`);
  console.log(`  • Commit Frequency: ${options.commitFrequency}`);
  console.log(`  • Output Format: ${options.outputFormat}`);
  console.log();
  console.log("📋 Phases to Execute:");
  if (!options.skipResearch) {
    console.log("  1. Research & Discovery");
  }
  console.log("  2. Specification");
  console.log("  3. Pseudocode");
  console.log("  4. Architecture");
  console.log("  5. Refinement (TDD)");
  console.log("  6. Completion");
  console.log();
  console.log("═".repeat(60));
  console.log("⚠️  This is a dry run. No code will be generated.");
}
```

### Workflow Preview

Show workflow steps before execution:

```typescript
if (options.dryRun || options["dry-run"]) {
  warning("DRY RUN - TDD Workflow:");
  for (const step of workflow) {
    console.log(`${cyan(step.phase)}: ${step.mode} - ${step.description}`);
  }
  return;
}
```

## Error Help Integration

### Usage Information in Errors

Errors can automatically include relevant help information:

```typescript
if (!modeSlug || !taskDescription) {
  error("Usage: sparc run <mode-slug> <task-description>");
  console.log();
  console.log("Available modes:");
  const config = await loadSparcConfig();
  for (const mode of config.customModes) {
    console.log(`  ${mode.slug.padEnd(20)} ${mode.name}`);
  }
  return;
}
```

### Command Suggestion System

When commands are not found, suggest similar commands:

```typescript
if (!handler) {
  throw new CLIError(
    `Unknown command: ${commandName}`,
    'UNKNOWN_COMMAND',
    1,
    true  // showUsage flag
  );
}

// In error handler:
if (error.showUsage) {
  console.log();
  console.log(this.getHelp());
  
  // Could add command suggestions here:
  const suggestions = this.getSimilarCommands(commandName);
  if (suggestions.length > 0) {
    console.log(`\nDid you mean: ${suggestions.join(', ')}?`);
  }
}
```

## Implementation Guidelines

### Help Design Principles

1. **Consistent Structure**: All help text should follow the same format (Usage, Options, Subcommands, Examples)
2. **Progressive Disclosure**: Basic help by default, detailed help with flags like `--verbose`
3. **Contextual Information**: Show relevant information based on current system state
4. **Clear Examples**: Always provide practical examples that users can copy and use
5. **Visual Hierarchy**: Use colors and formatting to create clear information hierarchy

### Best Practices

- Use standardized color schemes for consistency
- Include both basic and advanced usage examples
- Provide dry-run modes for complex operations
- Make help text searchable and scannable
- Include links to external documentation when appropriate
- Generate help dynamically based on available features and configuration

This help generation system provides a comprehensive approach to user assistance, making CLI tools discoverable and easy to use.