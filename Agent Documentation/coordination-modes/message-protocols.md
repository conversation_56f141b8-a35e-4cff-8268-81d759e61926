# Message Protocols Implementation Guide

This document provides the messaging patterns from claude-code-flow for Rust implementation. All code snippets are preserved exactly from the original implementation.

## Inter-Agent Communication System

### Original TypeScript Messaging System

```
MessagingSystem (src/coordination/messaging.ts):
  - Reliable Delivery: Ensures message delivery with queuing.
  - Request-Response: Pattern with timeouts.
  - Broadcast: Capability for broadcasting messages.
  - Message Expiry: Handles message expiry and cleanup.
  - Handler Registration: System for registering message handlers.
  - Metrics: Tracks message metrics.
```

### Core Message Types

#### Task Assignment Message

```typescript
// From coordination manager
await manager.sendMessage('agent1', 'agent2', { type: 'status' });
```

#### Status Update Protocol

```json
{
  "type": "status_update",
  "agentId": "agent-123",
  "timestamp": 1234567890,
  "status": {
    "state": "executing",
    "taskId": "task-456",
    "progress": 0.65,
    "metrics": {
      "cpuUsage": 45,
      "memoryUsage": 72,
      "taskCount": 3
    }
  }
}
```

## Coordination Event Types

### Event-Driven Architecture

```
- Event-driven coordination using Node.js EventEmitter
- Multi-agent task distribution with capability-based selection
- Hierarchical task decomposition with dependency management
- Real-time monitoring with metrics collection
- Configurable execution modes (centralized, parallel, distributed)
```

### Task Completion Event

```typescript
// Emit completion event
this.emitSwarmEvent({
  type: 'task.completed',
  data: { task, result }
});
```

## Message Protocol Patterns

### Request-Response Pattern

```typescript
// Configuration for messaging
{
  "coordination": {
    "messageTimeout": 30000,
    "communication": {
      "protocol": "async",
      "bufferSize": 1000,
      "compression": true
    }
  }
}
```

### Broadcast Pattern

For mesh coordination:
- All agents receive state updates
- Consensus building messages
- Heartbeat broadcasts

## Mode-Specific Protocols

### Centralized Mode Messages

1. **TaskAssignment**
   ```json
   {
     "type": "task.assign",
     "from": "coordinator",
     "to": "agent-id",
     "task": {
       "id": "task-123",
       "type": "development",
       "priority": "high",
       "deadline": 1234567890
     }
   }
   ```

2. **ResourceRequest**
   ```json
   {
     "type": "resource.request",
     "from": "agent-id",
     "to": "coordinator",
     "resource": "database-lock",
     "priority": 2,
     "timeout": 60000
   }
   ```

### Distributed Mode Messages

1. **CoordinatorSync**
   ```json
   {
     "type": "coordinator.sync",
     "coordinatorId": "coord-1",
     "state": {
       "agents": ["agent-1", "agent-2"],
       "tasks": ["task-1", "task-2"],
       "version": 123
     }
   }
   ```

2. **LoadBalanceRequest**
   ```json
   {
     "type": "load.balance",
     "from": "coord-1",
     "to": "coord-2",
     "tasks": ["task-456", "task-789"],
     "reason": "overload"
   }
   ```

### Hierarchical Mode Messages

1. **DelegateTask**
   ```json
   {
     "type": "task.delegate",
     "from": "manager-1",
     "to": "worker-1",
     "task": {
       "id": "subtask-123",
       "parentId": "task-100",
       "depth": 2
     }
   }
   ```

2. **ReportProgress**
   ```json
   {
     "type": "progress.report",
     "from": "worker-1",
     "to": "manager-1",
     "taskId": "subtask-123",
     "progress": 0.75,
     "eta": 300000
   }
   ```

### Mesh Mode Messages

1. **ConsensusProposal**
   ```json
   {
     "type": "consensus.proposal",
     "from": "agent-1",
     "to": "all",
     "proposal": {
       "id": "prop-123",
       "action": "modify-architecture",
       "details": {}
     },
     "votingDeadline": 1234567890
   }
   ```

2. **PeerHeartbeat**
   ```json
   {
     "type": "peer.heartbeat",
     "from": "agent-1",
     "peers": ["agent-2", "agent-3"],
     "timestamp": 1234567890,
     "load": 0.6
   }
   ```

## Protocol Configuration

### Communication Settings

```json
{
  "coordination": {
    "maxRetries": 3,
    "retryDelay": 1000,
    "deadlockDetection": true,
    "resourceTimeout": 60000,
    "messageTimeout": 30000,
    "communication": {
      "protocol": "async",
      "bufferSize": 1000,
      "compression": true
    }
  }
}
```

## Message Delivery Guarantees

### Reliability Levels

1. **At-Most-Once**: Fire and forget (status updates)
2. **At-Least-Once**: Retry until acknowledged (task assignments)
3. **Exactly-Once**: Idempotent operations (resource allocation)

### Timeout Handling

```typescript
// Message timeout configuration
const messageConfig = {
  timeout: 30000,  // 30 seconds
  retries: 3,
  retryDelay: 1000
};
```

## Performance Considerations

### Message Batching

From optimization analysis:
```
optimization_tips = {
    "centralized": "Batch task assignments",
    "distributed": "Minimize coordinator sync",
    "hierarchical": "Reduce tree depth",
    "mesh": "Limit peer connections",
    "hybrid": "Cache mode decisions"
}
```

### Network Overhead

```
Performance Impact:
    - 15-20% CPU overhead for coordination
    - Network chattiness in distributed mode
    - Lock contention on shared state
```

## Rust Implementation Requirements

1. **Message Types**: Use serde for serialization
   ```rust
   #[derive(Serialize, Deserialize, Debug)]
   #[serde(tag = "type")]
   pub enum Message {
       TaskAssign { from: String, to: String, task: Task },
       StatusUpdate { agent_id: String, status: AgentStatus },
       ResourceRequest { from: String, resource: String, priority: u8 },
       // ... other variants
   }
   ```

2. **Channels**: Use tokio channels
   - mpsc for point-to-point
   - broadcast for mesh communication
   - oneshot for request-response

3. **Serialization**: JSON with serde_json
   - Support compression with flate2
   - Message versioning for compatibility

4. **Reliability**: Implement acknowledgment system
   - Use UUID for message tracking
   - Timeout with tokio::time::timeout
   - Retry logic with exponential backoff

5. **Metrics**: Track with prometheus
   - Message counts by type
   - Delivery latency
   - Failure rates
   - Queue depths