# System Connection Frameworks

## Connector Architecture Patterns

### 1. Abstract Factory Pattern for Connectors

#### Intent
Provide a unified interface for creating different types of external system connectors while maintaining consistency across implementations.

#### Structure
```typescript
interface ConnectorFactory {
  createRESTConnector(config: RESTConfig): RESTConnector;
  createGraphQLConnector(config: GraphQLConfig): GraphQLConnector;
  createDatabaseConnector(config: DatabaseConfig): DatabaseConnector;
  createMessageQueueConnector(config: MessageQueueConfig): MessageQueueConnector;
}

class StandardConnectorFactory implements ConnectorFactory {
  createRESTConnector(config: RESTConfig): RESTConnector {
    return new RESTConnector(config)
      .withCircuitBreaker(config.circuitBreaker)
      .withRateLimit(config.rateLimit)
      .withAuthentication(config.auth)
      .withMonitoring(config.monitoring);
  }
  
  createGraphQLConnector(config: GraphQLConfig): GraphQLConnector {
    return new GraphQLConnector(config)
      .withSchemaValidation(config.schema)
      .withQueryComplexityAnalysis(config.complexity)
      .withSubscriptionSupport(config.subscriptions);
  }
}
```

#### Benefits
- **Consistency**: All connectors follow the same creation pattern
- **Flexibility**: Easy to swap implementations without changing client code
- **Extensibility**: Simple to add new connector types
- **Testing**: Mock factories for unit testing

### 2. Strategy Pattern for Connection Management

#### Connection Pool Strategies
Different external systems require different connection management approaches based on their characteristics and usage patterns.

```typescript
interface ConnectionStrategy {
  acquire(): Promise<Connection>;
  release(connection: Connection): Promise<void>;
  validate(connection: Connection): Promise<boolean>;
  getPoolMetrics(): PoolMetrics;
}

class FixedPoolStrategy implements ConnectionStrategy {
  private readonly minConnections: number;
  private readonly maxConnections: number;
  private availableConnections: Connection[] = [];
  private activeConnections: Set<Connection> = new Set();
  
  constructor(config: FixedPoolConfig) {
    this.minConnections = config.min;
    this.maxConnections = config.max;
    this.initializePool();
  }
  
  async acquire(): Promise<Connection> {
    if (this.availableConnections.length > 0) {
      const connection = this.availableConnections.pop()!;
      this.activeConnections.add(connection);
      return connection;
    }
    
    if (this.activeConnections.size < this.maxConnections) {
      const connection = await this.createConnection();
      this.activeConnections.add(connection);
      return connection;
    }
    
    throw new Error('Connection pool exhausted');
  }
}

class DynamicPoolStrategy implements ConnectionStrategy {
  private readonly baseSize: number;
  private readonly maxSize: number;
  private readonly scaleUpThreshold: number;
  private readonly scaleDownThreshold: number;
  
  async acquire(): Promise<Connection> {
    const demand = this.calculateDemand();
    
    if (demand > this.scaleUpThreshold) {
      await this.scaleUp();
    } else if (demand < this.scaleDownThreshold) {
      await this.scaleDown();
    }
    
    return this.getAvailableConnection();
  }
}
```

### 3. Adapter Pattern for Protocol Translation

#### Protocol Adapters
Enable seamless integration with different protocols by providing a common interface while handling protocol-specific details internally.

```typescript
interface ProtocolAdapter<TRequest, TResponse> {
  connect(): Promise<void>;
  send(request: TRequest): Promise<TResponse>;
  disconnect(): Promise<void>;
  getConnectionStatus(): ConnectionStatus;
}

class HTTPAdapter implements ProtocolAdapter<HTTPRequest, HTTPResponse> {
  private httpClient: HTTPClient;
  private config: HTTPConfig;
  
  constructor(config: HTTPConfig) {
    this.config = config;
    this.httpClient = new HTTPClient({
      baseURL: config.baseURL,
      timeout: config.timeout,
      headers: config.defaultHeaders
    });
  }
  
  async send(request: HTTPRequest): Promise<HTTPResponse> {
    const startTime = Date.now();
    
    try {
      const response = await this.httpClient.request({
        method: request.method,
        url: request.url,
        data: request.body,
        headers: request.headers
      });
      
      this.recordMetrics('success', Date.now() - startTime);
      return this.transformResponse(response);
    } catch (error) {
      this.recordMetrics('error', Date.now() - startTime);
      throw this.transformError(error);
    }
  }
}

class WebSocketAdapter implements ProtocolAdapter<WSMessage, WSMessage> {
  private websocket: WebSocket;
  private messageQueue: Map<string, Promise<WSMessage>> = new Map();
  
  async send(request: WSMessage): Promise<WSMessage> {
    const messageId = this.generateMessageId();
    request.id = messageId;
    
    const responsePromise = new Promise<WSMessage>((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.messageQueue.delete(messageId);
        reject(new Error('WebSocket request timeout'));
      }, this.config.timeout);
      
      this.messageQueue.set(messageId, { resolve, reject, timeout });
    });
    
    this.websocket.send(JSON.stringify(request));
    return responsePromise;
  }
  
  private handleIncomingMessage(message: WSMessage): void {
    const pendingRequest = this.messageQueue.get(message.id);
    if (pendingRequest) {
      clearTimeout(pendingRequest.timeout);
      this.messageQueue.delete(message.id);
      pendingRequest.resolve(message);
    }
  }
}
```

### 4. Proxy Pattern for Enhanced Functionality

#### Connection Proxy
Add cross-cutting concerns like logging, monitoring, caching, and security without modifying the underlying connector implementations.

```typescript
class ConnectorProxy implements ExternalSystemConnector {
  private target: ExternalSystemConnector;
  private logger: Logger;
  private monitor: MetricsCollector;
  private cache: Cache;
  private security: SecurityValidator;
  
  constructor(
    target: ExternalSystemConnector,
    logger: Logger,
    monitor: MetricsCollector,
    cache: Cache,
    security: SecurityValidator
  ) {
    this.target = target;
    this.logger = logger;
    this.monitor = monitor;
    this.cache = cache;
    this.security = security;
  }
  
  async execute<T>(operation: Operation): Promise<T> {
    const operationId = this.generateOperationId();
    const startTime = Date.now();
    
    // Security validation
    await this.security.validateOperation(operation);
    
    // Logging
    this.logger.info('Operation started', { 
      operationId, 
      operation: operation.name 
    });
    
    try {
      // Check cache
      const cacheKey = this.generateCacheKey(operation);
      const cachedResult = await this.cache.get(cacheKey);
      if (cachedResult) {
        this.monitor.recordCacheHit(operation.name);
        return cachedResult;
      }
      
      // Execute operation
      const result = await this.target.execute<T>(operation);
      
      // Cache result
      if (operation.cacheable) {
        await this.cache.set(cacheKey, result, operation.ttl);
      }
      
      // Record metrics
      const duration = Date.now() - startTime;
      this.monitor.recordSuccess(operation.name, duration);
      
      this.logger.info('Operation completed', { 
        operationId, 
        duration 
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.monitor.recordError(operation.name, duration, error);
      
      this.logger.error('Operation failed', { 
        operationId, 
        duration, 
        error: error.message 
      });
      
      throw error;
    }
  }
}
```

### 5. Observer Pattern for Event-Driven Integration

#### Connection Event System
Enable reactive programming patterns by notifying interested parties about connection state changes and events.

```typescript
interface ConnectionObserver {
  onConnectionEstablished(connector: ExternalSystemConnector): void;
  onConnectionLost(connector: ExternalSystemConnector, error: Error): void;
  onOperationCompleted(connector: ExternalSystemConnector, operation: Operation, result: any): void;
  onOperationFailed(connector: ExternalSystemConnector, operation: Operation, error: Error): void;
}

class ObservableConnector implements ExternalSystemConnector {
  private observers: ConnectionObserver[] = [];
  private target: ExternalSystemConnector;
  
  addObserver(observer: ConnectionObserver): void {
    this.observers.push(observer);
  }
  
  removeObserver(observer: ConnectionObserver): void {
    const index = this.observers.indexOf(observer);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }
  
  async connect(): Promise<Connection> {
    try {
      const connection = await this.target.connect();
      this.notifyConnectionEstablished();
      return connection;
    } catch (error) {
      this.notifyConnectionLost(error);
      throw error;
    }
  }
  
  async execute<T>(operation: Operation): Promise<T> {
    try {
      const result = await this.target.execute<T>(operation);
      this.notifyOperationCompleted(operation, result);
      return result;
    } catch (error) {
      this.notifyOperationFailed(operation, error);
      throw error;
    }
  }
  
  private notifyConnectionEstablished(): void {
    this.observers.forEach(observer => {
      try {
        observer.onConnectionEstablished(this);
      } catch (error) {
        console.error('Observer notification failed:', error);
      }
    });
  }
}
```

## Connection Lifecycle Management

### Connection State Machine
```typescript
enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

class ConnectionStateMachine {
  private currentState: ConnectionState = ConnectionState.DISCONNECTED;
  private transitions: Map<ConnectionState, ConnectionState[]>;
  
  constructor() {
    this.transitions = new Map([
      [ConnectionState.DISCONNECTED, [ConnectionState.CONNECTING]],
      [ConnectionState.CONNECTING, [ConnectionState.CONNECTED, ConnectionState.ERROR]],
      [ConnectionState.CONNECTED, [ConnectionState.RECONNECTING, ConnectionState.ERROR, ConnectionState.DISCONNECTED]],
      [ConnectionState.RECONNECTING, [ConnectionState.CONNECTED, ConnectionState.ERROR]],
      [ConnectionState.ERROR, [ConnectionState.RECONNECTING, ConnectionState.DISCONNECTED]]
    ]);
  }
  
  canTransitionTo(newState: ConnectionState): boolean {
    const allowedTransitions = this.transitions.get(this.currentState);
    return allowedTransitions?.includes(newState) ?? false;
  }
  
  transitionTo(newState: ConnectionState): void {
    if (!this.canTransitionTo(newState)) {
      throw new Error(`Invalid state transition from ${this.currentState} to ${newState}`);
    }
    
    const previousState = this.currentState;
    this.currentState = newState;
    this.onStateChanged(previousState, newState);
  }
  
  private onStateChanged(from: ConnectionState, to: ConnectionState): void {
    // Trigger appropriate actions based on state change
    switch (to) {
      case ConnectionState.CONNECTING:
        this.handleConnecting();
        break;
      case ConnectionState.CONNECTED:
        this.handleConnected();
        break;
      case ConnectionState.ERROR:
        this.handleError();
        break;
      case ConnectionState.RECONNECTING:
        this.handleReconnecting();
        break;
    }
  }
}
```

### Health Check Framework
```typescript
interface HealthCheck {
  name: string;
  execute(): Promise<HealthCheckResult>;
  interval: number;
  timeout: number;
}

interface HealthCheckResult {
  healthy: boolean;
  message?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

class HealthCheckManager {
  private healthChecks: Map<string, HealthCheck> = new Map();
  private results: Map<string, HealthCheckResult> = new Map();
  private intervals: Map<string, NodeJS.Timer> = new Map();
  
  register(healthCheck: HealthCheck): void {
    this.healthChecks.set(healthCheck.name, healthCheck);
    this.startHealthCheck(healthCheck);
  }
  
  private startHealthCheck(healthCheck: HealthCheck): void {
    const interval = setInterval(async () => {
      try {
        const result = await Promise.race([
          healthCheck.execute(),
          this.createTimeoutPromise(healthCheck.timeout)
        ]);
        
        this.results.set(healthCheck.name, result);
        this.notifyHealthStatusChange(healthCheck.name, result);
      } catch (error) {
        const errorResult: HealthCheckResult = {
          healthy: false,
          message: error.message,
          timestamp: new Date()
        };
        
        this.results.set(healthCheck.name, errorResult);
        this.notifyHealthStatusChange(healthCheck.name, errorResult);
      }
    }, healthCheck.interval);
    
    this.intervals.set(healthCheck.name, interval);
  }
  
  getOverallHealth(): HealthCheckResult {
    const allResults = Array.from(this.results.values());
    const healthy = allResults.every(result => result.healthy);
    
    return {
      healthy,
      message: healthy ? 'All systems healthy' : 'Some systems unhealthy',
      metadata: {
        totalChecks: allResults.length,
        healthyChecks: allResults.filter(r => r.healthy).length,
        details: Object.fromEntries(this.results)
      },
      timestamp: new Date()
    };
  }
}