# Protocol Translation and Adaptation

## Protocol Adaptation Framework

### Universal Protocol Interface

#### Standardized Communication Contract
All protocol adapters implement a common interface that abstracts protocol-specific details while providing consistent behavior across different communication methods.

```typescript
interface ProtocolAdapter<TRequest, TResponse> {
  // Connection lifecycle
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  reconnect(): Promise<void>;
  
  // Communication
  send(request: TRequest): Promise<TResponse>;
  sendAsync(request: TRequest): Promise<void>;
  subscribe(callback: (message: TResponse) => void): Promise<void>;
  
  // Status and monitoring
  getStatus(): ConnectionStatus;
  getMetrics(): ProtocolMetrics;
  healthCheck(): Promise<boolean>;
}

interface ConnectionStatus {
  state: 'connected' | 'disconnected' | 'connecting' | 'error';
  lastConnected?: Date;
  lastError?: Error;
  reconnectAttempts: number;
  endpoint: string;
}

interface ProtocolMetrics {
  messagesSucceeded: number;
  messagesFailed: number;
  averageLatency: number;
  connectionUptime: number;
  bytesTransferred: number;
  lastActivity: Date;
}
```

### HTTP/REST Protocol Adapter

#### RESTful Service Integration
Provides comprehensive HTTP client functionality with advanced features for reliable REST API integration.

```typescript
class HTTPAdapter implements ProtocolAdapter<HTTPRequest, HTTPResponse> {
  private client: HTTPClient;
  private config: HTTPConfig;
  private connectionPool: ConnectionPool;
  private retryPolicy: RetryPolicy;
  
  constructor(config: HTTPConfig) {
    this.config = config;
    this.connectionPool = new ConnectionPool({
      maxConnections: config.pool?.max ?? 20,
      keepAlive: config.pool?.keepAlive ?? true,
      timeout: config.pool?.timeout ?? 30000
    });
    
    this.retryPolicy = new RetryPolicy({
      maxAttempts: config.retry?.maxAttempts ?? 3,
      backoff: config.retry?.backoff ?? 'exponential',
      retryCondition: this.shouldRetry.bind(this)
    });
    
    this.client = new HTTPClient({
      baseURL: config.baseURL,
      timeout: config.timeout ?? 30000,
      headers: this.buildDefaultHeaders(config),
      agent: this.connectionPool.getAgent()
    });
  }
  
  async send(request: HTTPRequest): Promise<HTTPResponse> {
    return this.retryPolicy.execute(async () => {
      const startTime = Date.now();
      
      try {
        // Pre-request processing
        const processedRequest = await this.preprocessRequest(request);
        
        // Execute HTTP request
        const response = await this.client.request({
          method: processedRequest.method,
          url: processedRequest.url,
          data: processedRequest.body,
          headers: processedRequest.headers,
          params: processedRequest.queryParams
        });
        
        // Post-response processing
        const processedResponse = await this.postprocessResponse(response);
        
        // Record metrics
        this.recordMetrics('success', Date.now() - startTime, response.status);
        
        return processedResponse;
      } catch (error) {
        this.recordMetrics('error', Date.now() - startTime, error.status);
        throw this.transformError(error);
      }
    });
  }
  
  private async preprocessRequest(request: HTTPRequest): Promise<HTTPRequest> {
    // Content negotiation
    if (!request.headers['Accept']) {
      request.headers['Accept'] = 'application/json';
    }
    
    // Authentication
    if (this.config.auth) {
      request.headers['Authorization'] = await this.getAuthHeader();
    }
    
    // Request compression
    if (this.config.compression && request.body) {
      request.body = await this.compressBody(request.body);
      request.headers['Content-Encoding'] = 'gzip';
    }
    
    // Request signing
    if (this.config.signing) {
      request.headers['X-Request-Signature'] = await this.signRequest(request);
    }
    
    return request;
  }
  
  private shouldRetry(error: HTTPError): boolean {
    // Retry on network errors
    if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
      return true;
    }
    
    // Retry on specific HTTP status codes
    const retryableStatuses = [429, 502, 503, 504];
    return retryableStatuses.includes(error.status);
  }
}
```

### WebSocket Protocol Adapter

#### Real-time Bidirectional Communication
Supports persistent connections for real-time messaging with automatic reconnection and message queuing.

```typescript
class WebSocketAdapter implements ProtocolAdapter<WSMessage, WSMessage> {
  private websocket: WebSocket;
  private config: WebSocketConfig;
  private messageQueue: Map<string, PendingMessage> = new Map();
  private subscriptions: Map<string, (message: WSMessage) => void> = new Map();
  private reconnectTimer: NodeJS.Timer;
  
  constructor(config: WebSocketConfig) {
    this.config = config;
    this.setupEventHandlers();
  }
  
  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.websocket = new WebSocket(this.config.url, {
          headers: this.config.headers,
          protocols: this.config.protocols
        });
        
        this.websocket.onopen = () => {
          this.onConnectionEstablished();
          resolve();
        };
        
        this.websocket.onerror = (error) => {
          this.onConnectionError(error);
          reject(error);
        };
        
        this.websocket.onclose = (event) => {
          this.onConnectionClosed(event);
        };
        
        this.websocket.onmessage = (event) => {
          this.onMessageReceived(event);
        };
        
      } catch (error) {
        reject(error);
      }
    });
  }
  
  async send(request: WSMessage): Promise<WSMessage> {
    if (this.websocket.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket connection not open');
    }
    
    const messageId = this.generateMessageId();
    request.id = messageId;
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.messageQueue.delete(messageId);
        reject(new Error('WebSocket request timeout'));
      }, this.config.timeout ?? 30000);
      
      this.messageQueue.set(messageId, {
        resolve,
        reject,
        timeout,
        timestamp: Date.now()
      });
      
      this.websocket.send(JSON.stringify(request));
    });
  }
  
  async subscribe(callback: (message: WSMessage) => void): Promise<void> {
    const subscriptionId = this.generateSubscriptionId();
    this.subscriptions.set(subscriptionId, callback);
    
    // Send subscription message if required by protocol
    if (this.config.requiresSubscription) {
      await this.send({
        type: 'subscribe',
        subscriptionId: subscriptionId
      });
    }
  }
  
  private onMessageReceived(event: MessageEvent): void {
    try {
      const message: WSMessage = JSON.parse(event.data);
      
      // Handle response to pending request
      if (message.id && this.messageQueue.has(message.id)) {
        const pending = this.messageQueue.get(message.id)!;
        clearTimeout(pending.timeout);
        this.messageQueue.delete(message.id);
        pending.resolve(message);
        return;
      }
      
      // Handle subscription message
      if (message.subscriptionId && this.subscriptions.has(message.subscriptionId)) {
        const callback = this.subscriptions.get(message.subscriptionId)!;
        callback(message);
        return;
      }
      
      // Handle broadcast message
      this.handleBroadcastMessage(message);
      
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }
  
  private onConnectionClosed(event: CloseEvent): void {
    this.status.state = 'disconnected';
    
    // Reject all pending messages
    this.messageQueue.forEach(pending => {
      clearTimeout(pending.timeout);
      pending.reject(new Error('Connection closed'));
    });
    this.messageQueue.clear();
    
    // Attempt reconnection if configured
    if (this.config.autoReconnect && !event.wasClean) {
      this.scheduleReconnection();
    }
  }
  
  private scheduleReconnection(): void {
    const delay = this.calculateReconnectDelay();
    this.reconnectTimer = setTimeout(() => {
      this.reconnect();
    }, delay);
  }
}
```

### gRPC Protocol Adapter

#### High-Performance RPC Communication
Provides type-safe, high-performance RPC communication with streaming support and automatic code generation.

```typescript
class GRPCAdapter implements ProtocolAdapter<GRPCRequest, GRPCResponse> {
  private client: grpc.Client;
  private config: GRPCConfig;
  private channelCredentials: grpc.ChannelCredentials;
  private callOptions: grpc.CallOptions;
  
  constructor(config: GRPCConfig) {
    this.config = config;
    this.setupCredentials();
    this.setupCallOptions();
    this.createClient();
  }
  
  async send(request: GRPCRequest): Promise<GRPCResponse> {
    const methodName = request.method;
    const serviceMethod = this.getServiceMethod(methodName);
    
    return new Promise((resolve, reject) => {
      const call = this.client[methodName](
        request.data,
        this.callOptions,
        (error: grpc.ServiceError, response: GRPCResponse) => {
          if (error) {
            reject(this.transformGRPCError(error));
          } else {
            resolve(response);
          }
        }
      );
      
      // Handle call metadata
      call.on('metadata', (metadata) => {
        this.handleMetadata(metadata);
      });
      
      // Handle call status
      call.on('status', (status) => {
        this.handleStatus(status);
      });
    });
  }
  
  async sendStream(request: GRPCRequest): Promise<AsyncIterator<GRPCResponse>> {
    const methodName = request.method;
    const call = this.client[methodName](request.data, this.callOptions);
    
    return {
      async *[Symbol.asyncIterator]() {
        for await (const response of call) {
          yield response;
        }
      }
    };
  }
  
  async sendClientStream(requests: AsyncIterator<GRPCRequest>): Promise<GRPCResponse> {
    const methodName = requests.method;
    const call = this.client[methodName](this.callOptions);
    
    // Send all requests
    for await (const request of requests) {
      call.write(request.data);
    }
    call.end();
    
    return new Promise((resolve, reject) => {
      call.on('error', reject);
      call.on('data', resolve);
    });
  }
  
  private setupCredentials(): void {
    if (this.config.tls) {
      if (this.config.tls.mutual) {
        // Mutual TLS
        this.channelCredentials = grpc.credentials.createSsl(
          this.config.tls.ca,
          this.config.tls.key,
          this.config.tls.cert
        );
      } else {
        // Server TLS only
        this.channelCredentials = grpc.credentials.createSsl(
          this.config.tls.ca
        );
      }
    } else {
      // Insecure connection
      this.channelCredentials = grpc.credentials.createInsecure();
    }
  }
  
  private transformGRPCError(error: grpc.ServiceError): Error {
    // Map gRPC status codes to standard errors
    switch (error.code) {
      case grpc.status.UNAUTHENTICATED:
        return new AuthenticationError(error.message);
      case grpc.status.PERMISSION_DENIED:
        return new AuthorizationError(error.message);
      case grpc.status.NOT_FOUND:
        return new NotFoundError(error.message);
      case grpc.status.DEADLINE_EXCEEDED:
        return new TimeoutError(error.message);
      default:
        return new Error(`gRPC Error: ${error.message} (${error.code})`);
    }
  }
}
```

### Message Queue Protocol Adapter

#### Asynchronous Message Processing
Supports various message queue protocols with reliable delivery, acknowledgments, and dead letter handling.

```typescript
class MessageQueueAdapter implements ProtocolAdapter<QueueMessage, QueueMessage> {
  private connection: amqp.Connection;
  private channel: amqp.Channel;
  private config: MessageQueueConfig;
  private consumers: Map<string, Consumer> = new Map();
  
  constructor(config: MessageQueueConfig) {
    this.config = config;
  }
  
  async connect(): Promise<void> {
    this.connection = await amqp.connect(this.config.url, {
      heartbeat: this.config.heartbeat ?? 60,
      connectionTimeout: this.config.connectionTimeout ?? 30000
    });
    
    this.channel = await this.connection.createChannel();
    
    // Setup error handling
    this.connection.on('error', (error) => {
      this.handleConnectionError(error);
    });
    
    this.channel.on('error', (error) => {
      this.handleChannelError(error);
    });
    
    // Setup dead letter exchange
    if (this.config.deadLetter) {
      await this.setupDeadLetterExchange();
    }
    
    // Prefetch configuration
    await this.channel.prefetch(this.config.prefetch ?? 10);
  }
  
  async send(request: QueueMessage): Promise<QueueMessage> {
    const queueName = request.queue;
    const exchange = request.exchange ?? '';
    const routingKey = request.routingKey ?? queueName;
    
    // Ensure queue exists
    await this.channel.assertQueue(queueName, {
      durable: this.config.durableQueues ?? true,
      arguments: this.getQueueArguments(queueName)
    });
    
    // Publish message
    const published = this.channel.publish(
      exchange,
      routingKey,
      Buffer.from(JSON.stringify(request.data)),
      {
        persistent: true,
        messageId: this.generateMessageId(),
        timestamp: Date.now(),
        headers: request.headers,
        replyTo: this.config.replyQueue,
        correlationId: request.correlationId
      }
    );
    
    if (!published) {
      throw new Error('Failed to publish message to queue');
    }
    
    // Wait for reply if synchronous
    if (request.expectReply) {
      return this.waitForReply(request.correlationId);
    }
    
    return { success: true };
  }
  
  async subscribe(callback: (message: QueueMessage) => void): Promise<void> {
    const queueName = this.config.consumeQueue;
    
    await this.channel.assertQueue(queueName, {
      durable: this.config.durableQueues ?? true
    });
    
    const consumer = await this.channel.consume(queueName, async (msg) => {
      if (!msg) return;
      
      try {
        const message: QueueMessage = {
          data: JSON.parse(msg.content.toString()),
          headers: msg.properties.headers,
          messageId: msg.properties.messageId,
          correlationId: msg.properties.correlationId,
          queue: queueName
        };
        
        // Process message
        await callback(message);
        
        // Acknowledge message
        this.channel.ack(msg);
        
      } catch (error) {
        console.error('Error processing message:', error);
        
        // Handle failed message
        if (this.shouldRequeue(error, msg)) {
          this.channel.nack(msg, false, true); // Requeue
        } else {
          this.channel.nack(msg, false, false); // Send to DLQ
        }
      }
    });
    
    this.consumers.set(queueName, consumer);
  }
  
  private async setupDeadLetterExchange(): Promise<void> {
    const dlxName = this.config.deadLetter.exchange;
    const dlqName = this.config.deadLetter.queue;
    
    // Create dead letter exchange
    await this.channel.assertExchange(dlxName, 'direct', { durable: true });
    
    // Create dead letter queue
    await this.channel.assertQueue(dlqName, { durable: true });
    
    // Bind dead letter queue to exchange
    await this.channel.bindQueue(dlqName, dlxName, '');
  }
  
  private getQueueArguments(queueName: string): any {
    const args: any = {};
    
    // Dead letter configuration
    if (this.config.deadLetter) {
      args['x-dead-letter-exchange'] = this.config.deadLetter.exchange;
      args['x-dead-letter-routing-key'] = queueName;
    }
    
    // Message TTL
    if (this.config.messageTTL) {
      args['x-message-ttl'] = this.config.messageTTL;
    }
    
    // Queue TTL
    if (this.config.queueTTL) {
      args['x-expires'] = this.config.queueTTL;
    }
    
    // Max length
    if (this.config.maxLength) {
      args['x-max-length'] = this.config.maxLength;
    }
    
    return args;
  }
}
```

## Protocol Translation Patterns

### Content Negotiation
Automatic format conversion based on client preferences and server capabilities.

### Schema Transformation
Bidirectional transformation between different data schemas and formats.

### Protocol Bridging
Seamless communication between different protocol types (HTTP to WebSocket, gRPC to REST).

### Error Mapping
Consistent error handling across different protocol error models.

### Security Translation
Protocol-specific security implementation while maintaining consistent security policies.