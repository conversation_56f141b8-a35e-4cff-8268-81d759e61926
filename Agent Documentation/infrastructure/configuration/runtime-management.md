# Runtime Configuration Management

## Hot Reload System

### Configuration Hot Reload Manager
```rust
use tokio::sync::{broadcast, RwLock, watch};
use std::sync::Arc;

pub struct HotReloadManager {
    config_manager: Arc<ConfigurationManager>,
    subscribers: Arc<RwLock<HashMap<String, Box<dyn ConfigSubscriber>>>>,
    change_sender: broadcast::Sender<ConfigurationChange>,
    change_receiver: broadcast::Receiver<ConfigurationChange>,
    reload_policies: HashMap<String, ReloadPolicy>,
    rollback_history: VecDeque<ConfigurationSnapshot>,
    max_history_size: usize,
}

#[derive(Debug, Clone)]
pub struct ConfigurationChange {
    pub change_id: String,
    pub timestamp: DateTime<Utc>,
    pub source: String,
    pub change_type: ChangeType,
    pub affected_keys: Vec<String>,
    pub old_values: HashMap<String, ConfigValue>,
    pub new_values: HashMap<String, ConfigValue>,
    pub requires_restart: bool,
}

#[derive(Debug, <PERSON>lone)]
pub enum ChangeType {
    Updated,
    Added,
    Removed,
    Reloaded,
}

#[derive(Debug, Clone)]
pub struct ReloadPolicy {
    pub hot_reloadable: bool,
    pub validation_required: bool,
    pub rollback_on_failure: bool,
    pub restart_required: bool,
    pub grace_period: Duration,
    pub max_retries: u32,
}

#[async_trait]
pub trait ConfigSubscriber: Send + Sync {
    async fn on_config_changed(&self, change: &ConfigurationChange) -> Result<()>;
    fn subscription_key(&self) -> &str;
    fn can_handle_hot_reload(&self) -> bool { true }
}

struct ConfigurationSnapshot {
    timestamp: DateTime<Utc>,
    configuration: HashMap<String, ConfigValue>,
    change_id: String,
}

impl HotReloadManager {
    pub fn new(config_manager: Arc<ConfigurationManager>) -> Self {
        let (change_sender, change_receiver) = broadcast::channel(1000);
        
        let mut policies = HashMap::new();
        Self::setup_default_policies(&mut policies);
        
        Self {
            config_manager,
            subscribers: Arc::new(RwLock::new(HashMap::new())),
            change_sender,
            change_receiver,
            reload_policies: policies,
            rollback_history: VecDeque::new(),
            max_history_size: 50,
        }
    }
    
    fn setup_default_policies(policies: &mut HashMap<String, ReloadPolicy>) {
        // Logging configuration - hot reloadable
        policies.insert("logging".to_string(), ReloadPolicy {
            hot_reloadable: true,
            validation_required: true,
            rollback_on_failure: true,
            restart_required: false,
            grace_period: Duration::from_secs(5),
            max_retries: 3,
        });
        
        // Agent configuration - partially hot reloadable
        policies.insert("agents".to_string(), ReloadPolicy {
            hot_reloadable: true,
            validation_required: true,
            rollback_on_failure: true,
            restart_required: false,
            grace_period: Duration::from_secs(10),
            max_retries: 2,
        });
        
        // Server configuration - requires restart
        policies.insert("server".to_string(), ReloadPolicy {
            hot_reloadable: false,
            validation_required: true,
            rollback_on_failure: false,
            restart_required: true,
            grace_period: Duration::from_secs(30),
            max_retries: 1,
        });
        
        // Database configuration - requires restart
        policies.insert("database".to_string(), ReloadPolicy {
            hot_reloadable: false,
            validation_required: true,
            rollback_on_failure: false,
            restart_required: true,
            grace_period: Duration::from_secs(30),
            max_retries: 1,
        });
        
        // Feature flags - hot reloadable
        policies.insert("features".to_string(), ReloadPolicy {
            hot_reloadable: true,
            validation_required: false,
            rollback_on_failure: true,
            restart_required: false,
            grace_period: Duration::from_secs(1),
            max_retries: 5,
        });
        
        // Monitoring configuration - hot reloadable
        policies.insert("monitoring".to_string(), ReloadPolicy {
            hot_reloadable: true,
            validation_required: true,
            rollback_on_failure: true,
            restart_required: false,
            grace_period: Duration::from_secs(5),
            max_retries: 3,
        });
    }
    
    pub async fn register_subscriber(&self, subscriber: Box<dyn ConfigSubscriber>) {
        let key = subscriber.subscription_key().to_string();
        self.subscribers.write().await.insert(key, subscriber);
    }
    
    pub async fn reload_configuration(&self, source: &str) -> Result<ConfigReloadResult> {
        let change_id = Uuid::new_v4().to_string();
        
        // Take snapshot before reload
        let old_config = self.take_configuration_snapshot(&change_id).await?;
        
        // Reload configuration
        self.config_manager.load_configuration().await?;
        
        // Compare configurations to detect changes
        let changes = self.detect_configuration_changes(&old_config.configuration).await?;
        
        if changes.is_empty() {
            return Ok(ConfigReloadResult {
                change_id,
                changes_applied: 0,
                requires_restart: false,
                rollback_performed: false,
                errors: Vec::new(),
            });
        }
        
        // Group changes by reload policy
        let change_groups = self.group_changes_by_policy(&changes);
        
        let mut reload_result = ConfigReloadResult {
            change_id: change_id.clone(),
            changes_applied: 0,
            requires_restart: false,
            rollback_performed: false,
            errors: Vec::new(),
        };
        
        // Process each group
        for (policy_key, group_changes) in change_groups {
            let policy = self.reload_policies.get(&policy_key)
                .cloned()
                .unwrap_or_else(|| self.get_default_policy());
                
            match self.apply_configuration_changes(
                &change_id,
                &group_changes,
                &policy,
                source,
            ).await {
                Ok(group_result) => {
                    reload_result.changes_applied += group_result.changes_applied;
                    reload_result.requires_restart |= group_result.requires_restart;
                }
                Err(e) => {
                    reload_result.errors.push(format!("Policy {}: {}", policy_key, e));
                    
                    if policy.rollback_on_failure {
                        log::warn!("Rolling back changes for policy {} due to error: {}", policy_key, e);
                        if let Err(rollback_err) = self.rollback_to_snapshot(&old_config).await {
                            reload_result.errors.push(format!("Rollback failed: {}", rollback_err));
                        } else {
                            reload_result.rollback_performed = true;
                        }
                    }
                }
            }
        }
        
        // Store snapshot if changes were successful
        if reload_result.errors.is_empty() {
            self.store_snapshot(old_config).await;
        }
        
        Ok(reload_result)
    }
    
    async fn detect_configuration_changes(
        &self,
        old_config: &HashMap<String, ConfigValue>,
    ) -> Result<Vec<ConfigurationChange>> {
        let current_config = self.get_current_configuration().await?;
        let mut changes = Vec::new();
        
        // Find updated and removed keys
        for (key, old_value) in old_config {
            match current_config.get(key) {
                Some(new_value) if new_value != old_value => {
                    changes.push(ConfigurationChange {
                        change_id: Uuid::new_v4().to_string(),
                        timestamp: Utc::now(),
                        source: "reload".to_string(),
                        change_type: ChangeType::Updated,
                        affected_keys: vec![key.clone()],
                        old_values: hashmap! { key.clone() => old_value.clone() },
                        new_values: hashmap! { key.clone() => new_value.clone() },
                        requires_restart: self.requires_restart_for_key(key),
                    });
                }
                None => {
                    changes.push(ConfigurationChange {
                        change_id: Uuid::new_v4().to_string(),
                        timestamp: Utc::now(),
                        source: "reload".to_string(),
                        change_type: ChangeType::Removed,
                        affected_keys: vec![key.clone()],
                        old_values: hashmap! { key.clone() => old_value.clone() },
                        new_values: HashMap::new(),
                        requires_restart: self.requires_restart_for_key(key),
                    });
                }
                _ => {}
            }
        }
        
        // Find added keys
        for (key, new_value) in &current_config {
            if !old_config.contains_key(key) {
                changes.push(ConfigurationChange {
                    change_id: Uuid::new_v4().to_string(),
                    timestamp: Utc::now(),
                    source: "reload".to_string(),
                    change_type: ChangeType::Added,
                    affected_keys: vec![key.clone()],
                    old_values: HashMap::new(),
                    new_values: hashmap! { key.clone() => new_value.clone() },
                    requires_restart: self.requires_restart_for_key(key),
                });
            }
        }
        
        Ok(changes)
    }
    
    async fn apply_configuration_changes(
        &self,
        change_id: &str,
        changes: &[ConfigurationChange],
        policy: &ReloadPolicy,
        source: &str,
    ) -> Result<GroupReloadResult> {
        let mut result = GroupReloadResult {
            changes_applied: 0,
            requires_restart: false,
        };
        
        // Check if hot reload is supported
        if !policy.hot_reloadable {
            result.requires_restart = true;
            return Ok(result);
        }
        
        // Validate changes if required
        if policy.validation_required {
            for change in changes {
                for (key, value) in &change.new_values {
                    self.config_manager.validate_partial(key, value).await?;
                }
            }
        }
        
        // Apply changes with retry logic
        for change in changes {
            let mut retries = 0;
            
            while retries <= policy.max_retries {
                match self.notify_subscribers(change).await {
                    Ok(_) => {
                        result.changes_applied += 1;
                        break;
                    }
                    Err(e) if retries < policy.max_retries => {
                        retries += 1;
                        log::warn!(
                            "Failed to apply change {} (attempt {}/{}): {}",
                            change.change_id,
                            retries,
                            policy.max_retries,
                            e
                        );
                        
                        tokio::time::sleep(Duration::from_millis(100 * retries as u64)).await;
                    }
                    Err(e) => {
                        return Err(anyhow!(
                            "Failed to apply change {} after {} retries: {}",
                            change.change_id,
                            policy.max_retries,
                            e
                        ));
                    }
                }
            }
        }
        
        // Wait for grace period if specified
        if policy.grace_period > Duration::ZERO {
            tokio::time::sleep(policy.grace_period).await;
        }
        
        Ok(result)
    }
    
    async fn notify_subscribers(&self, change: &ConfigurationChange) -> Result<()> {
        let subscribers = self.subscribers.read().await;
        
        for (key, subscriber) in subscribers.iter() {
            // Check if subscriber is interested in this change
            if self.subscriber_interested_in_change(key, change) {
                if let Err(e) = subscriber.on_config_changed(change).await {
                    log::error!("Subscriber {} failed to handle config change: {}", key, e);
                    return Err(e);
                }
            }
        }
        
        // Broadcast change to all listeners
        let _ = self.change_sender.send(change.clone());
        
        Ok(())
    }
    
    fn subscriber_interested_in_change(&self, subscriber_key: &str, change: &ConfigurationChange) -> bool {
        // Check if any of the affected keys match the subscriber's interest
        change.affected_keys.iter().any(|key| {
            key.starts_with(subscriber_key) || subscriber_key.starts_with(key)
        })
    }
    
    async fn rollback_to_snapshot(&self, snapshot: &ConfigurationSnapshot) -> Result<()> {
        log::info!("Rolling back configuration to snapshot {}", snapshot.change_id);
        
        // This would restore the configuration to the snapshot state
        // Implementation depends on the specific configuration manager
        
        // Notify subscribers of rollback
        let rollback_change = ConfigurationChange {
            change_id: format!("rollback-{}", snapshot.change_id),
            timestamp: Utc::now(),
            source: "rollback".to_string(),
            change_type: ChangeType::Reloaded,
            affected_keys: snapshot.configuration.keys().cloned().collect(),
            old_values: HashMap::new(), // Current values
            new_values: snapshot.configuration.clone(),
            requires_restart: false,
        };
        
        self.notify_subscribers(&rollback_change).await?;
        
        Ok(())
    }
    
    fn requires_restart_for_key(&self, key: &str) -> bool {
        for (policy_key, policy) in &self.reload_policies {
            if key.starts_with(policy_key) {
                return policy.restart_required;
            }
        }
        
        false // Default to not requiring restart
    }
    
    fn get_default_policy(&self) -> ReloadPolicy {
        ReloadPolicy {
            hot_reloadable: true,
            validation_required: true,
            rollback_on_failure: true,
            restart_required: false,
            grace_period: Duration::from_secs(5),
            max_retries: 3,
        }
    }
}

#[derive(Debug)]
pub struct ConfigReloadResult {
    pub change_id: String,
    pub changes_applied: usize,
    pub requires_restart: bool,
    pub rollback_performed: bool,
    pub errors: Vec<String>,
}

#[derive(Debug)]
struct GroupReloadResult {
    pub changes_applied: usize,
    pub requires_restart: bool,
}
```

## Feature Flag Management

### Dynamic Feature Flag System
```rust
use std::sync::atomic::{AtomicBool, Ordering};

pub struct FeatureFlagManager {
    flags: Arc<RwLock<HashMap<String, FeatureFlag>>>,
    subscribers: Arc<RwLock<HashMap<String, Box<dyn FeatureFlagSubscriber>>>>,
    evaluation_cache: Arc<RwLock<HashMap<String, CachedEvaluation>>>,
    cache_ttl: Duration,
    remote_provider: Option<Box<dyn RemoteFeatureFlagProvider>>,
}

#[derive(Debug, Clone)]
pub struct FeatureFlag {
    pub name: String,
    pub enabled: bool,
    pub conditions: Vec<FeatureCondition>,
    pub rollout_percentage: f64,
    pub user_segments: Vec<String>,
    pub environment_filter: Option<String>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, Clone)]
pub enum FeatureCondition {
    UserSegment(String),
    PercentageRollout(f64),
    UserProperty { key: String, value: String },
    TimeWindow { start: DateTime<Utc>, end: DateTime<Utc> },
    Environment(String),
    Custom(String), // Custom condition expression
}

#[derive(Debug, Clone)]
pub struct FeatureContext {
    pub user_id: Option<String>,
    pub environment: String,
    pub user_properties: HashMap<String, String>,
    pub timestamp: DateTime<Utc>,
}

struct CachedEvaluation {
    result: bool,
    cached_at: Instant,
    context_hash: u64,
}

#[async_trait]
pub trait FeatureFlagSubscriber: Send + Sync {
    async fn on_flag_changed(&self, flag_name: &str, old_value: bool, new_value: bool) -> Result<()>;
}

#[async_trait]
pub trait RemoteFeatureFlagProvider: Send + Sync {
    async fn fetch_flags(&self) -> Result<HashMap<String, FeatureFlag>>;
    async fn watch_for_changes(&self) -> Result<Box<dyn Stream<Item = FeatureFlagUpdate> + Send>>;
}

impl FeatureFlagManager {
    pub fn new() -> Self {
        Self {
            flags: Arc::new(RwLock::new(HashMap::new())),
            subscribers: Arc::new(RwLock::new(HashMap::new())),
            evaluation_cache: Arc::new(RwLock::new(HashMap::new())),
            cache_ttl: Duration::from_secs(60),
            remote_provider: None,
        }
    }
    
    pub fn with_remote_provider(mut self, provider: Box<dyn RemoteFeatureFlagProvider>) -> Self {
        self.remote_provider = Some(provider);
        self
    }
    
    pub async fn is_enabled(&self, flag_name: &str, context: &FeatureContext) -> bool {
        // Check cache first
        let cache_key = format!("{}:{}", flag_name, self.hash_context(context));
        
        {
            let cache = self.evaluation_cache.read().await;
            if let Some(cached) = cache.get(&cache_key) {
                if cached.cached_at.elapsed() < self.cache_ttl {
                    return cached.result;
                }
            }
        }
        
        // Evaluate flag
        let result = self.evaluate_flag(flag_name, context).await;
        
        // Update cache
        {
            let mut cache = self.evaluation_cache.write().await;
            cache.insert(cache_key, CachedEvaluation {
                result,
                cached_at: Instant::now(),
                context_hash: self.hash_context(context),
            });
        }
        
        result
    }
    
    async fn evaluate_flag(&self, flag_name: &str, context: &FeatureContext) -> bool {
        let flags = self.flags.read().await;
        
        let flag = match flags.get(flag_name) {
            Some(flag) => flag,
            None => {
                log::debug!("Feature flag '{}' not found, defaulting to false", flag_name);
                return false;
            }
        };
        
        // Check if flag is globally enabled
        if !flag.enabled {
            return false;
        }
        
        // Check environment filter
        if let Some(ref env_filter) = flag.environment_filter {
            if context.environment != *env_filter {
                return false;
            }
        }
        
        // Check time window
        if let (Some(start), Some(end)) = (&flag.start_time, &flag.end_time) {
            if context.timestamp < *start || context.timestamp > *end {
                return false;
            }
        }
        
        // Evaluate conditions
        for condition in &flag.conditions {
            if !self.evaluate_condition(condition, context, flag).await {
                return false;
            }
        }
        
        true
    }
    
    async fn evaluate_condition(
        &self,
        condition: &FeatureCondition,
        context: &FeatureContext,
        flag: &FeatureFlag,
    ) -> bool {
        match condition {
            FeatureCondition::UserSegment(segment) => {
                flag.user_segments.contains(segment)
            }
            FeatureCondition::PercentageRollout(percentage) => {
                if let Some(ref user_id) = context.user_id {
                    let hash = self.hash_user_id_for_flag(&flag.name, user_id);
                    (hash % 100.0) < *percentage
                } else {
                    // No user ID, fall back to random
                    rand::random::<f64>() * 100.0 < *percentage
                }
            }
            FeatureCondition::UserProperty { key, value } => {
                context.user_properties.get(key).map(|v| v == value).unwrap_or(false)
            }
            FeatureCondition::TimeWindow { start, end } => {
                context.timestamp >= *start && context.timestamp <= *end
            }
            FeatureCondition::Environment(env) => {
                context.environment == *env
            }
            FeatureCondition::Custom(expression) => {
                // Evaluate custom expression
                self.evaluate_custom_expression(expression, context).await
            }
        }
    }
    
    fn hash_user_id_for_flag(&self, flag_name: &str, user_id: &str) -> f64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        flag_name.hash(&mut hasher);
        user_id.hash(&mut hasher);
        
        let hash = hasher.finish();
        (hash % 10000) as f64 / 100.0 // Return percentage 0-100
    }
    
    fn hash_context(&self, context: &FeatureContext) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        context.user_id.hash(&mut hasher);
        context.environment.hash(&mut hasher);
        context.user_properties.hash(&mut hasher);
        
        hasher.finish()
    }
    
    async fn evaluate_custom_expression(&self, expression: &str, context: &FeatureContext) -> bool {
        // Simple expression evaluator - in production, use a proper expression engine
        match expression {
            "premium_user" => {
                context.user_properties.get("tier").map(|t| t == "premium").unwrap_or(false)
            }
            "beta_tester" => {
                context.user_properties.get("beta_tester").map(|t| t == "true").unwrap_or(false)
            }
            "weekday_only" => {
                let weekday = context.timestamp.weekday();
                !matches!(weekday, chrono::Weekday::Sat | chrono::Weekday::Sun)
            }
            _ => {
                log::warn!("Unknown custom expression: {}", expression);
                false
            }
        }
    }
    
    pub async fn set_flag(&self, flag_name: String, flag: FeatureFlag) {
        let old_value = {
            let flags = self.flags.read().await;
            flags.get(&flag_name).map(|f| f.enabled).unwrap_or(false)
        };
        
        let new_value = flag.enabled;
        
        {
            let mut flags = self.flags.write().await;
            flags.insert(flag_name.clone(), flag);
        }
        
        // Clear related cache entries
        self.clear_cache_for_flag(&flag_name).await;
        
        // Notify subscribers if value changed
        if old_value != new_value {
            self.notify_flag_change(&flag_name, old_value, new_value).await;
        }
    }
    
    async fn clear_cache_for_flag(&self, flag_name: &str) {
        let mut cache = self.evaluation_cache.write().await;
        cache.retain(|key, _| !key.starts_with(&format!("{}:", flag_name)));
    }
    
    async fn notify_flag_change(&self, flag_name: &str, old_value: bool, new_value: bool) {
        let subscribers = self.subscribers.read().await;
        
        for subscriber in subscribers.values() {
            if let Err(e) = subscriber.on_flag_changed(flag_name, old_value, new_value).await {
                log::error!("Feature flag subscriber error: {}", e);
            }
        }
    }
    
    pub async fn start_remote_sync(&self) -> Result<()> {
        if let Some(ref provider) = self.remote_provider {
            // Initial fetch
            let remote_flags = provider.fetch_flags().await?;
            
            {
                let mut flags = self.flags.write().await;
                for (name, flag) in remote_flags {
                    flags.insert(name, flag);
                }
            }
            
            // Start watching for changes
            let mut change_stream = provider.watch_for_changes().await?;
            let flags = Arc::clone(&self.flags);
            let subscribers = Arc::clone(&self.subscribers);
            let evaluation_cache = Arc::clone(&self.evaluation_cache);
            
            tokio::spawn(async move {
                while let Some(update) = change_stream.next().await {
                    // Handle remote flag update
                    log::info!("Received remote flag update: {:?}", update);
                    
                    // Update local flags and notify subscribers
                    // Implementation details depend on the update structure
                }
            });
        }
        
        Ok(())
    }
}

#[derive(Debug)]
pub struct FeatureFlagUpdate {
    pub flag_name: String,
    pub old_flag: Option<FeatureFlag>,
    pub new_flag: Option<FeatureFlag>,
    pub change_type: FlagChangeType,
}

#[derive(Debug)]
pub enum FlagChangeType {
    Created,
    Updated,
    Deleted,
}
```

## A/B Testing Integration

### A/B Test Configuration Manager
```rust
pub struct ABTestManager {
    tests: Arc<RwLock<HashMap<String, ABTest>>>,
    user_assignments: Arc<RwLock<HashMap<String, HashMap<String, String>>>>, // user_id -> test_id -> variant
    analytics_provider: Option<Box<dyn AnalyticsProvider>>,
}

#[derive(Debug, Clone)]
pub struct ABTest {
    pub id: String,
    pub name: String,
    pub description: String,
    pub enabled: bool,
    pub variants: Vec<ABVariant>,
    pub traffic_allocation: f64, // Percentage of users to include
    pub start_time: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
    pub targeting_rules: Vec<TargetingRule>,
    pub metrics: Vec<String>, // Metric names to track
}

#[derive(Debug, Clone)]
pub struct ABVariant {
    pub id: String,
    pub name: String,
    pub allocation_percentage: f64,
    pub config: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone)]
pub struct TargetingRule {
    pub condition: String, // Expression to evaluate
    pub include: bool,     // Include or exclude matching users
}

#[async_trait]
pub trait AnalyticsProvider: Send + Sync {
    async fn track_assignment(&self, user_id: &str, test_id: &str, variant_id: &str) -> Result<()>;
    async fn track_conversion(&self, user_id: &str, test_id: &str, metric: &str, value: f64) -> Result<()>;
}

impl ABTestManager {
    pub fn new() -> Self {
        Self {
            tests: Arc::new(RwLock::new(HashMap::new())),
            user_assignments: Arc::new(RwLock::new(HashMap::new())),
            analytics_provider: None,
        }
    }
    
    pub async fn get_variant(
        &self,
        test_id: &str,
        user_id: &str,
        context: &FeatureContext,
    ) -> Option<ABVariant> {
        // Check existing assignment first
        {
            let assignments = self.user_assignments.read().await;
            if let Some(user_tests) = assignments.get(user_id) {
                if let Some(variant_id) = user_tests.get(test_id) {
                    let tests = self.tests.read().await;
                    if let Some(test) = tests.get(test_id) {
                        return test.variants.iter()
                            .find(|v| v.id == *variant_id)
                            .cloned();
                    }
                }
            }
        }
        
        // No existing assignment, check if user should be assigned
        let tests = self.tests.read().await;
        let test = tests.get(test_id)?;
        
        if !test.enabled {
            return None;
        }
        
        // Check time window
        if Utc::now() < test.start_time {
            return None;
        }
        
        if let Some(end_time) = test.end_time {
            if Utc::now() > end_time {
                return None;
            }
        }
        
        // Check targeting rules
        if !self.user_matches_targeting(user_id, &test.targeting_rules, context).await {
            return None;
        }
        
        // Check traffic allocation
        let user_hash = self.hash_user_for_test(user_id, test_id);
        if user_hash > test.traffic_allocation {
            return None;
        }
        
        // Assign variant
        let variant = self.assign_variant(test, user_id)?;
        
        // Store assignment
        {
            let mut assignments = self.user_assignments.write().await;
            assignments
                .entry(user_id.to_string())
                .or_insert_with(HashMap::new)
                .insert(test_id.to_string(), variant.id.clone());
        }
        
        // Track assignment
        if let Some(ref analytics) = self.analytics_provider {
            let _ = analytics.track_assignment(user_id, test_id, &variant.id).await;
        }
        
        Some(variant)
    }
    
    fn assign_variant(&self, test: &ABTest, user_id: &str) -> Option<ABVariant> {
        let user_hash = self.hash_user_for_variant(user_id, &test.id);
        let mut cumulative_percentage = 0.0;
        
        for variant in &test.variants {
            cumulative_percentage += variant.allocation_percentage;
            if user_hash <= cumulative_percentage {
                return Some(variant.clone());
            }
        }
        
        // Fallback to first variant if percentages don't add up to 100
        test.variants.first().cloned()
    }
    
    fn hash_user_for_test(&self, user_id: &str, test_id: &str) -> f64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        user_id.hash(&mut hasher);
        test_id.hash(&mut hasher);
        "traffic".hash(&mut hasher);
        
        let hash = hasher.finish();
        (hash % 10000) as f64 / 100.0
    }
    
    fn hash_user_for_variant(&self, user_id: &str, test_id: &str) -> f64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        user_id.hash(&mut hasher);
        test_id.hash(&mut hasher);
        "variant".hash(&mut hasher);
        
        let hash = hasher.finish();
        (hash % 10000) as f64 / 100.0
    }
    
    async fn user_matches_targeting(
        &self,
        user_id: &str,
        rules: &[TargetingRule],
        context: &FeatureContext,
    ) -> bool {
        for rule in rules {
            let matches = self.evaluate_targeting_rule(&rule.condition, user_id, context).await;
            
            if rule.include && !matches {
                return false;
            }
            
            if !rule.include && matches {
                return false;
            }
        }
        
        true
    }
    
    async fn evaluate_targeting_rule(
        &self,
        condition: &str,
        user_id: &str,
        context: &FeatureContext,
    ) -> bool {
        // Simple condition evaluator - in production, use a proper expression engine
        match condition {
            "new_user" => {
                context.user_properties.get("account_age_days")
                    .and_then(|age| age.parse::<i32>().ok())
                    .map(|age| age <= 7)
                    .unwrap_or(false)
            }
            "mobile_user" => {
                context.user_properties.get("platform")
                    .map(|platform| platform == "mobile")
                    .unwrap_or(false)
            }
            "premium_user" => {
                context.user_properties.get("subscription")
                    .map(|sub| sub == "premium")
                    .unwrap_or(false)
            }
            _ => {
                log::warn!("Unknown targeting condition: {}", condition);
                true
            }
        }
    }
    
    pub async fn track_conversion(
        &self,
        user_id: &str,
        test_id: &str,
        metric: &str,
        value: f64,
    ) -> Result<()> {
        // Verify user is in the test
        let assignments = self.user_assignments.read().await;
        let user_tests = assignments.get(user_id)
            .ok_or_else(|| anyhow!("User {} not found in any tests", user_id))?;
            
        let _variant_id = user_tests.get(test_id)
            .ok_or_else(|| anyhow!("User {} not assigned to test {}", user_id, test_id))?;
        
        // Track conversion
        if let Some(ref analytics) = self.analytics_provider {
            analytics.track_conversion(user_id, test_id, metric, value).await?;
        }
        
        Ok(())
    }
}
```

## Configuration API

### RESTful Configuration Endpoints
```rust
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post, put, delete},
    Router,
};

pub fn configuration_routes() -> Router<AppState> {
    Router::new()
        .route("/config", get(get_configuration))
        .route("/config/:key", get(get_configuration_key))
        .route("/config/:key", put(update_configuration_key))
        .route("/config/:key", delete(delete_configuration_key))
        .route("/config/reload", post(reload_configuration))
        .route("/config/validate", post(validate_configuration))
        .route("/config/schema", get(get_configuration_schema))
        .route("/features", get(list_feature_flags))
        .route("/features/:flag", put(update_feature_flag))
        .route("/features/:flag/evaluate", post(evaluate_feature_flag))
        .route("/ab-tests", get(list_ab_tests))
        .route("/ab-tests/:test/variant", post(get_ab_test_variant))
}

#[derive(Serialize)]
struct ConfigurationResponse {
    config: HashMap<String, serde_json::Value>,
    metadata: ConfigurationMetadata,
}

#[derive(Serialize)]
struct ConfigurationMetadata {
    last_updated: DateTime<Utc>,
    version: String,
    sources: Vec<String>,
}

async fn get_configuration(
    State(state): State<AppState>,
) -> Result<Json<ConfigurationResponse>, StatusCode> {
    let config_manager = &state.config_manager;
    
    match config_manager.get_all_configuration().await {
        Ok(config) => {
            let response = ConfigurationResponse {
                config: config.into_iter()
                    .map(|(k, v)| (k, serde_json::Value::String(format!("{:?}", v))))
                    .collect(),
                metadata: ConfigurationMetadata {
                    last_updated: Utc::now(),
                    version: "1.0".to_string(),
                    sources: vec!["file".to_string(), "env".to_string()],
                },
            };
            
            Ok(Json(response))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn get_configuration_key(
    Path(key): Path<String>,
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let config_manager = &state.config_manager;
    
    match config_manager.get::<serde_json::Value>(&key).await {
        Ok(Some(value)) => Ok(Json(value)),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

#[derive(Deserialize)]
struct UpdateConfigRequest {
    value: serde_json::Value,
    validate_only: Option<bool>,
}

async fn update_configuration_key(
    Path(key): Path<String>,
    State(state): State<AppState>,
    Json(request): Json<UpdateConfigRequest>,
) -> Result<StatusCode, StatusCode> {
    let hot_reload_manager = &state.hot_reload_manager;
    
    // Convert JSON value to ConfigValue
    let config_value = json_to_config_value(request.value);
    
    if request.validate_only.unwrap_or(false) {
        // Validation only
        match state.config_manager.validate_partial(&key, &config_value).await {
            Ok(_) => Ok(StatusCode::OK),
            Err(_) => Err(StatusCode::BAD_REQUEST),
        }
    } else {
        // Update configuration
        match hot_reload_manager.update_configuration_key(key, config_value).await {
            Ok(_) => Ok(StatusCode::OK),
            Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
        }
    }
}

async fn reload_configuration(
    State(state): State<AppState>,
) -> Result<Json<ConfigReloadResult>, StatusCode> {
    let hot_reload_manager = &state.hot_reload_manager;
    
    match hot_reload_manager.reload_configuration("api").await {
        Ok(result) => Ok(Json(result)),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

fn json_to_config_value(json: serde_json::Value) -> ConfigValue {
    match json {
        serde_json::Value::String(s) => ConfigValue::String(s),
        serde_json::Value::Number(n) => {
            if let Some(i) = n.as_i64() {
                ConfigValue::Integer(i)
            } else if let Some(f) = n.as_f64() {
                ConfigValue::Float(f)
            } else {
                ConfigValue::String(n.to_string())
            }
        }
        serde_json::Value::Bool(b) => ConfigValue::Boolean(b),
        serde_json::Value::Array(arr) => {
            let config_arr = arr.into_iter().map(json_to_config_value).collect();
            ConfigValue::Array(config_arr)
        }
        serde_json::Value::Object(obj) => {
            let config_obj = obj.into_iter()
                .map(|(k, v)| (k, json_to_config_value(v)))
                .collect();
            ConfigValue::Object(config_obj)
        }
        serde_json::Value::Null => ConfigValue::Null,
    }
}
```