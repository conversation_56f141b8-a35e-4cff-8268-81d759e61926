# Secrets Management

## Secret Store Architecture

### Multi-Backend Secret Manager
```rust
use std::sync::Arc;
use tokio::sync::RwLock;
use aes_gcm::{Aes256Gcm, Key, Nonce, aead::{Aead, NewAead}};
use rand::{Rng, thread_rng};

pub struct SecretManager {
    backends: Vec<Box<dyn SecretBackend>>,
    cache: Arc<RwLock<HashMap<String, CachedSecret>>>,
    encryption_key: Key<Aes256Gcm>,
    cache_ttl: Duration,
    audit_logger: Arc<dyn AuditLogger>,
}

#[async_trait]
pub trait SecretBackend: Send + Sync {
    async fn get_secret(&self, key: &str) -> Result<Option<Secret>>;
    async fn put_secret(&self, key: &str, secret: &Secret) -> Result<()>;
    async fn delete_secret(&self, key: &str) -> Result<()>;
    async fn list_secrets(&self) -> Result<Vec<String>>;
    fn backend_name(&self) -> &str;
    fn priority(&self) -> u32;
}

#[derive(Debug, Clone)]
pub struct Secret {
    pub key: String,
    pub value: SecretValue,
    pub metadata: SecretMetadata,
}

#[derive(Debug, Clone)]
pub enum SecretValue {
    String(String),
    Binary(Vec<u8>),
    Json(serde_json::Value),
    Certificate {
        cert: Vec<u8>,
        private_key: Vec<u8>,
        ca_chain: Option<Vec<u8>>,
    },
}

#[derive(Debug, Clone)]
pub struct SecretMetadata {
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub version: u64,
    pub tags: HashMap<String, String>,
    pub access_policy: AccessPolicy,
}

#[derive(Debug, Clone)]
pub struct AccessPolicy {
    pub allowed_services: Vec<String>,
    pub allowed_environments: Vec<String>,
    pub rotation_required: bool,
    pub max_access_count: Option<u64>,
}

struct CachedSecret {
    secret: Secret,
    cached_at: Instant,
    access_count: u64,
}

#[async_trait]
pub trait AuditLogger: Send + Sync {
    async fn log_secret_access(&self, event: SecretAccessEvent);
}

#[derive(Debug)]
pub struct SecretAccessEvent {
    pub secret_key: String,
    pub operation: SecretOperation,
    pub service_id: String,
    pub user_id: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub success: bool,
    pub error: Option<String>,
}

#[derive(Debug)]
pub enum SecretOperation {
    Read,
    Write,
    Delete,
    List,
    Rotate,
}

impl SecretManager {
    pub fn new(encryption_key: [u8; 32]) -> Self {
        Self {
            backends: Vec::new(),
            cache: Arc::new(RwLock::new(HashMap::new())),
            encryption_key: Key::from_slice(&encryption_key).clone(),
            cache_ttl: Duration::from_secs(300), // 5 minutes
            audit_logger: Arc::new(DefaultAuditLogger),
        }
    }
    
    pub fn add_backend(&mut self, backend: Box<dyn SecretBackend>) {
        self.backends.push(backend);
        // Sort by priority (higher priority first)
        self.backends.sort_by(|a, b| b.priority().cmp(&a.priority()));
    }
    
    pub async fn get_secret(
        &self,
        key: &str,
        service_id: &str,
        user_id: Option<&str>,
    ) -> Result<Option<Secret>> {
        let start_time = Instant::now();
        
        // Check cache first
        {
            let cache = self.cache.read().await;
            if let Some(cached) = cache.get(key) {
                if cached.cached_at.elapsed() < self.cache_ttl {
                    // Verify access policy
                    if self.check_access_policy(&cached.secret, service_id).await? {
                        // Log access
                        self.audit_logger.log_secret_access(SecretAccessEvent {
                            secret_key: key.to_string(),
                            operation: SecretOperation::Read,
                            service_id: service_id.to_string(),
                            user_id: user_id.map(|s| s.to_string()),
                            timestamp: Utc::now(),
                            success: true,
                            error: None,
                        }).await;
                        
                        return Ok(Some(cached.secret.clone()));
                    } else {
                        self.audit_logger.log_secret_access(SecretAccessEvent {
                            secret_key: key.to_string(),
                            operation: SecretOperation::Read,
                            service_id: service_id.to_string(),
                            user_id: user_id.map(|s| s.to_string()),
                            timestamp: Utc::now(),
                            success: false,
                            error: Some("Access denied by policy".to_string()),
                        }).await;
                        
                        return Err(anyhow!("Access denied to secret '{}'", key));
                    }
                }
            }
        }
        
        // Try backends in priority order
        for backend in &self.backends {
            match backend.get_secret(key).await {
                Ok(Some(secret)) => {
                    // Verify access policy
                    if !self.check_access_policy(&secret, service_id).await? {
                        self.audit_logger.log_secret_access(SecretAccessEvent {
                            secret_key: key.to_string(),
                            operation: SecretOperation::Read,
                            service_id: service_id.to_string(),
                            user_id: user_id.map(|s| s.to_string()),
                            timestamp: Utc::now(),
                            success: false,
                            error: Some("Access denied by policy".to_string()),
                        }).await;
                        
                        return Err(anyhow!("Access denied to secret '{}'", key));
                    }
                    
                    // Cache the secret
                    {
                        let mut cache = self.cache.write().await;
                        cache.insert(key.to_string(), CachedSecret {
                            secret: secret.clone(),
                            cached_at: Instant::now(),
                            access_count: 1,
                        });
                    }
                    
                    // Log successful access
                    self.audit_logger.log_secret_access(SecretAccessEvent {
                        secret_key: key.to_string(),
                        operation: SecretOperation::Read,
                        service_id: service_id.to_string(),
                        user_id: user_id.map(|s| s.to_string()),
                        timestamp: Utc::now(),
                        success: true,
                        error: None,
                    }).await;
                    
                    return Ok(Some(secret));
                }
                Ok(None) => continue,
                Err(e) => {
                    log::warn!("Backend {} failed to get secret '{}': {}", 
                        backend.backend_name(), key, e);
                    continue;
                }
            }
        }
        
        // Secret not found in any backend
        self.audit_logger.log_secret_access(SecretAccessEvent {
            secret_key: key.to_string(),
            operation: SecretOperation::Read,
            service_id: service_id.to_string(),
            user_id: user_id.map(|s| s.to_string()),
            timestamp: Utc::now(),
            success: false,
            error: Some("Secret not found".to_string()),
        }).await;
        
        Ok(None)
    }
    
    async fn check_access_policy(&self, secret: &Secret, service_id: &str) -> Result<bool> {
        let policy = &secret.metadata.access_policy;
        
        // Check service access
        if !policy.allowed_services.is_empty() && 
           !policy.allowed_services.contains(&service_id.to_string()) {
            return Ok(false);
        }
        
        // Check environment access (would need environment context)
        // This is simplified - in reality you'd get environment from context
        
        // Check expiration
        if let Some(expires_at) = secret.metadata.expires_at {
            if Utc::now() > expires_at {
                return Ok(false);
            }
        }
        
        // Check max access count
        if let Some(max_count) = policy.max_access_count {
            let cache = self.cache.read().await;
            if let Some(cached) = cache.get(&secret.key) {
                if cached.access_count >= max_count {
                    return Ok(false);
                }
            }
        }
        
        Ok(true)
    }
    
    pub async fn put_secret(
        &self,
        key: &str,
        value: SecretValue,
        access_policy: AccessPolicy,
        service_id: &str,
    ) -> Result<()> {
        let secret = Secret {
            key: key.to_string(),
            value,
            metadata: SecretMetadata {
                created_at: Utc::now(),
                updated_at: Utc::now(),
                expires_at: None,
                version: 1,
                tags: HashMap::new(),
                access_policy,
            },
        };
        
        // Store in primary backend (highest priority)
        if let Some(primary_backend) = self.backends.first() {
            primary_backend.put_secret(key, &secret).await?;
            
            // Invalidate cache
            {
                let mut cache = self.cache.write().await;
                cache.remove(key);
            }
            
            // Log operation
            self.audit_logger.log_secret_access(SecretAccessEvent {
                secret_key: key.to_string(),
                operation: SecretOperation::Write,
                service_id: service_id.to_string(),
                user_id: None,
                timestamp: Utc::now(),
                success: true,
                error: None,
            }).await;
            
            Ok(())
        } else {
            Err(anyhow!("No secret backends configured"))
        }
    }
    
    pub async fn rotate_secret(&self, key: &str, service_id: &str) -> Result<Secret> {
        // Get current secret
        let current_secret = self.get_secret(key, service_id, None).await?
            .ok_or_else(|| anyhow!("Secret '{}' not found", key))?;
        
        // Generate new secret value based on type
        let new_value = match &current_secret.value {
            SecretValue::String(_) => {
                SecretValue::String(self.generate_random_string(32))
            }
            SecretValue::Binary(_) => {
                SecretValue::Binary(self.generate_random_bytes(32))
            }
            SecretValue::Json(template) => {
                // For JSON secrets, we'd need custom rotation logic
                SecretValue::Json(template.clone())
            }
            SecretValue::Certificate { .. } => {
                // Certificate rotation would involve generating new certs
                return Err(anyhow!("Certificate rotation not implemented"));
            }
        };
        
        // Create new secret with incremented version
        let new_secret = Secret {
            key: key.to_string(),
            value: new_value,
            metadata: SecretMetadata {
                created_at: current_secret.metadata.created_at,
                updated_at: Utc::now(),
                expires_at: current_secret.metadata.expires_at,
                version: current_secret.metadata.version + 1,
                tags: current_secret.metadata.tags.clone(),
                access_policy: current_secret.metadata.access_policy.clone(),
            },
        };
        
        // Store the new secret
        if let Some(primary_backend) = self.backends.first() {
            primary_backend.put_secret(key, &new_secret).await?;
            
            // Invalidate cache
            {
                let mut cache = self.cache.write().await;
                cache.remove(key);
            }
            
            // Log rotation
            self.audit_logger.log_secret_access(SecretAccessEvent {
                secret_key: key.to_string(),
                operation: SecretOperation::Rotate,
                service_id: service_id.to_string(),
                user_id: None,
                timestamp: Utc::now(),
                success: true,
                error: None,
            }).await;
            
            Ok(new_secret)
        } else {
            Err(anyhow!("No secret backends configured"))
        }
    }
    
    fn generate_random_string(&self, length: usize) -> String {
        use rand::distributions::Alphanumeric;
        thread_rng()
            .sample_iter(&Alphanumeric)
            .take(length)
            .map(char::from)
            .collect()
    }
    
    fn generate_random_bytes(&self, length: usize) -> Vec<u8> {
        let mut bytes = vec![0u8; length];
        thread_rng().fill(&mut bytes[..]);
        bytes
    }
}
```

## HashiCorp Vault Backend

### Vault Integration
```rust
use reqwest::Client;

pub struct VaultBackend {
    client: Client,
    base_url: String,
    token: String,
    mount_path: String,
    priority: u32,
}

impl VaultBackend {
    pub fn new(base_url: String, token: String, mount_path: String) -> Self {
        Self {
            client: Client::new(),
            base_url,
            token,
            mount_path,
            priority: 90, // High priority
        }
    }
    
    async fn make_vault_request<T>(&self, method: &str, path: &str, body: Option<&T>) -> Result<serde_json::Value>
    where
        T: serde::Serialize,
    {
        let url = format!("{}/v1/{}/{}", self.base_url, self.mount_path, path);
        
        let mut request = match method {
            "GET" => self.client.get(&url),
            "POST" => self.client.post(&url),
            "PUT" => self.client.put(&url),
            "DELETE" => self.client.delete(&url),
            _ => return Err(anyhow!("Unsupported HTTP method: {}", method)),
        };
        
        request = request.header("X-Vault-Token", &self.token);
        
        if let Some(body) = body {
            request = request.json(body);
        }
        
        let response = request.send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("Vault request failed: {}", error_text));
        }
        
        let json: serde_json::Value = response.json().await?;
        Ok(json)
    }
}

#[async_trait]
impl SecretBackend for VaultBackend {
    async fn get_secret(&self, key: &str) -> Result<Option<Secret>> {
        let response = self.make_vault_request::<()>("GET", key, None).await?;
        
        if let Some(data) = response.get("data").and_then(|d| d.get("data")) {
            let secret_data = data.as_object()
                .ok_or_else(|| anyhow!("Invalid secret data format"))?;
                
            // Extract secret value
            let value = if let Some(value_str) = secret_data.get("value").and_then(|v| v.as_str()) {
                SecretValue::String(value_str.to_string())
            } else if let Some(value_json) = secret_data.get("value") {
                SecretValue::Json(value_json.clone())
            } else {
                return Err(anyhow!("No value found in secret"));
            };
            
            // Extract metadata
            let metadata = response.get("data").and_then(|d| d.get("metadata"));
            let created_at = metadata
                .and_then(|m| m.get("created_time"))
                .and_then(|t| t.as_str())
                .and_then(|s| DateTime::parse_from_rfc3339(s).ok())
                .map(|dt| dt.with_timezone(&Utc))
                .unwrap_or_else(Utc::now);
                
            let version = metadata
                .and_then(|m| m.get("version"))
                .and_then(|v| v.as_u64())
                .unwrap_or(1);
            
            // Create access policy from custom metadata
            let custom_metadata = metadata
                .and_then(|m| m.get("custom_metadata"))
                .and_then(|cm| cm.as_object())
                .unwrap_or(&serde_json::Map::new());
                
            let allowed_services = custom_metadata
                .get("allowed_services")
                .and_then(|s| s.as_str())
                .map(|s| s.split(',').map(|s| s.trim().to_string()).collect())
                .unwrap_or_else(Vec::new);
                
            let access_policy = AccessPolicy {
                allowed_services,
                allowed_environments: Vec::new(),
                rotation_required: false,
                max_access_count: None,
            };
            
            let secret = Secret {
                key: key.to_string(),
                value,
                metadata: SecretMetadata {
                    created_at,
                    updated_at: created_at,
                    expires_at: None,
                    version,
                    tags: HashMap::new(),
                    access_policy,
                },
            };
            
            Ok(Some(secret))
        } else {
            Ok(None)
        }
    }
    
    async fn put_secret(&self, key: &str, secret: &Secret) -> Result<()> {
        let mut request_body = serde_json::Map::new();
        
        // Add secret value
        match &secret.value {
            SecretValue::String(s) => {
                request_body.insert("value".to_string(), serde_json::Value::String(s.clone()));
            }
            SecretValue::Json(j) => {
                request_body.insert("value".to_string(), j.clone());
            }
            SecretValue::Binary(b) => {
                let encoded = base64::encode(b);
                request_body.insert("value".to_string(), serde_json::Value::String(encoded));
                request_body.insert("encoding".to_string(), serde_json::Value::String("base64".to_string()));
            }
            SecretValue::Certificate { cert, private_key, ca_chain } => {
                let mut cert_obj = serde_json::Map::new();
                cert_obj.insert("certificate".to_string(), 
                    serde_json::Value::String(base64::encode(cert)));
                cert_obj.insert("private_key".to_string(), 
                    serde_json::Value::String(base64::encode(private_key)));
                    
                if let Some(ca) = ca_chain {
                    cert_obj.insert("ca_chain".to_string(), 
                        serde_json::Value::String(base64::encode(ca)));
                }
                
                request_body.insert("value".to_string(), serde_json::Value::Object(cert_obj));
            }
        }
        
        // Add custom metadata for access policy
        let mut custom_metadata = serde_json::Map::new();
        custom_metadata.insert("allowed_services".to_string(), 
            serde_json::Value::String(secret.metadata.access_policy.allowed_services.join(",")));
            
        let vault_request = serde_json::json!({
            "data": request_body,
            "options": {
                "custom_metadata": custom_metadata
            }
        });
        
        self.make_vault_request("POST", key, Some(&vault_request)).await?;
        
        Ok(())
    }
    
    async fn delete_secret(&self, key: &str) -> Result<()> {
        self.make_vault_request::<()>("DELETE", key, None).await?;
        Ok(())
    }
    
    async fn list_secrets(&self) -> Result<Vec<String>> {
        let response = self.make_vault_request::<()>("GET", "", None).await?;
        
        if let Some(keys) = response.get("data").and_then(|d| d.get("keys")).and_then(|k| k.as_array()) {
            let secret_keys = keys
                .iter()
                .filter_map(|k| k.as_str())
                .map(|s| s.to_string())
                .collect();
                
            Ok(secret_keys)
        } else {
            Ok(Vec::new())
        }
    }
    
    fn backend_name(&self) -> &str {
        "vault"
    }
    
    fn priority(&self) -> u32 {
        self.priority
    }
}
```

## Local Encrypted Storage

### File-Based Encrypted Backend
```rust
use std::path::PathBuf;
use tokio::fs;

pub struct EncryptedFileBackend {
    storage_path: PathBuf,
    cipher: Aes256Gcm,
    priority: u32,
}

impl EncryptedFileBackend {
    pub fn new(storage_path: PathBuf, encryption_key: &[u8; 32]) -> Self {
        let key = Key::from_slice(encryption_key);
        let cipher = Aes256Gcm::new(key);
        
        Self {
            storage_path,
            cipher,
            priority: 50, // Medium priority
        }
    }
    
    fn get_secret_file_path(&self, key: &str) -> PathBuf {
        let safe_key = key.replace('/', "_").replace('\\', "_");
        self.storage_path.join(format!("{}.secret", safe_key))
    }
    
    async fn encrypt_secret(&self, secret: &Secret) -> Result<Vec<u8>> {
        let serialized = bincode::serialize(secret)?;
        
        let mut nonce_bytes = [0u8; 12];
        thread_rng().fill(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);
        
        let ciphertext = self.cipher.encrypt(nonce, serialized.as_ref())
            .map_err(|e| anyhow!("Encryption failed: {}", e))?;
            
        // Prepend nonce to ciphertext
        let mut encrypted_data = nonce_bytes.to_vec();
        encrypted_data.extend_from_slice(&ciphertext);
        
        Ok(encrypted_data)
    }
    
    async fn decrypt_secret(&self, encrypted_data: &[u8]) -> Result<Secret> {
        if encrypted_data.len() < 12 {
            return Err(anyhow!("Invalid encrypted data: too short"));
        }
        
        let nonce = Nonce::from_slice(&encrypted_data[0..12]);
        let ciphertext = &encrypted_data[12..];
        
        let plaintext = self.cipher.decrypt(nonce, ciphertext)
            .map_err(|e| anyhow!("Decryption failed: {}", e))?;
            
        let secret = bincode::deserialize(&plaintext)?;
        Ok(secret)
    }
}

#[async_trait]
impl SecretBackend for EncryptedFileBackend {
    async fn get_secret(&self, key: &str) -> Result<Option<Secret>> {
        let file_path = self.get_secret_file_path(key);
        
        if !file_path.exists() {
            return Ok(None);
        }
        
        let encrypted_data = fs::read(&file_path).await?;
        let secret = self.decrypt_secret(&encrypted_data).await?;
        
        Ok(Some(secret))
    }
    
    async fn put_secret(&self, key: &str, secret: &Secret) -> Result<()> {
        // Ensure directory exists
        if let Some(parent) = self.storage_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        let encrypted_data = self.encrypt_secret(secret).await?;
        let file_path = self.get_secret_file_path(key);
        
        fs::write(&file_path, encrypted_data).await?;
        
        // Set restrictive permissions on Unix systems
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = fs::metadata(&file_path).await?.permissions();
            perms.set_mode(0o600); // Owner read/write only
            fs::set_permissions(&file_path, perms).await?;
        }
        
        Ok(())
    }
    
    async fn delete_secret(&self, key: &str) -> Result<()> {
        let file_path = self.get_secret_file_path(key);
        
        if file_path.exists() {
            fs::remove_file(&file_path).await?;
        }
        
        Ok(())
    }
    
    async fn list_secrets(&self) -> Result<Vec<String>> {
        if !self.storage_path.exists() {
            return Ok(Vec::new());
        }
        
        let mut entries = fs::read_dir(&self.storage_path).await?;
        let mut secrets = Vec::new();
        
        while let Some(entry) = entries.next_entry().await? {
            if let Some(file_name) = entry.file_name().to_str() {
                if file_name.ends_with(".secret") {
                    let key = file_name.strip_suffix(".secret").unwrap();
                    secrets.push(key.replace('_', "/"));
                }
            }
        }
        
        Ok(secrets)
    }
    
    fn backend_name(&self) -> &str {
        "encrypted_file"
    }
    
    fn priority(&self) -> u32 {
        self.priority
    }
}
```

## Automatic Secret Rotation

### Secret Rotation Manager
```rust
pub struct SecretRotationManager {
    secret_manager: Arc<SecretManager>,
    rotation_rules: HashMap<String, RotationRule>,
    scheduler: Arc<tokio_cron_scheduler::JobScheduler>,
}

#[derive(Debug, Clone)]
pub struct RotationRule {
    pub secret_pattern: String,
    pub rotation_interval: Duration,
    pub rotation_strategy: RotationStrategy,
    pub notification_channels: Vec<String>,
    pub pre_rotation_hooks: Vec<String>,
    pub post_rotation_hooks: Vec<String>,
}

#[derive(Debug, Clone)]
pub enum RotationStrategy {
    GenerateRandom,
    External { endpoint: String, auth_token: String },
    Custom { handler: String },
}

impl SecretRotationManager {
    pub async fn new(secret_manager: Arc<SecretManager>) -> Result<Self> {
        let scheduler = tokio_cron_scheduler::JobScheduler::new().await?;
        
        Ok(Self {
            secret_manager,
            rotation_rules: HashMap::new(),
            scheduler: Arc::new(scheduler),
        })
    }
    
    pub async fn add_rotation_rule(&mut self, pattern: String, rule: RotationRule) -> Result<()> {
        // Convert interval to cron expression
        let cron_expr = self.interval_to_cron(&rule.rotation_interval)?;
        
        let secret_manager = Arc::clone(&self.secret_manager);
        let rule_clone = rule.clone();
        let pattern_clone = pattern.clone();
        
        self.scheduler.add(
            tokio_cron_scheduler::Job::new_async(cron_expr.as_str(), move |_uuid, _l| {
                let secret_manager = Arc::clone(&secret_manager);
                let rule = rule_clone.clone();
                let pattern = pattern_clone.clone();
                
                Box::pin(async move {
                    if let Err(e) = Self::execute_rotation(&secret_manager, &pattern, &rule).await {
                        log::error!("Secret rotation failed for pattern {}: {}", pattern, e);
                    }
                })
            })?
        ).await?;
        
        self.rotation_rules.insert(pattern, rule);
        
        Ok(())
    }
    
    pub async fn start(&self) -> Result<()> {
        self.scheduler.start().await?;
        log::info!("Secret rotation manager started");
        Ok(())
    }
    
    async fn execute_rotation(
        secret_manager: &SecretManager,
        pattern: &str,
        rule: &RotationRule,
    ) -> Result<()> {
        log::info!("Starting secret rotation for pattern: {}", pattern);
        
        // Find secrets matching the pattern
        let matching_secrets = Self::find_matching_secrets(secret_manager, pattern).await?;
        
        for secret_key in matching_secrets {
            log::info!("Rotating secret: {}", secret_key);
            
            // Execute pre-rotation hooks
            for hook in &rule.pre_rotation_hooks {
                if let Err(e) = Self::execute_hook(hook, &secret_key, "pre-rotation").await {
                    log::warn!("Pre-rotation hook failed for {}: {}", secret_key, e);
                }
            }
            
            // Perform rotation based on strategy
            match Self::rotate_secret_with_strategy(
                secret_manager,
                &secret_key,
                &rule.rotation_strategy,
            ).await {
                Ok(new_secret) => {
                    log::info!("Successfully rotated secret: {}", secret_key);
                    
                    // Execute post-rotation hooks
                    for hook in &rule.post_rotation_hooks {
                        if let Err(e) = Self::execute_hook(hook, &secret_key, "post-rotation").await {
                            log::warn!("Post-rotation hook failed for {}: {}", secret_key, e);
                        }
                    }
                    
                    // Send notifications
                    for channel in &rule.notification_channels {
                        Self::send_rotation_notification(channel, &secret_key, &new_secret).await;
                    }
                }
                Err(e) => {
                    log::error!("Failed to rotate secret {}: {}", secret_key, e);
                    
                    // Send failure notification
                    for channel in &rule.notification_channels {
                        Self::send_rotation_failure_notification(channel, &secret_key, &e).await;
                    }
                }
            }
        }
        
        Ok(())
    }
    
    async fn find_matching_secrets(
        secret_manager: &SecretManager,
        pattern: &str,
    ) -> Result<Vec<String>> {
        // This would need to be implemented based on the secret backend's list capability
        // For now, return empty list
        Ok(Vec::new())
    }
    
    async fn rotate_secret_with_strategy(
        secret_manager: &SecretManager,
        secret_key: &str,
        strategy: &RotationStrategy,
    ) -> Result<Secret> {
        match strategy {
            RotationStrategy::GenerateRandom => {
                secret_manager.rotate_secret(secret_key, "rotation_manager").await
            }
            RotationStrategy::External { endpoint, auth_token } => {
                Self::rotate_with_external_service(secret_key, endpoint, auth_token).await
            }
            RotationStrategy::Custom { handler } => {
                Self::rotate_with_custom_handler(secret_key, handler).await
            }
        }
    }
    
    async fn rotate_with_external_service(
        secret_key: &str,
        endpoint: &str,
        auth_token: &str,
    ) -> Result<Secret> {
        let client = reqwest::Client::new();
        
        let request_body = serde_json::json!({
            "secret_key": secret_key,
            "action": "rotate"
        });
        
        let response = client
            .post(endpoint)
            .header("Authorization", format!("Bearer {}", auth_token))
            .json(&request_body)
            .send()
            .await?;
            
        if !response.status().is_success() {
            return Err(anyhow!("External rotation service failed: {}", response.status()));
        }
        
        let rotation_response: RotationResponse = response.json().await?;
        
        // Convert response to Secret
        let new_secret = Secret {
            key: secret_key.to_string(),
            value: SecretValue::String(rotation_response.new_value),
            metadata: SecretMetadata {
                created_at: Utc::now(),
                updated_at: Utc::now(),
                expires_at: rotation_response.expires_at,
                version: rotation_response.version,
                tags: rotation_response.tags,
                access_policy: AccessPolicy {
                    allowed_services: Vec::new(),
                    allowed_environments: Vec::new(),
                    rotation_required: true,
                    max_access_count: None,
                },
            },
        };
        
        Ok(new_secret)
    }
    
    async fn rotate_with_custom_handler(
        secret_key: &str,
        handler: &str,
    ) -> Result<Secret> {
        // Execute custom rotation handler
        // This could be a script, webhook, or other custom logic
        match handler {
            "database_password" => Self::rotate_database_password(secret_key).await,
            "api_key" => Self::rotate_api_key(secret_key).await,
            "certificate" => Self::rotate_certificate(secret_key).await,
            _ => Err(anyhow!("Unknown rotation handler: {}", handler)),
        }
    }
    
    async fn rotate_database_password(secret_key: &str) -> Result<Secret> {
        // Generate new secure password
        let new_password = Self::generate_secure_password(32);
        
        // In a real implementation, this would:
        // 1. Connect to the database
        // 2. Update the user's password
        // 3. Verify the new password works
        
        let new_secret = Secret {
            key: secret_key.to_string(),
            value: SecretValue::String(new_password),
            metadata: SecretMetadata {
                created_at: Utc::now(),
                updated_at: Utc::now(),
                expires_at: Some(Utc::now() + Duration::days(90)), // 90 days
                version: 1,
                tags: hashmap! {
                    "type".to_string() => "database_password".to_string(),
                    "rotation_date".to_string() => Utc::now().to_rfc3339(),
                },
                access_policy: AccessPolicy {
                    allowed_services: vec!["database".to_string()],
                    allowed_environments: vec!["production".to_string()],
                    rotation_required: true,
                    max_access_count: None,
                },
            },
        };
        
        Ok(new_secret)
    }
    
    fn generate_secure_password(length: usize) -> String {
        use rand::distributions::Alphanumeric;
        use rand::seq::SliceRandom;
        
        let charset = b"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        let mut rng = thread_rng();
        
        (0..length)
            .map(|_| {
                let idx = rng.gen_range(0..charset.len());
                charset[idx] as char
            })
            .collect()
    }
    
    async fn execute_hook(hook: &str, secret_key: &str, phase: &str) -> Result<()> {
        log::info!("Executing {} hook: {} for secret: {}", phase, hook, secret_key);
        
        // In a real implementation, this would execute the actual hook
        // Could be a webhook, script execution, etc.
        
        Ok(())
    }
    
    async fn send_rotation_notification(channel: &str, secret_key: &str, new_secret: &Secret) {
        log::info!("Sending rotation notification via {}: {} rotated", channel, secret_key);
    }
    
    async fn send_rotation_failure_notification(channel: &str, secret_key: &str, error: &anyhow::Error) {
        log::error!("Sending failure notification via {}: {} rotation failed: {}", channel, secret_key, error);
    }
    
    fn interval_to_cron(&self, interval: &Duration) -> Result<String> {
        let hours = interval.as_secs() / 3600;
        
        if hours >= 24 {
            let days = hours / 24;
            Ok(format!("0 0 */{} * *", days)) // Every N days at midnight
        } else if hours >= 1 {
            Ok(format!("0 */{} * * *", hours)) // Every N hours
        } else {
            let minutes = interval.as_secs() / 60;
            Ok(format!("*/{} * * * *", minutes)) // Every N minutes
        }
    }
}

#[derive(Deserialize)]
struct RotationResponse {
    new_value: String,
    expires_at: Option<DateTime<Utc>>,
    version: u64,
    tags: HashMap<String, String>,
}
```

## Secret Injection

### Environment Variable Injection
```rust
pub struct SecretInjector {
    secret_manager: Arc<SecretManager>,
    injection_patterns: HashMap<String, InjectionPattern>,
}

#[derive(Debug, Clone)]
pub struct InjectionPattern {
    pub secret_key: String,
    pub target_env_var: String,
    pub format: InjectionFormat,
    pub refresh_interval: Option<Duration>,
}

#[derive(Debug, Clone)]
pub enum InjectionFormat {
    Raw,
    Json { field: String },
    ConnectionString { template: String },
    Base64Encoded,
}

impl SecretInjector {
    pub fn new(secret_manager: Arc<SecretManager>) -> Self {
        Self {
            secret_manager,
            injection_patterns: HashMap::new(),
        }
    }
    
    pub fn add_injection_pattern(&mut self, pattern_name: String, pattern: InjectionPattern) {
        self.injection_patterns.insert(pattern_name, pattern);
    }
    
    pub async fn inject_secrets(&self, service_id: &str) -> Result<()> {
        for (pattern_name, pattern) in &self.injection_patterns {
            if let Err(e) = self.inject_single_secret(pattern, service_id).await {
                log::error!("Failed to inject secret for pattern {}: {}", pattern_name, e);
            }
        }
        
        Ok(())
    }
    
    async fn inject_single_secret(&self, pattern: &InjectionPattern, service_id: &str) -> Result<()> {
        let secret = self.secret_manager
            .get_secret(&pattern.secret_key, service_id, None)
            .await?
            .ok_or_else(|| anyhow!("Secret '{}' not found", pattern.secret_key))?;
            
        let formatted_value = self.format_secret_value(&secret.value, &pattern.format)?;
        
        std::env::set_var(&pattern.target_env_var, formatted_value);
        
        log::debug!("Injected secret '{}' into environment variable '{}'", 
            pattern.secret_key, pattern.target_env_var);
        
        Ok(())
    }
    
    fn format_secret_value(&self, value: &SecretValue, format: &InjectionFormat) -> Result<String> {
        match (value, format) {
            (SecretValue::String(s), InjectionFormat::Raw) => Ok(s.clone()),
            (SecretValue::String(s), InjectionFormat::Base64Encoded) => {
                Ok(base64::encode(s.as_bytes()))
            }
            (SecretValue::Json(j), InjectionFormat::Json { field }) => {
                j.get(field)
                    .and_then(|v| v.as_str())
                    .map(|s| s.to_string())
                    .ok_or_else(|| anyhow!("Field '{}' not found in JSON secret", field))
            }
            (SecretValue::String(s), InjectionFormat::ConnectionString { template }) => {
                Ok(template.replace("{SECRET}", s))
            }
            (SecretValue::Binary(b), InjectionFormat::Base64Encoded) => {
                Ok(base64::encode(b))
            }
            _ => Err(anyhow!("Unsupported value/format combination")),
        }
    }
    
    pub async fn start_refresh_loop(&self, service_id: String) {
        let injector = self.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(300)); // 5 minutes
            
            loop {
                interval.tick().await;
                
                if let Err(e) = injector.inject_secrets(&service_id).await {
                    log::error!("Failed to refresh injected secrets: {}", e);
                }
            }
        });
    }
}

impl Clone for SecretInjector {
    fn clone(&self) -> Self {
        Self {
            secret_manager: Arc::clone(&self.secret_manager),
            injection_patterns: self.injection_patterns.clone(),
        }
    }
}
```

## Audit and Compliance

### Default Audit Logger Implementation
```rust
struct DefaultAuditLogger;

#[async_trait]
impl AuditLogger for DefaultAuditLogger {
    async fn log_secret_access(&self, event: SecretAccessEvent) {
        // In production, this would send to a dedicated audit system
        log::info!(
            target: "secret_audit",
            "SECRET_ACCESS: key={}, operation={:?}, service={}, user={:?}, success={}, error={:?}, timestamp={}",
            event.secret_key,
            event.operation,
            event.service_id,
            event.user_id,
            event.success,
            event.error,
            event.timestamp.to_rfc3339()
        );
    }
}

pub struct ComplianceReporter {
    secret_manager: Arc<SecretManager>,
    audit_events: Arc<RwLock<Vec<SecretAccessEvent>>>,
}

impl ComplianceReporter {
    pub async fn generate_compliance_report(&self, period: Duration) -> Result<ComplianceReport> {
        let end_time = Utc::now();
        let start_time = end_time - period;
        
        let events = self.audit_events.read().await;
        let relevant_events: Vec<_> = events
            .iter()
            .filter(|e| e.timestamp >= start_time && e.timestamp <= end_time)
            .collect();
            
        let total_accesses = relevant_events.len();
        let failed_accesses = relevant_events.iter().filter(|e| !e.success).count();
        let unique_secrets = relevant_events
            .iter()
            .map(|e| &e.secret_key)
            .collect::<std::collections::HashSet<_>>()
            .len();
        let unique_services = relevant_events
            .iter()
            .map(|e| &e.service_id)
            .collect::<std::collections::HashSet<_>>()
            .len();
            
        Ok(ComplianceReport {
            period_start: start_time,
            period_end: end_time,
            total_accesses,
            failed_accesses,
            success_rate: if total_accesses > 0 {
                (total_accesses - failed_accesses) as f64 / total_accesses as f64
            } else {
                1.0
            },
            unique_secrets_accessed: unique_secrets,
            unique_services: unique_services,
            policy_violations: self.detect_policy_violations(&relevant_events),
        })
    }
    
    fn detect_policy_violations(&self, events: &[&SecretAccessEvent]) -> Vec<PolicyViolation> {
        let mut violations = Vec::new();
        
        // Check for excessive access patterns
        let mut access_counts: HashMap<String, usize> = HashMap::new();
        for event in events {
            *access_counts.entry(event.secret_key.clone()).or_insert(0) += 1;
        }
        
        for (secret_key, count) in access_counts {
            if count > 1000 { // Arbitrary threshold
                violations.push(PolicyViolation {
                    violation_type: "excessive_access".to_string(),
                    description: format!("Secret '{}' accessed {} times", secret_key, count),
                    severity: ViolationSeverity::Medium,
                });
            }
        }
        
        // Check for access outside business hours
        for event in events {
            let hour = event.timestamp.hour();
            if hour < 6 || hour > 22 { // Outside 6 AM - 10 PM
                violations.push(PolicyViolation {
                    violation_type: "after_hours_access".to_string(),
                    description: format!("Secret '{}' accessed at {}", event.secret_key, event.timestamp),
                    severity: ViolationSeverity::Low,
                });
            }
        }
        
        violations
    }
}

#[derive(Debug, Serialize)]
pub struct ComplianceReport {
    pub period_start: DateTime<Utc>,
    pub period_end: DateTime<Utc>,
    pub total_accesses: usize,
    pub failed_accesses: usize,
    pub success_rate: f64,
    pub unique_secrets_accessed: usize,
    pub unique_services: usize,
    pub policy_violations: Vec<PolicyViolation>,
}

#[derive(Debug, Serialize)]
pub struct PolicyViolation {
    pub violation_type: String,
    pub description: String,
    pub severity: ViolationSeverity,
}

#[derive(Debug, Serialize)]
pub enum ViolationSeverity {
    Low,
    Medium,
    High,
    Critical,
}
```