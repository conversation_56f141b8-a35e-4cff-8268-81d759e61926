# Configuration Validation Schemas

## Type-Safe Configuration Definitions

### Application Configuration Schema
```rust
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};
use std::collections::HashMap;

#[derive(Debug, Deserialize, Serialize, Validate)]
pub struct ApplicationConfig {
    #[validate(nested)]
    pub server: ServerConfig,
    
    #[validate(nested)]
    pub database: DatabaseConfig,
    
    #[validate(nested)]
    pub messaging: MessagingConfig,
    
    #[validate(nested)]
    pub logging: LoggingConfig,
    
    #[validate(nested)]
    pub agents: AgentConfig,
    
    #[serde(default)]
    pub features: FeatureFlags,
    
    #[serde(default)]
    pub monitoring: MonitoringConfig,
}

#[derive(Debug, Deserialize, Serialize, Validate)]
pub struct ServerConfig {
    #[validate(length(min = 1))]
    pub host: String,
    
    #[validate(range(min = 1024, max = 65535))]
    pub port: u16,
    
    #[validate(range(min = 1, max = 1000))]
    pub workers: usize,
    
    #[validate(range(min = 1, max = 300))]
    pub timeout_seconds: u64,
    
    #[validate(range(min = 1024, max = 104857600))] // 1KB to 100MB
    pub max_request_size: usize,
    
    #[serde(default = "default_cors_origins")]
    pub cors_origins: Vec<String>,
    
    #[serde(default = "default_true")]
    pub enable_compression: bool,
}

#[derive(Debug, Deserialize, Serialize, Validate)]
pub struct DatabaseConfig {
    #[validate(url)]
    pub url: String,
    
    #[validate(range(min = 1, max = 100))]
    pub max_connections: u32,
    
    #[validate(range(min = 1, max = 100))]
    pub min_connections: u32,
    
    #[validate(range(min = 1, max = 300))]
    pub connection_timeout: u64,
    
    #[validate(range(min = 1, max = 3600))]
    pub idle_timeout: u64,
    
    #[validate(range(min = 1, max = 86400))]
    pub max_lifetime: u64,
    
    #[serde(default = "default_true")]
    pub enable_logging: bool,
    
    #[serde(default)]
    pub read_replicas: Vec<String>,
}

#[derive(Debug, Deserialize, Serialize, Validate)]
pub struct MessagingConfig {
    #[validate(length(min = 1))]
    pub provider: String, // "nats", "redis", "rabbitmq"
    
    #[validate(url)]
    pub url: String,
    
    #[validate(range(min = 1, max = 100))]
    pub max_connections: u32,
    
    #[validate(range(min = 1, max = 300))]
    pub connection_timeout: u64,
    
    #[validate(range(min = 1, max = 3600))]
    pub reconnect_timeout: u64,
    
    #[validate(range(min = 1, max = 1000000))]
    pub max_message_size: usize,
    
    #[serde(default)]
    pub subjects: SubjectConfig,
    
    #[serde(default)]
    pub tls: Option<TlsConfig>,
}

#[derive(Debug, Deserialize, Serialize, Validate, Default)]
pub struct SubjectConfig {
    #[validate(length(min = 1))]
    pub agent_commands: String,
    
    #[validate(length(min = 1))]
    pub agent_events: String,
    
    #[validate(length(min = 1))]
    pub task_queue: String,
    
    #[validate(length(min = 1))]
    pub metrics: String,
}

#[derive(Debug, Deserialize, Serialize, Validate)]
pub struct LoggingConfig {
    #[validate(custom = "validate_log_level")]
    pub level: String,
    
    #[validate(custom = "validate_log_format")]
    pub format: String, // "json", "pretty", "compact"
    
    #[serde(default = "default_true")]
    pub enable_colors: bool,
    
    pub file: Option<LogFileConfig>,
    
    #[serde(default)]
    pub filters: HashMap<String, String>,
}

#[derive(Debug, Deserialize, Serialize, Validate)]
pub struct LogFileConfig {
    #[validate(length(min = 1))]
    pub path: String,
    
    #[validate(range(min = 1024, max = 1073741824))] // 1KB to 1GB
    pub max_size: u64,
    
    #[validate(range(min = 1, max = 100))]
    pub max_files: u32,
    
    #[serde(default = "default_true")]
    pub compress: bool,
}

#[derive(Debug, Deserialize, Serialize, Validate)]
pub struct AgentConfig {
    #[validate(range(min = 1, max = 1000))]
    pub max_agents: usize,
    
    #[validate(range(min = 1, max = 3600))]
    pub default_timeout: u64,
    
    #[validate(range(min = 1, max = 100))]
    pub max_retries: u32,
    
    #[validate(range(min = 1, max = 10000))]
    pub task_queue_size: usize,
    
    #[serde(default)]
    pub types: HashMap<String, AgentTypeConfig>,
    
    #[serde(default)]
    pub resource_limits: ResourceLimits,
}

#[derive(Debug, Deserialize, Serialize, Validate)]
pub struct AgentTypeConfig {
    #[validate(range(min = 1, max = 100))]
    pub max_instances: usize,
    
    #[validate(range(min = 1, max = 3600))]
    pub timeout: u64,
    
    #[validate(range(min = 1, max = 10))]
    pub retries: u32,
    
    #[serde(default)]
    pub capabilities: Vec<String>,
    
    #[serde(default)]
    pub resources: ResourceLimits,
}

#[derive(Debug, Deserialize, Serialize, Validate, Default)]
pub struct ResourceLimits {
    #[validate(range(min = 1048576, max = 17179869184))] // 1MB to 16GB
    pub max_memory: Option<u64>,
    
    #[validate(range(min = 1, max = 100))]
    pub max_cpu_percent: Option<u32>,
    
    #[validate(range(min = 1, max = 86400))]
    pub max_execution_time: Option<u64>,
}

#[derive(Debug, Deserialize, Serialize, Default)]
pub struct FeatureFlags {
    pub enable_agent_metrics: bool,
    pub enable_task_caching: bool,
    pub enable_distributed_tracing: bool,
    pub enable_hot_reload: bool,
    pub enable_experimental_features: bool,
    pub maintenance_mode: bool,
}

#[derive(Debug, Deserialize, Serialize, Validate, Default)]
pub struct MonitoringConfig {
    #[serde(default = "default_true")]
    pub enable_metrics: bool,
    
    #[serde(default = "default_true")]
    pub enable_health_checks: bool,
    
    #[validate(range(min = 1, max = 3600))]
    #[serde(default = "default_metrics_interval")]
    pub metrics_interval: u64,
    
    #[validate(range(min = 1024, max = 65535))]
    #[serde(default = "default_metrics_port")]
    pub metrics_port: u16,
    
    #[serde(default)]
    pub prometheus: PrometheusConfig,
    
    #[serde(default)]
    pub jaeger: JaegerConfig,
}

#[derive(Debug, Deserialize, Serialize, Validate, Default)]
pub struct PrometheusConfig {
    #[serde(default = "default_true")]
    pub enabled: bool,
    
    #[validate(length(min = 1))]
    #[serde(default = "default_metrics_path")]
    pub path: String,
    
    #[serde(default)]
    pub labels: HashMap<String, String>,
}

#[derive(Debug, Deserialize, Serialize, Validate, Default)]
pub struct JaegerConfig {
    #[serde(default)]
    pub enabled: bool,
    
    #[validate(url)]
    pub endpoint: Option<String>,
    
    #[validate(range(min = 0.0, max = 1.0))]
    #[serde(default = "default_sample_rate")]
    pub sample_rate: f64,
    
    #[validate(length(min = 1))]
    #[serde(default = "default_service_name")]
    pub service_name: String,
}

#[derive(Debug, Deserialize, Serialize, Validate)]
pub struct TlsConfig {
    #[serde(default)]
    pub enabled: bool,
    
    #[validate(length(min = 1))]
    pub cert_file: Option<String>,
    
    #[validate(length(min = 1))]
    pub key_file: Option<String>,
    
    #[validate(length(min = 1))]
    pub ca_file: Option<String>,
    
    #[serde(default = "default_true")]
    pub verify_peer: bool,
}

// Default value functions
fn default_true() -> bool { true }
fn default_metrics_interval() -> u64 { 60 }
fn default_metrics_port() -> u16 { 9090 }
fn default_metrics_path() -> String { "/metrics".to_string() }
fn default_sample_rate() -> f64 { 0.1 }
fn default_service_name() -> String { "rust-agent-system".to_string() }
fn default_cors_origins() -> Vec<String> { vec!["*".to_string()] }

// Custom validation functions
fn validate_log_level(level: &str) -> Result<(), ValidationError> {
    match level.to_lowercase().as_str() {
        "trace" | "debug" | "info" | "warn" | "error" => Ok(()),
        _ => Err(ValidationError::new("invalid_log_level")),
    }
}

fn validate_log_format(format: &str) -> Result<(), ValidationError> {
    match format.to_lowercase().as_str() {
        "json" | "pretty" | "compact" => Ok(()),
        _ => Err(ValidationError::new("invalid_log_format")),
    }
}
```

## Configuration Validator Implementation

### Comprehensive Validation Engine
```rust
use std::sync::Arc;

#[async_trait]
pub trait ConfigValidator: Send + Sync {
    async fn validate(&self, config: &HashMap<String, CachedValue>) -> Result<ValidationReport>;
    async fn validate_partial(&self, key: &str, value: &ConfigValue) -> Result<()>;
}

pub struct ComprehensiveValidator {
    schema_registry: Arc<SchemaRegistry>,
    custom_validators: HashMap<String, Box<dyn CustomValidator>>,
    environment_rules: HashMap<String, EnvironmentRules>,
}

#[derive(Debug)]
pub struct ValidationReport {
    pub is_valid: bool,
    pub errors: Vec<ValidationError>,
    pub warnings: Vec<ValidationWarning>,
    pub suggestions: Vec<ValidationSuggestion>,
}

#[derive(Debug)]
pub struct ValidationError {
    pub key: String,
    pub message: String,
    pub error_type: ValidationErrorType,
    pub severity: ErrorSeverity,
}

#[derive(Debug)]
pub enum ValidationErrorType {
    TypeMismatch,
    RangeViolation,
    FormatInvalid,
    RequiredMissing,
    CustomValidationFailed,
    EnvironmentConstraintViolation,
}

#[derive(Debug)]
pub enum ErrorSeverity {
    Critical,  // Will prevent application start
    Major,     // Will cause feature degradation
    Minor,     // May cause suboptimal behavior
}

impl ComprehensiveValidator {
    pub fn new() -> Self {
        let mut validator = Self {
            schema_registry: Arc::new(SchemaRegistry::new()),
            custom_validators: HashMap::new(),
            environment_rules: HashMap::new(),
        };
        
        // Register built-in validators
        validator.register_builtin_validators();
        validator.register_environment_rules();
        
        validator
    }
    
    fn register_builtin_validators(&mut self) {
        // URL validator
        self.custom_validators.insert(
            "url".to_string(),
            Box::new(UrlValidator),
        );
        
        // Email validator
        self.custom_validators.insert(
            "email".to_string(),
            Box::new(EmailValidator),
        );
        
        // File path validator
        self.custom_validators.insert(
            "file_path".to_string(),
            Box::new(FilePathValidator),
        );
        
        // JSON schema validator
        self.custom_validators.insert(
            "json_schema".to_string(),
            Box::new(JsonSchemaValidator),
        );
        
        // Security validator
        self.custom_validators.insert(
            "security".to_string(),
            Box::new(SecurityValidator),
        );
    }
    
    fn register_environment_rules(&mut self) {
        // Production environment rules
        let production_rules = EnvironmentRules {
            required_tls: true,
            min_connection_timeout: 30,
            max_log_level: "info".to_string(),
            require_secrets_encryption: true,
            max_resource_limits: ResourceLimits {
                max_memory: Some(8 * 1024 * 1024 * 1024), // 8GB
                max_cpu_percent: Some(80),
                max_execution_time: Some(300), // 5 minutes
            },
        };
        
        self.environment_rules.insert("production".to_string(), production_rules);
        
        // Development environment rules
        let development_rules = EnvironmentRules {
            required_tls: false,
            min_connection_timeout: 5,
            max_log_level: "debug".to_string(),
            require_secrets_encryption: false,
            max_resource_limits: ResourceLimits {
                max_memory: Some(2 * 1024 * 1024 * 1024), // 2GB
                max_cpu_percent: Some(100),
                max_execution_time: Some(3600), // 1 hour
            },
        };
        
        self.environment_rules.insert("development".to_string(), development_rules);
    }
}

#[async_trait]
impl ConfigValidator for ComprehensiveValidator {
    async fn validate(&self, config: &HashMap<String, CachedValue>) -> Result<ValidationReport> {
        let mut report = ValidationReport {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
            suggestions: Vec::new(),
        };
        
        // Convert config to typed structure for validation
        let app_config = self.deserialize_config(config)?;
        
        // Run struct validation
        if let Err(validation_errors) = app_config.validate() {
            for error in validation_errors.field_errors() {
                for field_error in error.1 {
                    report.errors.push(ValidationError {
                        key: error.0.to_string(),
                        message: field_error.message
                            .as_ref()
                            .map(|m| m.to_string())
                            .unwrap_or_else(|| "Validation failed".to_string()),
                        error_type: ValidationErrorType::CustomValidationFailed,
                        severity: ErrorSeverity::Major,
                    });
                }
            }
        }
        
        // Run custom validations
        for (key, cached_value) in config {
            if let Err(e) = self.validate_custom_rules(key, &cached_value.value).await {
                report.errors.push(ValidationError {
                    key: key.clone(),
                    message: e.to_string(),
                    error_type: ValidationErrorType::CustomValidationFailed,
                    severity: ErrorSeverity::Major,
                });
            }
        }
        
        // Run environment-specific validations
        if let Some(env) = self.get_environment_from_config(config) {
            self.validate_environment_rules(&app_config, &env, &mut report).await;
        }
        
        // Run security validations
        self.validate_security_requirements(&app_config, &mut report).await;
        
        // Generate suggestions
        self.generate_optimization_suggestions(&app_config, &mut report).await;
        
        report.is_valid = report.errors.iter()
            .all(|e| !matches!(e.severity, ErrorSeverity::Critical));
        
        Ok(report)
    }
    
    async fn validate_partial(&self, key: &str, value: &ConfigValue) -> Result<()> {
        // Validate individual configuration value
        self.validate_custom_rules(key, value).await
    }
}

impl ComprehensiveValidator {
    async fn validate_custom_rules(&self, key: &str, value: &ConfigValue) -> Result<()> {
        // Check if any custom validators apply to this key
        for (validator_name, validator) in &self.custom_validators {
            if self.should_apply_validator(key, validator_name) {
                validator.validate(key, value).await?;
            }
        }
        
        Ok(())
    }
    
    fn should_apply_validator(&self, key: &str, validator_name: &str) -> bool {
        match validator_name {
            "url" => key.contains("url") || key.contains("endpoint"),
            "email" => key.contains("email"),
            "file_path" => key.contains("path") || key.contains("file"),
            "security" => key.contains("password") || key.contains("secret") || key.contains("token"),
            _ => false,
        }
    }
    
    async fn validate_environment_rules(
        &self,
        config: &ApplicationConfig,
        environment: &str,
        report: &mut ValidationReport,
    ) {
        if let Some(rules) = self.environment_rules.get(environment) {
            // Check TLS requirements
            if rules.required_tls {
                if let Some(ref tls) = config.messaging.tls {
                    if !tls.enabled {
                        report.errors.push(ValidationError {
                            key: "messaging.tls.enabled".to_string(),
                            message: format!("TLS is required in {} environment", environment),
                            error_type: ValidationErrorType::EnvironmentConstraintViolation,
                            severity: ErrorSeverity::Critical,
                        });
                    }
                } else {
                    report.errors.push(ValidationError {
                        key: "messaging.tls".to_string(),
                        message: format!("TLS configuration is required in {} environment", environment),
                        error_type: ValidationErrorType::RequiredMissing,
                        severity: ErrorSeverity::Critical,
                    });
                }
            }
            
            // Check log level restrictions
            let log_level_order = ["trace", "debug", "info", "warn", "error"];
            let max_level_index = log_level_order.iter()
                .position(|&level| level == rules.max_log_level)
                .unwrap_or(log_level_order.len() - 1);
            let current_level_index = log_level_order.iter()
                .position(|&level| level == config.logging.level)
                .unwrap_or(0);
                
            if current_level_index < max_level_index {
                report.warnings.push(ValidationWarning {
                    key: "logging.level".to_string(),
                    message: format!(
                        "Log level '{}' is more verbose than recommended '{}' for {} environment",
                        config.logging.level,
                        rules.max_log_level,
                        environment
                    ),
                });
            }
        }
    }
    
    async fn validate_security_requirements(
        &self,
        config: &ApplicationConfig,
        report: &mut ValidationReport,
    ) {
        // Check for insecure configurations
        if config.database.url.starts_with("http://") {
            report.errors.push(ValidationError {
                key: "database.url".to_string(),
                message: "Database URL should use secure protocol (https://)".to_string(),
                error_type: ValidationErrorType::FormatInvalid,
                severity: ErrorSeverity::Major,
            });
        }
        
        // Check for weak passwords (if any are configured)
        if config.database.url.contains("password=123") || 
           config.database.url.contains("password=admin") {
            report.errors.push(ValidationError {
                key: "database.url".to_string(),
                message: "Weak or default password detected in database URL".to_string(),
                error_type: ValidationErrorType::CustomValidationFailed,
                severity: ErrorSeverity::Critical,
            });
        }
        
        // Check CORS configuration
        if config.server.cors_origins.contains(&"*".to_string()) {
            report.warnings.push(ValidationWarning {
                key: "server.cors_origins".to_string(),
                message: "Wildcard CORS origin (*) allows all domains - consider restricting".to_string(),
            });
        }
    }
    
    async fn generate_optimization_suggestions(
        &self,
        config: &ApplicationConfig,
        report: &mut ValidationReport,
    ) {
        // Connection pool optimization
        if config.database.max_connections > 50 {
            report.suggestions.push(ValidationSuggestion {
                key: "database.max_connections".to_string(),
                message: "High connection count may impact database performance - consider connection pooling strategies".to_string(),
                optimization_type: OptimizationType::Performance,
            });
        }
        
        // Memory optimization
        for (agent_type, agent_config) in &config.agents.types {
            if let Some(max_memory) = agent_config.resources.max_memory {
                if max_memory > 4 * 1024 * 1024 * 1024 { // > 4GB
                    report.suggestions.push(ValidationSuggestion {
                        key: format!("agents.types.{}.resources.max_memory", agent_type),
                        message: format!("Agent type '{}' has high memory limit - monitor for memory leaks", agent_type),
                        optimization_type: OptimizationType::Resource,
                    });
                }
            }
        }
        
        // Feature flag optimization
        if !config.features.enable_task_caching {
            report.suggestions.push(ValidationSuggestion {
                key: "features.enable_task_caching".to_string(),
                message: "Task caching is disabled - enabling it may improve performance".to_string(),
                optimization_type: OptimizationType::Performance,
            });
        }
    }
}

#[derive(Debug)]
pub struct ValidationWarning {
    pub key: String,
    pub message: String,
}

#[derive(Debug)]
pub struct ValidationSuggestion {
    pub key: String,
    pub message: String,
    pub optimization_type: OptimizationType,
}

#[derive(Debug)]
pub enum OptimizationType {
    Performance,
    Security,
    Resource,
    Reliability,
}

struct EnvironmentRules {
    required_tls: bool,
    min_connection_timeout: u64,
    max_log_level: String,
    require_secrets_encryption: bool,
    max_resource_limits: ResourceLimits,
}
```

## Custom Validators

### Specialized Validation Implementations
```rust
#[async_trait]
pub trait CustomValidator: Send + Sync {
    async fn validate(&self, key: &str, value: &ConfigValue) -> Result<()>;
    fn validator_name(&self) -> &str;
}

pub struct UrlValidator;

#[async_trait]
impl CustomValidator for UrlValidator {
    async fn validate(&self, key: &str, value: &ConfigValue) -> Result<()> {
        if let ConfigValue::String(url_str) = value {
            match url::Url::parse(url_str) {
                Ok(url) => {
                    // Additional checks
                    if url.scheme() == "http" && key.contains("database") {
                        return Err(anyhow!("Database URLs should use secure protocols"));
                    }
                    
                    if url.host().is_none() {
                        return Err(anyhow!("URL must have a valid host"));
                    }
                    
                    Ok(())
                }
                Err(e) => Err(anyhow!("Invalid URL format: {}", e)),
            }
        } else {
            Err(anyhow!("URL value must be a string"))
        }
    }
    
    fn validator_name(&self) -> &str {
        "url"
    }
}

pub struct SecurityValidator;

#[async_trait]
impl CustomValidator for SecurityValidator {
    async fn validate(&self, key: &str, value: &ConfigValue) -> Result<()> {
        if let ConfigValue::String(secret_value) = value {
            // Check for common weak patterns
            let weak_patterns = [
                "123456", "password", "admin", "secret", "default",
                "changeme", "temp", "test", "demo"
            ];
            
            let lower_value = secret_value.to_lowercase();
            for pattern in &weak_patterns {
                if lower_value.contains(pattern) {
                    return Err(anyhow!(
                        "Weak or default value detected in security-sensitive field '{}'",
                        key
                    ));
                }
            }
            
            // Check minimum length for passwords/tokens
            if key.contains("password") || key.contains("token") {
                if secret_value.len() < 12 {
                    return Err(anyhow!(
                        "Security field '{}' should be at least 12 characters long",
                        key
                    ));
                }
            }
            
            // Check for hardcoded secrets (base64 patterns, etc.)
            if secret_value.len() > 20 && 
               secret_value.chars().all(|c| c.is_alphanumeric() || c == '+' || c == '/' || c == '=') &&
               !key.contains("template") {
                log::warn!("Possible hardcoded secret detected in field '{}'", key);
            }
            
            Ok(())
        } else {
            Ok(()) // Non-string values are okay for security validator
        }
    }
    
    fn validator_name(&self) -> &str {
        "security"
    }
}

pub struct FilePathValidator;

#[async_trait]
impl CustomValidator for FilePathValidator {
    async fn validate(&self, key: &str, value: &ConfigValue) -> Result<()> {
        if let ConfigValue::String(path_str) = value {
            let path = Path::new(path_str);
            
            // Check for absolute vs relative paths
            if key.contains("config") || key.contains("cert") || key.contains("key") {
                if !path.is_absolute() {
                    return Err(anyhow!(
                        "Path '{}' should be absolute for security and reliability",
                        path_str
                    ));
                }
            }
            
            // Check if parent directory exists (for output files)
            if key.contains("output") || key.contains("log") {
                if let Some(parent) = path.parent() {
                    if !parent.exists() {
                        return Err(anyhow!(
                            "Parent directory '{}' does not exist for path '{}'",
                            parent.display(),
                            path_str
                        ));
                    }
                }
            }
            
            // Check file permissions (for existing files)
            if path.exists() {
                let metadata = std::fs::metadata(path)?;
                
                if key.contains("cert") || key.contains("key") {
                    // Check that certificate/key files are not world-readable
                    #[cfg(unix)]
                    {
                        use std::os::unix::fs::PermissionsExt;
                        let permissions = metadata.permissions();
                        if permissions.mode() & 0o044 != 0 {
                            return Err(anyhow!(
                                "Certificate/key file '{}' should not be world-readable",
                                path_str
                            ));
                        }
                    }
                }
            }
            
            Ok(())
        } else {
            Err(anyhow!("File path value must be a string"))
        }
    }
    
    fn validator_name(&self) -> &str {
        "file_path"
    }
}

pub struct JsonSchemaValidator {
    schemas: HashMap<String, serde_json::Value>,
}

impl JsonSchemaValidator {
    pub fn new() -> Self {
        Self {
            schemas: HashMap::new(),
        }
    }
    
    pub fn register_schema(&mut self, key_pattern: String, schema: serde_json::Value) {
        self.schemas.insert(key_pattern, schema);
    }
}

#[async_trait]
impl CustomValidator for JsonSchemaValidator {
    async fn validate(&self, key: &str, value: &ConfigValue) -> Result<()> {
        // Find matching schema
        for (pattern, schema) in &self.schemas {
            if key.contains(pattern) {
                let json_value = self.config_value_to_json(value)?;
                
                // Validate against JSON schema
                // In a real implementation, you'd use a JSON schema validation library
                // like `jsonschema` or `valico`
                
                return Ok(());
            }
        }
        
        Ok(()) // No schema found, skip validation
    }
    
    fn validator_name(&self) -> &str {
        "json_schema"
    }
}

impl JsonSchemaValidator {
    fn config_value_to_json(&self, value: &ConfigValue) -> Result<serde_json::Value> {
        // Convert ConfigValue to serde_json::Value for schema validation
        match value {
            ConfigValue::String(s) => Ok(serde_json::Value::String(s.clone())),
            ConfigValue::Integer(i) => Ok(serde_json::Value::Number((*i).into())),
            ConfigValue::Float(f) => Ok(serde_json::Value::Number(
                serde_json::Number::from_f64(*f)
                    .ok_or_else(|| anyhow!("Invalid float"))?
            )),
            ConfigValue::Boolean(b) => Ok(serde_json::Value::Bool(*b)),
            ConfigValue::Array(arr) => {
                let json_arr: Result<Vec<_>> = arr
                    .iter()
                    .map(|v| self.config_value_to_json(v))
                    .collect();
                Ok(serde_json::Value::Array(json_arr?))
            }
            ConfigValue::Object(obj) => {
                let json_obj: Result<serde_json::Map<String, serde_json::Value>> = obj
                    .iter()
                    .map(|(k, v)| Ok((k.clone(), self.config_value_to_json(v)?)))
                    .collect();
                Ok(serde_json::Value::Object(json_obj?))
            }
            ConfigValue::Null => Ok(serde_json::Value::Null),
        }
    }
}
```