# Caching Configuration Examples

## TTL Map Configurations

### Default Configuration
```typescript
const defaultTTLConfig = {
  defaultTTL: 3600000,      // 1 hour default expiration
  cleanupInterval: 60000,   // Clean expired items every minute
  maxSize: 1000,           // Maximum 1000 entries
  onExpire: (key, value) => {
    console.log(`Cache entry expired: ${key}`);
  }
};
```

### Short-Lived Cache (API Responses)
```typescript
const apiCacheConfig = {
  defaultTTL: 300000,       // 5 minutes for API responses
  cleanupInterval: 30000,   // Frequent cleanup
  maxSize: 500,            // Smaller size for fresh data
  onExpire: (key, value) => {
    metrics.increment('cache.api.expired');
  }
};
```

### Long-Lived Cache (Static Resources)
```typescript
const staticCacheConfig = {
  defaultTTL: 86400000,     // 24 hours for static content
  cleanupInterval: 3600000, // Hourly cleanup
  maxSize: 10000,          // Large capacity
  onExpire: async (key, value) => {
    // Refresh static content in background
    await refreshStaticContent(key);
  }
};
```

### Session Cache
```typescript
const sessionCacheConfig = {
  defaultTTL: 1800000,      // 30 minutes session timeout
  cleanupInterval: 60000,   // Check every minute
  maxSize: 5000,           // Support many concurrent users
  onExpire: (sessionId, sessionData) => {
    // Clean up session resources
    cleanupUserSession(sessionId);
    auditLog.record('session.expired', { sessionId });
  }
};
```

## Memory Cache Configurations

### Development Environment
```typescript
const devMemoryCacheConfig = {
  maxSize: 10 * 1024 * 1024,  // 10MB for development
  logger: devLogger,
  enableMetrics: true,
  enableDebugMode: true,
  evictionThreshold: 0.8      // Evict at 80% capacity
};
```

### Production Environment
```typescript
const prodMemoryCacheConfig = {
  maxSize: 1024 * 1024 * 1024, // 1GB for production
  logger: prodLogger,
  enableMetrics: true,
  enableDebugMode: false,
  evictionThreshold: 0.9,      // Evict at 90% capacity
  persistDirtyInterval: 30000   // Persist dirty entries every 30s
};
```

### Memory-Constrained Environment
```typescript
const constrainedCacheConfig = {
  maxSize: 50 * 1024 * 1024,   // 50MB limit
  logger: logger,
  aggressiveEviction: true,     // More aggressive LRU
  evictionThreshold: 0.7,       // Earlier eviction
  compressEntries: true         // Compress large entries
};
```

## Executor Cache Integration

### High-Performance Configuration
```typescript
const executorConfig = {
  connectionPool: {
    min: 5,
    max: 20
  },
  caching: {
    enabled: true,
    ttl: 3600000,          // 1 hour cache TTL
    maxSize: 5000          // 5000 cached results
  },
  resultTransform: (result) => {
    // Strip non-cacheable data
    delete result.metadata.timestamp;
    return result;
  }
};
```

### Cost-Optimized Configuration
```typescript
const costOptimizedConfig = {
  caching: {
    enabled: true,
    ttl: 7200000,          // 2 hours - longer cache
    maxSize: 10000,        // More aggressive caching
    cacheKeyGenerator: (task) => {
      // Include only essential fields in cache key
      return `${task.type}:${task.description}`;
    }
  }
};
```

### Real-Time Configuration
```typescript
const realtimeConfig = {
  caching: {
    enabled: true,
    ttl: 60000,            // 1 minute - very short
    maxSize: 100,          // Small cache for real-time
    skipCache: (task) => {
      // Don't cache time-sensitive operations
      return task.metadata?.realtime === true;
    }
  }
};
```

## Multi-Level Cache Configuration

### Three-Tier Cache System
```typescript
const multiLevelConfig = {
  l1: {
    type: 'ttl-map',
    maxSize: 100,
    ttl: 300000,           // 5 minutes in L1
    name: 'hot-cache'
  },
  l2: {
    type: 'memory-cache',
    maxSize: 100 * 1024 * 1024, // 100MB in L2
    ttl: 3600000,          // 1 hour in L2
    name: 'warm-cache'
  },
  l3: {
    type: 'disk-cache',
    maxSize: 10 * 1024 * 1024 * 1024, // 10GB on disk
    ttl: 86400000,         // 24 hours in L3
    directory: './cache',
    name: 'cold-cache'
  }
};
```

### Cache Promotion Rules
```typescript
const promotionRules = {
  l2ToL1: {
    accessCount: 3,        // Promote after 3 accesses
    timeWindow: 300000,    // Within 5 minutes
    maxPromotions: 20      // Limit L1 pollution
  },
  l3ToL2: {
    accessCount: 2,        // Promote after 2 accesses
    timeWindow: 3600000,   // Within 1 hour
    sizeLimit: 1024 * 1024 // Only items < 1MB
  }
};
```

## Specialized Cache Configurations

### AI Model Response Cache
```typescript
const modelCacheConfig = {
  defaultTTL: 86400000,    // 24 hours for model responses
  maxSize: 1000,
  keyNormalization: (prompt) => {
    // Normalize prompts for better cache hits
    return prompt.toLowerCase().trim().replace(/\s+/g, ' ');
  },
  shouldCache: (response) => {
    // Only cache high-confidence responses
    return response.confidence > 0.9;
  },
  compressionEnabled: true  // Compress large responses
};
```

### Task Result Cache
```typescript
const taskCacheConfig = {
  defaultTTL: 3600000,     // 1 hour default
  maxSize: 5000,
  ttlByTaskType: {
    'analysis': 7200000,   // 2 hours for analysis
    'generation': 1800000, // 30 minutes for generation
    'validation': 300000   // 5 minutes for validation
  },
  keyGenerator: (task) => {
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify({
      type: task.type,
      description: task.description,
      context: task.context
    }));
    return hash.digest('hex');
  }
};
```

### Distributed Cache Configuration
```typescript
const distributedCacheConfig = {
  local: {
    ttl: 60000,            // 1 minute local cache
    maxSize: 100
  },
  remote: {
    type: 'redis',
    ttl: 3600000,          // 1 hour in Redis
    connection: {
      host: 'cache.example.com',
      port: 6379,
      password: process.env.REDIS_PASSWORD
    },
    keyPrefix: 'claude-flow:',
    enableCompression: true
  },
  synchronization: {
    strategy: 'write-through',
    conflictResolution: 'last-write-wins',
    syncInterval: 5000
  }
};
```

## Dynamic Configuration

### Load-Based Adjustment
```typescript
class DynamicCacheConfig {
  adjust(currentLoad: number) {
    if (currentLoad > 0.8) {
      // High load - reduce cache size, shorter TTL
      this.config.maxSize = Math.floor(this.config.maxSize * 0.8);
      this.config.defaultTTL = Math.floor(this.config.defaultTTL * 0.7);
    } else if (currentLoad < 0.3) {
      // Low load - increase cache size, longer TTL
      this.config.maxSize = Math.floor(this.config.maxSize * 1.2);
      this.config.defaultTTL = Math.floor(this.config.defaultTTL * 1.5);
    }
  }
}
```

### Time-Based Configuration
```typescript
function getTimeBasedCacheConfig() {
  const hour = new Date().getHours();
  
  if (hour >= 9 && hour <= 17) {
    // Business hours - optimize for performance
    return {
      defaultTTL: 1800000,    // 30 minutes
      maxSize: 10000,
      aggressiveCaching: true
    };
  } else if (hour >= 22 || hour <= 6) {
    // Night hours - optimize for cost
    return {
      defaultTTL: 7200000,    // 2 hours
      maxSize: 5000,
      aggressiveCaching: false
    };
  } else {
    // Normal hours
    return defaultCacheConfig;
  }
}
```

## Monitoring Configuration

### Cache Metrics Collection
```typescript
const metricsConfig = {
  enableMetrics: true,
  metricsInterval: 60000,    // Collect every minute
  metrics: {
    hitRate: true,
    missRate: true,
    evictionRate: true,
    avgAccessTime: true,
    memoryUsage: true,
    itemCount: true
  },
  thresholds: {
    lowHitRate: 0.5,         // Alert if hit rate < 50%
    highEvictionRate: 0.1,   // Alert if eviction > 10%
    highMemoryUsage: 0.9     // Alert if memory > 90%
  }
};
```

### Performance Tracking
```typescript
const performanceConfig = {
  trackAccessPatterns: true,
  sampleRate: 0.1,           // Sample 10% of requests
  reportInterval: 300000,    // Report every 5 minutes
  slowAccessThreshold: 10,   // Log accesses > 10ms
  topItemsCount: 100        // Track top 100 accessed items
};
```

## Cache Warming Strategies

### Startup Cache Warming
```typescript
const warmingConfig = {
  enabled: true,
  sources: [
    {
      type: 'file',
      path: './cache-dump.json',
      maxAge: 86400000      // Use if less than 24h old
    },
    {
      type: 'database',
      query: 'SELECT * FROM frequent_queries',
      limit: 1000
    },
    {
      type: 'api',
      endpoint: '/api/popular-items',
      transform: (data) => data.items
    }
  ],
  parallel: true,           // Warm in parallel
  priority: 'high'          // High priority loading
};
```

### Predictive Cache Warming
```typescript
const predictiveConfig = {
  enabled: true,
  model: 'time-series',
  lookbackPeriod: 7,        // 7 days of history
  predictionWindow: 3600000, // Predict next hour
  confidence: 0.8,          // Min confidence to pre-cache
  maxPredictions: 500       // Limit predictions
};
```