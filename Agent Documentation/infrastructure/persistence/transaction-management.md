# Transaction Management

## Transaction Isolation Levels

### Isolation Level Configuration
```rust
use sqlx::{IsolationLevel, PgPool, Postgres, Transaction};

pub struct TransactionManager {
    pool: Arc<PgPool>,
    default_isolation: IsolationLevel,
}

impl TransactionManager {
    pub async fn begin_with_isolation(
        &self,
        isolation: IsolationLevel,
    ) -> Result<Transaction<'static, Postgres>> {
        let mut tx = self.pool.begin().await?;
        
        let isolation_sql = match isolation {
            IsolationLevel::ReadUncommitted => "SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED",
            IsolationLevel::ReadCommitted => "SET TRANSACTION ISOLATION LEVEL READ COMMITTED",
            IsolationLevel::RepeatableRead => "SET TRANSACTION ISOLATION LEVEL REPEATABLE READ",
            IsolationLevel::Serializable => "SET TRANSACTION ISOLATION LEVEL SERIALIZABLE",
        };
        
        sqlx::query(isolation_sql).execute(&mut *tx).await?;
        
        Ok(tx)
    }
    
    pub async fn with_transaction<F, R>(&self, f: F) -> Result<R>
    where
        F: for<'a> FnOnce(&'a mut Transaction<'_, Postgres>) -> BoxFuture<'a, Result<R>>,
    {
        let mut tx = self.pool.begin().await?;
        
        match f(&mut tx).await {
            Ok(result) => {
                tx.commit().await?;
                Ok(result)
            }
            Err(e) => {
                tx.rollback().await?;
                Err(e)
            }
        }
    }
    
    pub async fn with_serializable_transaction<F, R>(&self, f: F) -> Result<R>
    where
        F: for<'a> FnOnce(&'a mut Transaction<'_, Postgres>) -> BoxFuture<'a, Result<R>>,
    {
        let mut retries = 0;
        const MAX_RETRIES: u32 = 3;
        
        loop {
            let mut tx = self.begin_with_isolation(IsolationLevel::Serializable).await?;
            
            match f(&mut tx).await {
                Ok(result) => {
                    match tx.commit().await {
                        Ok(_) => return Ok(result),
                        Err(e) if is_serialization_failure(&e) && retries < MAX_RETRIES => {
                            retries += 1;
                            log::warn!("Serialization failure, retry {}/{}", retries, MAX_RETRIES);
                            tokio::time::sleep(Duration::from_millis(100 * retries as u64)).await;
                            continue;
                        }
                        Err(e) => return Err(e.into()),
                    }
                }
                Err(e) => {
                    tx.rollback().await?;
                    if is_serialization_failure(&e) && retries < MAX_RETRIES {
                        retries += 1;
                        log::warn!("Serialization failure, retry {}/{}", retries, MAX_RETRIES);
                        tokio::time::sleep(Duration::from_millis(100 * retries as u64)).await;
                        continue;
                    }
                    return Err(e);
                }
            }
        }
    }
}

fn is_serialization_failure(error: &anyhow::Error) -> bool {
    error.to_string().contains("could not serialize access")
        || error.to_string().contains("deadlock detected")
}
```

## Distributed Transactions

### Two-Phase Commit (2PC)
```rust
use uuid::Uuid;

pub struct DistributedTransactionManager {
    coordinators: HashMap<String, Box<dyn TransactionCoordinator>>,
    transaction_log: Arc<TransactionLog>,
}

#[async_trait]
pub trait TransactionCoordinator: Send + Sync {
    async fn prepare(&self, tx_id: &str, operations: &[Operation]) -> Result<PrepareResult>;
    async fn commit(&self, tx_id: &str) -> Result<()>;
    async fn abort(&self, tx_id: &str) -> Result<()>;
    fn coordinator_id(&self) -> &str;
}

#[derive(Debug)]
pub enum PrepareResult {
    Prepared,
    ReadOnly,
    Aborted(String),
}

pub struct DistributedTransaction {
    id: String,
    coordinators: Vec<String>,
    status: TransactionStatus,
    operations: HashMap<String, Vec<Operation>>,
    prepared_coordinators: HashSet<String>,
}

#[derive(Debug, Clone)]
pub enum TransactionStatus {
    Active,
    Preparing,
    Prepared,
    Committing,
    Committed,
    Aborting,
    Aborted,
}

impl DistributedTransactionManager {
    pub async fn begin_distributed_transaction(
        &self,
        coordinators: Vec<String>,
    ) -> Result<DistributedTransaction> {
        let tx_id = Uuid::new_v4().to_string();
        
        let tx = DistributedTransaction {
            id: tx_id.clone(),
            coordinators: coordinators.clone(),
            status: TransactionStatus::Active,
            operations: HashMap::new(),
            prepared_coordinators: HashSet::new(),
        };
        
        // Log transaction start
        self.transaction_log.log_transaction_start(&tx_id, &coordinators).await?;
        
        Ok(tx)
    }
    
    pub async fn commit_distributed_transaction(
        &self,
        mut tx: DistributedTransaction,
    ) -> Result<()> {
        // Phase 1: Prepare
        tx.status = TransactionStatus::Preparing;
        self.transaction_log.log_status_change(&tx.id, &tx.status).await?;
        
        for coordinator_id in &tx.coordinators {
            let coordinator = self.coordinators
                .get(coordinator_id)
                .ok_or_else(|| anyhow!("Coordinator {} not found", coordinator_id))?;
                
            let operations = tx.operations
                .get(coordinator_id)
                .unwrap_or(&Vec::new());
                
            match coordinator.prepare(&tx.id, operations).await {
                Ok(PrepareResult::Prepared) => {
                    tx.prepared_coordinators.insert(coordinator_id.clone());
                    self.transaction_log.log_coordinator_prepared(&tx.id, coordinator_id).await?;
                }
                Ok(PrepareResult::ReadOnly) => {
                    // Read-only coordinators don't participate in commit phase
                    self.transaction_log.log_coordinator_readonly(&tx.id, coordinator_id).await?;
                }
                Ok(PrepareResult::Aborted(reason)) | Err(_) => {
                    // Abort transaction
                    self.abort_distributed_transaction(tx).await?;
                    return Err(anyhow!("Coordinator {} failed to prepare", coordinator_id));
                }
            }
        }
        
        // All coordinators prepared, proceed to commit
        tx.status = TransactionStatus::Committing;
        self.transaction_log.log_status_change(&tx.id, &tx.status).await?;
        
        // Phase 2: Commit
        for coordinator_id in &tx.prepared_coordinators {
            let coordinator = self.coordinators.get(coordinator_id).unwrap();
            
            if let Err(e) = coordinator.commit(&tx.id).await {
                log::error!("Coordinator {} failed to commit: {}", coordinator_id, e);
                // Note: In a real implementation, this requires recovery logic
            } else {
                self.transaction_log.log_coordinator_committed(&tx.id, coordinator_id).await?;
            }
        }
        
        tx.status = TransactionStatus::Committed;
        self.transaction_log.log_status_change(&tx.id, &tx.status).await?;
        
        Ok(())
    }
    
    async fn abort_distributed_transaction(&self, mut tx: DistributedTransaction) -> Result<()> {
        tx.status = TransactionStatus::Aborting;
        self.transaction_log.log_status_change(&tx.id, &tx.status).await?;
        
        for coordinator_id in &tx.prepared_coordinators {
            let coordinator = self.coordinators.get(coordinator_id).unwrap();
            
            if let Err(e) = coordinator.abort(&tx.id).await {
                log::error!("Coordinator {} failed to abort: {}", coordinator_id, e);
            } else {
                self.transaction_log.log_coordinator_aborted(&tx.id, coordinator_id).await?;
            }
        }
        
        tx.status = TransactionStatus::Aborted;
        self.transaction_log.log_status_change(&tx.id, &tx.status).await?;
        
        Ok(())
    }
}
```

### Saga Pattern
```rust
pub struct SagaOrchestrator {
    saga_store: Arc<SagaStore>,
    step_handlers: HashMap<String, Box<dyn SagaStepHandler>>,
}

#[async_trait]
pub trait SagaStepHandler: Send + Sync {
    async fn execute(&self, context: &SagaContext, data: &serde_json::Value) -> Result<serde_json::Value>;
    async fn compensate(&self, context: &SagaContext, data: &serde_json::Value) -> Result<()>;
    fn step_name(&self) -> &str;
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Saga {
    pub id: String,
    pub saga_type: String,
    pub status: SagaStatus,
    pub current_step: usize,
    pub steps: Vec<SagaStep>,
    pub context: SagaContext,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SagaStep {
    pub name: String,
    pub status: StepStatus,
    pub input_data: serde_json::Value,
    pub output_data: Option<serde_json::Value>,
    pub executed_at: Option<DateTime<Utc>>,
    pub compensated_at: Option<DateTime<Utc>>,
    pub retry_count: u32,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum SagaStatus {
    Running,
    Completed,
    Compensating,
    Compensated,
    Failed,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum StepStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Compensating,
    Compensated,
}

impl SagaOrchestrator {
    pub async fn start_saga(
        &self,
        saga_type: &str,
        steps: Vec<SagaStepDefinition>,
        initial_context: SagaContext,
    ) -> Result<String> {
        let saga_id = Uuid::new_v4().to_string();
        
        let saga_steps: Vec<SagaStep> = steps
            .into_iter()
            .map(|def| SagaStep {
                name: def.name,
                status: StepStatus::Pending,
                input_data: def.input_data,
                output_data: None,
                executed_at: None,
                compensated_at: None,
                retry_count: 0,
                error: None,
            })
            .collect();
            
        let saga = Saga {
            id: saga_id.clone(),
            saga_type: saga_type.to_string(),
            status: SagaStatus::Running,
            current_step: 0,
            steps: saga_steps,
            context: initial_context,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };
        
        self.saga_store.save(&saga).await?;
        
        // Start execution
        self.execute_saga(&saga_id).await?;
        
        Ok(saga_id)
    }
    
    pub async fn execute_saga(&self, saga_id: &str) -> Result<()> {
        let mut saga = self.saga_store
            .find_by_id(saga_id)
            .await?
            .ok_or_else(|| anyhow!("Saga not found"))?;
            
        while saga.current_step < saga.steps.len() && saga.status == SagaStatus::Running {
            let step = &mut saga.steps[saga.current_step];
            
            if step.status == StepStatus::Pending {
                step.status = StepStatus::Running;
                saga.updated_at = Utc::now();
                self.saga_store.save(&saga).await?;
                
                let handler = self.step_handlers
                    .get(&step.name)
                    .ok_or_else(|| anyhow!("Handler not found for step {}", step.name))?;
                    
                match handler.execute(&saga.context, &step.input_data).await {
                    Ok(output) => {
                        step.status = StepStatus::Completed;
                        step.output_data = Some(output);
                        step.executed_at = Some(Utc::now());
                        saga.current_step += 1;
                        
                        // Update context with step output
                        if let Some(ref output_data) = step.output_data {
                            saga.context.add_step_output(&step.name, output_data.clone());
                        }
                    }
                    Err(e) => {
                        step.error = Some(e.to_string());
                        step.retry_count += 1;
                        
                        if step.retry_count < 3 {
                            step.status = StepStatus::Pending;
                            log::warn!("Step {} failed, retrying: {}", step.name, e);
                        } else {
                            step.status = StepStatus::Failed;
                            saga.status = SagaStatus::Compensating;
                            log::error!("Step {} failed permanently: {}", step.name, e);
                            break;
                        }
                    }
                }
                
                saga.updated_at = Utc::now();
                self.saga_store.save(&saga).await?;
            }
        }
        
        // Check if saga completed
        if saga.current_step >= saga.steps.len() {
            saga.status = SagaStatus::Completed;
            saga.updated_at = Utc::now();
            self.saga_store.save(&saga).await?;
        } else if saga.status == SagaStatus::Compensating {
            self.compensate_saga(&mut saga).await?;
        }
        
        Ok(())
    }
    
    async fn compensate_saga(&self, saga: &mut Saga) -> Result<()> {
        // Compensate in reverse order
        for i in (0..saga.current_step).rev() {
            let step = &mut saga.steps[i];
            
            if step.status == StepStatus::Completed {
                step.status = StepStatus::Compensating;
                saga.updated_at = Utc::now();
                self.saga_store.save(saga).await?;
                
                let handler = self.step_handlers
                    .get(&step.name)
                    .ok_or_else(|| anyhow!("Handler not found for step {}", step.name))?;
                    
                match handler.compensate(&saga.context, &step.input_data).await {
                    Ok(_) => {
                        step.status = StepStatus::Compensated;
                        step.compensated_at = Some(Utc::now());
                    }
                    Err(e) => {
                        log::error!("Failed to compensate step {}: {}", step.name, e);
                        // Continue with other compensations
                        step.status = StepStatus::Failed;
                    }
                }
                
                saga.updated_at = Utc::now();
                self.saga_store.save(saga).await?;
            }
        }
        
        saga.status = SagaStatus::Compensated;
        saga.updated_at = Utc::now();
        self.saga_store.save(saga).await?;
        
        Ok(())
    }
}
```

## Connection Pooling

### Advanced Pool Configuration
```rust
use sqlx::pool::PoolOptions;
use sqlx::{PgPool, Postgres};

pub struct PoolManager {
    pools: HashMap<String, Arc<PgPool>>,
    config: PoolConfiguration,
}

#[derive(Clone)]
pub struct PoolConfiguration {
    pub min_connections: u32,
    pub max_connections: u32,
    pub connect_timeout: Duration,
    pub idle_timeout: Duration,
    pub max_lifetime: Duration,
    pub acquire_timeout: Duration,
    pub test_before_acquire: bool,
}

impl PoolManager {
    pub async fn create_pool(
        &mut self,
        name: String,
        database_url: String,
        config: Option<PoolConfiguration>,
    ) -> Result<Arc<PgPool>> {
        let pool_config = config.unwrap_or_else(|| self.config.clone());
        
        let pool = PoolOptions::<Postgres>::new()
            .min_connections(pool_config.min_connections)
            .max_connections(pool_config.max_connections)
            .connect_timeout(pool_config.connect_timeout)
            .idle_timeout(Some(pool_config.idle_timeout))
            .max_lifetime(Some(pool_config.max_lifetime))
            .acquire_timeout(pool_config.acquire_timeout)
            .test_before_acquire(pool_config.test_before_acquire)
            .after_connect(|conn, _meta| {
                Box::pin(async move {
                    // Set up connection-specific settings
                    sqlx::query("SET application_name = 'rust-agent-system'")
                        .execute(conn)
                        .await?;
                        
                    sqlx::query("SET timezone = 'UTC'")
                        .execute(conn)
                        .await?;
                        
                    Ok(())
                })
            })
            .connect(&database_url)
            .await?;
            
        let pool_arc = Arc::new(pool);
        self.pools.insert(name, pool_arc.clone());
        
        Ok(pool_arc)
    }
    
    pub fn get_pool(&self, name: &str) -> Option<Arc<PgPool>> {
        self.pools.get(name).cloned()
    }
    
    pub async fn health_check(&self) -> HashMap<String, PoolHealth> {
        let mut health_map = HashMap::new();
        
        for (name, pool) in &self.pools {
            let health = self.check_pool_health(pool).await;
            health_map.insert(name.clone(), health);
        }
        
        health_map
    }
    
    async fn check_pool_health(&self, pool: &PgPool) -> PoolHealth {
        let start = Instant::now();
        
        match sqlx::query("SELECT 1").fetch_one(pool).await {
            Ok(_) => PoolHealth {
                is_healthy: true,
                response_time: start.elapsed(),
                active_connections: pool.num_idle(),
                idle_connections: pool.num_idle(),
                error: None,
            },
            Err(e) => PoolHealth {
                is_healthy: false,
                response_time: start.elapsed(),
                active_connections: 0,
                idle_connections: 0,
                error: Some(e.to_string()),
            },
        }
    }
}

#[derive(Debug)]
pub struct PoolHealth {
    pub is_healthy: bool,
    pub response_time: Duration,
    pub active_connections: usize,
    pub idle_connections: usize,
    pub error: Option<String>,
}
```

### Read/Write Connection Splitting
```rust
pub struct DatabaseCluster {
    write_pool: Arc<PgPool>,
    read_pools: Vec<Arc<PgPool>>,
    read_strategy: ReadStrategy,
    current_read_index: AtomicUsize,
}

#[derive(Clone, Copy)]
pub enum ReadStrategy {
    RoundRobin,
    Random,
    LeastConnections,
}

impl DatabaseCluster {
    pub fn new(
        write_pool: Arc<PgPool>,
        read_pools: Vec<Arc<PgPool>>,
        read_strategy: ReadStrategy,
    ) -> Self {
        Self {
            write_pool,
            read_pools,
            read_strategy,
            current_read_index: AtomicUsize::new(0),
        }
    }
    
    pub fn get_write_pool(&self) -> Arc<PgPool> {
        self.write_pool.clone()
    }
    
    pub fn get_read_pool(&self) -> Arc<PgPool> {
        if self.read_pools.is_empty() {
            return self.write_pool.clone();
        }
        
        let index = match self.read_strategy {
            ReadStrategy::RoundRobin => {
                self.current_read_index.fetch_add(1, Ordering::Relaxed) % self.read_pools.len()
            }
            ReadStrategy::Random => {
                rand::random::<usize>() % self.read_pools.len()
            }
            ReadStrategy::LeastConnections => {
                self.find_least_connections_pool()
            }
        };
        
        self.read_pools[index].clone()
    }
    
    fn find_least_connections_pool(&self) -> usize {
        self.read_pools
            .iter()
            .enumerate()
            .min_by_key(|(_, pool)| pool.num_idle())
            .map(|(index, _)| index)
            .unwrap_or(0)
    }
    
    pub async fn with_read_transaction<F, R>(&self, f: F) -> Result<R>
    where
        F: for<'a> FnOnce(&'a mut Transaction<'_, Postgres>) -> BoxFuture<'a, Result<R>>,
    {
        let pool = self.get_read_pool();
        let mut tx = pool.begin().await?;
        
        // Set transaction to read-only
        sqlx::query("SET TRANSACTION READ ONLY")
            .execute(&mut *tx)
            .await?;
        
        match f(&mut tx).await {
            Ok(result) => {
                tx.commit().await?;
                Ok(result)
            }
            Err(e) => {
                tx.rollback().await?;
                Err(e)
            }
        }
    }
    
    pub async fn with_write_transaction<F, R>(&self, f: F) -> Result<R>
    where
        F: for<'a> FnOnce(&'a mut Transaction<'_, Postgres>) -> BoxFuture<'a, Result<R>>,
    {
        let pool = self.get_write_pool();
        let mut tx = pool.begin().await?;
        
        match f(&mut tx).await {
            Ok(result) => {
                tx.commit().await?;
                Ok(result)
            }
            Err(e) => {
                tx.rollback().await?;
                Err(e)
            }
        }
    }
}
```

## Optimistic Locking

### Version-Based Concurrency Control
```rust
#[derive(Debug, Serialize, Deserialize)]
pub struct VersionedEntity {
    pub id: String,
    pub version: i64,
    pub data: serde_json::Value,
    pub updated_at: DateTime<Utc>,
}

pub struct OptimisticLockingRepository {
    pool: Arc<PgPool>,
}

impl OptimisticLockingRepository {
    pub async fn save_with_version_check(
        &self,
        entity: &VersionedEntity,
        expected_version: i64,
    ) -> Result<VersionedEntity> {
        let mut tx = self.pool.begin().await?;
        
        // Check current version
        let current_version = sqlx::query_scalar!(
            "SELECT version FROM versioned_entities WHERE id = $1",
            entity.id
        )
        .fetch_optional(&mut *tx)
        .await?;
        
        match current_version {
            Some(version) if version != expected_version => {
                return Err(anyhow!(
                    "Optimistic locking conflict: expected version {}, found {}",
                    expected_version,
                    version
                ));
            }
            None if expected_version != 0 => {
                return Err(anyhow!(
                    "Entity does not exist, expected version should be 0"
                ));
            }
            _ => {}
        }
        
        let new_version = expected_version + 1;
        let now = Utc::now();
        
        let updated_entity = if expected_version == 0 {
            // Insert new entity
            sqlx::query_as!(
                VersionedEntity,
                r#"
                INSERT INTO versioned_entities (id, version, data, updated_at)
                VALUES ($1, $2, $3, $4)
                RETURNING *
                "#,
                entity.id,
                new_version,
                entity.data,
                now
            )
            .fetch_one(&mut *tx)
            .await?
        } else {
            // Update existing entity
            sqlx::query_as!(
                VersionedEntity,
                r#"
                UPDATE versioned_entities 
                SET version = $2, data = $3, updated_at = $4
                WHERE id = $1
                RETURNING *
                "#,
                entity.id,
                new_version,
                entity.data,
                now
            )
            .fetch_one(&mut *tx)
            .await?
        };
        
        tx.commit().await?;
        Ok(updated_entity)
    }
    
    pub async fn save_with_retry(
        &self,
        mut entity: VersionedEntity,
        max_retries: u32,
    ) -> Result<VersionedEntity> {
        let mut retries = 0;
        
        loop {
            let current_version = entity.version;
            
            match self.save_with_version_check(&entity, current_version).await {
                Ok(saved_entity) => return Ok(saved_entity),
                Err(e) if retries < max_retries && e.to_string().contains("Optimistic locking conflict") => {
                    retries += 1;
                    
                    // Fetch latest version
                    let latest = self.find_by_id(&entity.id).await?
                        .ok_or_else(|| anyhow!("Entity was deleted during retry"))?;
                        
                    entity.version = latest.version;
                    
                    // Wait before retry
                    tokio::time::sleep(Duration::from_millis(100 * retries as u64)).await;
                }
                Err(e) => return Err(e),
            }
        }
    }
}
```

## Pessimistic Locking

### Row-Level Locking
```rust
pub struct PessimisticLockingRepository {
    pool: Arc<PgPool>,
}

impl PessimisticLockingRepository {
    pub async fn find_and_lock_for_update(
        &self,
        tx: &mut Transaction<'_, Postgres>,
        id: &str,
    ) -> Result<Option<VersionedEntity>> {
        let entity = sqlx::query_as!(
            VersionedEntity,
            "SELECT * FROM versioned_entities WHERE id = $1 FOR UPDATE",
            id
        )
        .fetch_optional(&mut **tx)
        .await?;
        
        Ok(entity)
    }
    
    pub async fn find_and_lock_for_share(
        &self,
        tx: &mut Transaction<'_, Postgres>,
        id: &str,
    ) -> Result<Option<VersionedEntity>> {
        let entity = sqlx::query_as!(
            VersionedEntity,
            "SELECT * FROM versioned_entities WHERE id = $1 FOR SHARE",
            id
        )
        .fetch_optional(&mut **tx)
        .await?;
        
        Ok(entity)
    }
    
    pub async fn with_exclusive_lock<F, R>(
        &self,
        id: &str,
        timeout: Duration,
        f: F,
    ) -> Result<R>
    where
        F: for<'a> FnOnce(&'a mut VersionedEntity, &'a mut Transaction<'_, Postgres>) -> BoxFuture<'a, Result<R>>,
    {
        let mut tx = self.pool.begin().await?;
        
        // Set lock timeout
        sqlx::query(&format!("SET lock_timeout = {}", timeout.as_millis()))
            .execute(&mut *tx)
            .await?;
        
        // Lock the entity
        let mut entity = self
            .find_and_lock_for_update(&mut tx, id)
            .await?
            .ok_or_else(|| anyhow!("Entity not found"))?;
        
        // Execute the operation
        let result = f(&mut entity, &mut tx).await?;
        
        // Save changes
        entity.version += 1;
        entity.updated_at = Utc::now();
        
        sqlx::query!(
            r#"
            UPDATE versioned_entities 
            SET version = $2, data = $3, updated_at = $4
            WHERE id = $1
            "#,
            entity.id,
            entity.version,
            entity.data,
            entity.updated_at
        )
        .execute(&mut *tx)
        .await?;
        
        tx.commit().await?;
        Ok(result)
    }
}
```

## Batch Operations

### Efficient Bulk Operations
```rust
pub struct BatchProcessor {
    pool: Arc<PgPool>,
    batch_size: usize,
}

impl BatchProcessor {
    pub async fn bulk_insert<T>(&self, entities: Vec<T>) -> Result<u64>
    where
        T: Serialize + Send + Sync,
    {
        let mut total_inserted = 0;
        
        for chunk in entities.chunks(self.batch_size) {
            let inserted = self.insert_batch(chunk).await?;
            total_inserted += inserted;
        }
        
        Ok(total_inserted)
    }
    
    async fn insert_batch<T>(&self, batch: &[T]) -> Result<u64>
    where
        T: Serialize + Send + Sync,
    {
        let mut tx = self.pool.begin().await?;
        
        // Build dynamic query
        let mut query_builder = QueryBuilder::new("INSERT INTO entities (data) ");
        query_builder.push_values(batch, |mut b, entity| {
            b.push_bind(serde_json::to_value(entity).unwrap());
        });
        
        let query = query_builder.build();
        let result = query.execute(&mut *tx).await?;
        
        tx.commit().await?;
        Ok(result.rows_affected())
    }
    
    pub async fn bulk_update(
        &self,
        updates: Vec<(String, serde_json::Value)>,
    ) -> Result<u64> {
        let mut tx = self.pool.begin().await?;
        let mut total_updated = 0;
        
        for chunk in updates.chunks(self.batch_size) {
            // Use temporary table for bulk updates
            sqlx::query!(
                r#"
                CREATE TEMPORARY TABLE temp_updates (
                    id TEXT,
                    data JSONB
                ) ON COMMIT DROP
                "#
            )
            .execute(&mut *tx)
            .await?;
            
            // Insert data into temp table
            for (id, data) in chunk {
                sqlx::query!(
                    "INSERT INTO temp_updates (id, data) VALUES ($1, $2)",
                    id,
                    data
                )
                .execute(&mut *tx)
                .await?;
            }
            
            // Perform bulk update
            let result = sqlx::query!(
                r#"
                UPDATE entities 
                SET data = temp_updates.data,
                    updated_at = NOW()
                FROM temp_updates 
                WHERE entities.id = temp_updates.id
                "#
            )
            .execute(&mut *tx)
            .await?;
            
            total_updated += result.rows_affected();
        }
        
        tx.commit().await?;
        Ok(total_updated)
    }
}
```