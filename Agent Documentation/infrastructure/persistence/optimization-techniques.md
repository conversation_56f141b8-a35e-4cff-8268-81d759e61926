# Optimization Techniques

## Query Optimization

### Query Builder with Performance Hints
```rust
use sqlx::query_builder::QueryBuilder;

pub struct OptimizedQueryBuilder {
    table_name: String,
    indexes: HashMap<String, IndexInfo>,
    statistics: Arc<QueryStatistics>,
}

#[derive(<PERSON>bu<PERSON>, Clone)]
pub struct IndexInfo {
    pub name: String,
    pub columns: Vec<String>,
    pub index_type: IndexType,
    pub is_unique: bool,
    pub selectivity: f64,
}

#[derive(Debug, Clone)]
pub enum IndexType {
    BTree,
    Hash,
    Gin,
    Gist,
    Partial(String), // condition
}

impl OptimizedQueryBuilder {
    pub fn new(table_name: String) -> Self {
        Self {
            table_name,
            indexes: HashMap::new(),
            statistics: Arc::new(QueryStatistics::new()),
        }
    }
    
    pub fn build_optimized_query(&self, criteria: &QueryCriteria) -> (String, Vec<QueryValue>) {
        let mut query_builder = QueryBuilder::new(&format!("SELECT * FROM {}", self.table_name));
        let mut params = Vec::new();
        
        // Analyze query and suggest index usage
        if let Some(best_index) = self.find_best_index(criteria) {
            log::debug!("Using index {} for query", best_index.name);
        }
        
        // Build WHERE clause with optimized order
        if !criteria.conditions.is_empty() {
            query_builder.push(" WHERE ");
            
            // Sort conditions by selectivity (most selective first)
            let mut sorted_conditions = criteria.conditions.clone();
            sorted_conditions.sort_by(|a, b| {
                self.get_condition_selectivity(a)
                    .partial_cmp(&self.get_condition_selectivity(b))
                    .unwrap_or(std::cmp::Ordering::Equal)
            });
            
            let where_clauses: Vec<String> = sorted_conditions
                .iter()
                .enumerate()
                .map(|(i, condition)| {
                    if i > 0 {
                        query_builder.push(" AND ");
                    }
                    
                    params.push(condition.value.clone());
                    self.build_condition_clause(condition, params.len())
                })
                .collect();
        }
        
        // Add ORDER BY with index consideration
        if !criteria.sort_orders.is_empty() {
            query_builder.push(" ORDER BY ");
            
            let order_clauses: Vec<String> = criteria.sort_orders
                .iter()
                .map(|order| {
                    // Check if we can use index for sorting
                    if self.can_use_index_for_sorting(&order.field) {
                        format!("{} {}", order.field, order.direction)
                    } else {
                        log::warn!("No index available for sorting by {}", order.field);
                        format!("{} {}", order.field, order.direction)
                    }
                })
                .collect();
                
            query_builder.push(&order_clauses.join(", "));
        }
        
        // Add LIMIT and OFFSET
        if let Some(limit) = criteria.limit {
            query_builder.push(&format!(" LIMIT {}", limit));
        }
        
        if let Some(offset) = criteria.offset {
            query_builder.push(&format!(" OFFSET {}", offset));
        }
        
        (query_builder.sql().to_string(), params)
    }
    
    fn find_best_index(&self, criteria: &QueryCriteria) -> Option<&IndexInfo> {
        let query_columns: HashSet<String> = criteria.conditions
            .iter()
            .map(|c| c.field.clone())
            .collect();
            
        self.indexes
            .values()
            .filter(|index| {
                // Check if index columns overlap with query columns
                index.columns.iter().any(|col| query_columns.contains(col))
            })
            .max_by(|a, b| {
                // Score based on column overlap and selectivity
                let a_score = self.calculate_index_score(a, &query_columns);
                let b_score = self.calculate_index_score(b, &query_columns);
                a_score.partial_cmp(&b_score).unwrap_or(std::cmp::Ordering::Equal)
            })
    }
    
    fn calculate_index_score(&self, index: &IndexInfo, query_columns: &HashSet<String>) -> f64 {
        let overlap = index.columns
            .iter()
            .filter(|col| query_columns.contains(*col))
            .count() as f64;
            
        let column_ratio = overlap / index.columns.len() as f64;
        
        // Higher selectivity and better column coverage = higher score
        index.selectivity * column_ratio
    }
    
    fn get_condition_selectivity(&self, condition: &Condition) -> f64 {
        // Estimate selectivity based on operator and field statistics
        match condition.operator {
            Operator::Equals => 0.1,           // High selectivity
            Operator::GreaterThan => 0.33,     // Medium selectivity
            Operator::LessThan => 0.33,        // Medium selectivity
            Operator::Like => 0.5,             // Low selectivity
            Operator::In => 0.2,               // Depends on array size
            _ => 0.5,                          // Default
        }
    }
}
```

### Query Plan Analysis
```rust
use sqlx::PgPool;

pub struct QueryPlanAnalyzer {
    pool: Arc<PgPool>,
    plan_cache: Arc<RwLock<HashMap<String, QueryPlan>>>,
}

#[derive(Debug, Clone)]
pub struct QueryPlan {
    pub query_hash: String,
    pub plan_json: serde_json::Value,
    pub total_cost: f64,
    pub startup_cost: f64,
    pub rows: i64,
    pub width: i32,
    pub actual_time: Option<f64>,
    pub node_type: String,
    pub index_usage: Vec<String>,
    pub warnings: Vec<String>,
}

impl QueryPlanAnalyzer {
    pub async fn analyze_query(&self, query: &str, params: &[QueryValue]) -> Result<QueryPlan> {
        let query_hash = self.calculate_query_hash(query);
        
        // Check cache first
        if let Some(cached_plan) = self.plan_cache.read().await.get(&query_hash) {
            return Ok(cached_plan.clone());
        }
        
        // Get query plan from database
        let explain_query = format!("EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {}", query);
        
        let plan_result = sqlx::query_scalar::<_, serde_json::Value>(&explain_query)
            .fetch_one(&*self.pool)
            .await?;
            
        let plan = self.parse_query_plan(query_hash.clone(), plan_result)?;
        
        // Cache the plan
        self.plan_cache.write().await.insert(query_hash, plan.clone());
        
        Ok(plan)
    }
    
    fn parse_query_plan(&self, query_hash: String, plan_json: serde_json::Value) -> Result<QueryPlan> {
        let plan_array = plan_json.as_array()
            .ok_or_else(|| anyhow!("Invalid plan format"))?;
            
        let root_plan = &plan_array[0]["Plan"];
        
        let mut warnings = Vec::new();
        let mut index_usage = Vec::new();
        
        // Extract index usage
        self.extract_index_usage(root_plan, &mut index_usage);
        
        // Check for performance warnings
        self.check_performance_warnings(root_plan, &mut warnings);
        
        Ok(QueryPlan {
            query_hash,
            plan_json: root_plan.clone(),
            total_cost: root_plan["Total Cost"].as_f64().unwrap_or(0.0),
            startup_cost: root_plan["Startup Cost"].as_f64().unwrap_or(0.0),
            rows: root_plan["Plan Rows"].as_i64().unwrap_or(0),
            width: root_plan["Plan Width"].as_i32().unwrap_or(0),
            actual_time: root_plan["Actual Total Time"].as_f64(),
            node_type: root_plan["Node Type"].as_str().unwrap_or("Unknown").to_string(),
            index_usage,
            warnings,
        })
    }
    
    fn extract_index_usage(&self, node: &serde_json::Value, indexes: &mut Vec<String>) {
        if let Some(index_name) = node.get("Index Name").and_then(|v| v.as_str()) {
            indexes.push(index_name.to_string());
        }
        
        // Recursively check child nodes
        if let Some(plans) = node.get("Plans").and_then(|v| v.as_array()) {
            for child_plan in plans {
                self.extract_index_usage(child_plan, indexes);
            }
        }
    }
    
    fn check_performance_warnings(&self, node: &serde_json::Value, warnings: &mut Vec<String>) {
        let node_type = node["Node Type"].as_str().unwrap_or("");
        
        // Check for sequential scans on large tables
        if node_type == "Seq Scan" {
            if let Some(rows) = node["Plan Rows"].as_i64() {
                if rows > 10000 {
                    warnings.push(format!(
                        "Sequential scan on large table ({} rows estimated)",
                        rows
                    ));
                }
            }
        }
        
        // Check for expensive sorts
        if node_type == "Sort" {
            if let Some(cost) = node["Total Cost"].as_f64() {
                if cost > 1000.0 {
                    warnings.push(format!("Expensive sort operation (cost: {})", cost));
                }
            }
        }
        
        // Check for hash joins with large hash tables
        if node_type == "Hash Join" {
            if let Some(hash_buckets) = node.get("Hash Buckets").and_then(|v| v.as_i64()) {
                if hash_buckets > 100000 {
                    warnings.push("Large hash table in join".to_string());
                }
            }
        }
        
        // Recursively check child nodes
        if let Some(plans) = node.get("Plans").and_then(|v| v.as_array()) {
            for child_plan in plans {
                self.check_performance_warnings(child_plan, warnings);
            }
        }
    }
    
    pub async fn suggest_optimizations(&self, plan: &QueryPlan) -> Vec<OptimizationSuggestion> {
        let mut suggestions = Vec::new();
        
        // Analyze warnings and suggest fixes
        for warning in &plan.warnings {
            if warning.contains("Sequential scan") {
                suggestions.push(OptimizationSuggestion {
                    suggestion_type: OptimizationType::AddIndex,
                    description: "Consider adding an index to avoid sequential scan".to_string(),
                    estimated_improvement: 0.7,
                    complexity: OptimizationComplexity::Low,
                });
            }
            
            if warning.contains("Expensive sort") {
                suggestions.push(OptimizationSuggestion {
                    suggestion_type: OptimizationType::AddSortIndex,
                    description: "Add index to support ORDER BY clause".to_string(),
                    estimated_improvement: 0.5,
                    complexity: OptimizationComplexity::Medium,
                });
            }
        }
        
        // Check for missing indexes
        if plan.index_usage.is_empty() && plan.total_cost > 100.0 {
            suggestions.push(OptimizationSuggestion {
                suggestion_type: OptimizationType::AddIndex,
                description: "Query not using any indexes".to_string(),
                estimated_improvement: 0.8,
                complexity: OptimizationComplexity::Low,
            });
        }
        
        suggestions
    }
}

#[derive(Debug)]
pub struct OptimizationSuggestion {
    pub suggestion_type: OptimizationType,
    pub description: String,
    pub estimated_improvement: f64, // 0.0 to 1.0
    pub complexity: OptimizationComplexity,
}

#[derive(Debug)]
pub enum OptimizationType {
    AddIndex,
    AddSortIndex,
    RewriteQuery,
    PartitionTable,
    UpdateStatistics,
}

#[derive(Debug)]
pub enum OptimizationComplexity {
    Low,
    Medium,
    High,
}
```

## Index Management

### Dynamic Index Creation
```rust
pub struct IndexManager {
    pool: Arc<PgPool>,
    query_analyzer: Arc<QueryPlanAnalyzer>,
    index_usage_stats: Arc<RwLock<HashMap<String, IndexUsageStats>>>,
}

#[derive(Debug, Clone)]
pub struct IndexUsageStats {
    pub index_name: String,
    pub table_name: String,
    pub scan_count: u64,
    pub tuple_read: u64,
    pub tuple_fetch: u64,
    pub last_used: Option<DateTime<Utc>>,
    pub size_bytes: i64,
    pub efficiency_score: f64,
}

impl IndexManager {
    pub async fn analyze_index_usage(&self) -> Result<Vec<IndexUsageStats>> {
        let stats = sqlx::query!(
            r#"
            SELECT 
                schemaname,
                tablename,
                indexname,
                idx_scan,
                idx_tup_read,
                idx_tup_fetch,
                pg_size_pretty(pg_relation_size(indexrelid)) as size,
                pg_relation_size(indexrelid) as size_bytes
            FROM pg_stat_user_indexes
            ORDER BY idx_scan DESC
            "#
        )
        .fetch_all(&*self.pool)
        .await?;
        
        let mut usage_stats = Vec::new();
        
        for stat in stats {
            let efficiency_score = if stat.idx_scan > 0 {
                stat.idx_tup_fetch as f64 / stat.idx_scan as f64
            } else {
                0.0
            };
            
            usage_stats.push(IndexUsageStats {
                index_name: stat.indexname,
                table_name: stat.tablename,
                scan_count: stat.idx_scan as u64,
                tuple_read: stat.idx_tup_read as u64,
                tuple_fetch: stat.idx_tup_fetch as u64,
                last_used: None, // Would need pg_stat_reset_time
                size_bytes: stat.size_bytes,
                efficiency_score,
            });
        }
        
        Ok(usage_stats)
    }
    
    pub async fn suggest_new_indexes(&self, query_patterns: Vec<QueryPattern>) -> Result<Vec<IndexSuggestion>> {
        let mut suggestions = Vec::new();
        
        for pattern in query_patterns {
            // Analyze query pattern for index opportunities
            if let Some(suggestion) = self.analyze_query_pattern(&pattern).await? {
                suggestions.push(suggestion);
            }
        }
        
        // Remove duplicate suggestions
        suggestions.dedup_by(|a, b| a.table_name == b.table_name && a.columns == b.columns);
        
        Ok(suggestions)
    }
    
    async fn analyze_query_pattern(&self, pattern: &QueryPattern) -> Result<Option<IndexSuggestion>> {
        // Simulate the query to get execution plan
        let plan = self.query_analyzer
            .analyze_query(&pattern.query_template, &[])
            .await?;
            
        // Check if query would benefit from an index
        if plan.warnings.iter().any(|w| w.contains("Sequential scan")) {
            let columns = pattern.filter_columns.clone();
            
            if !columns.is_empty() {
                return Ok(Some(IndexSuggestion {
                    table_name: pattern.table_name.clone(),
                    columns,
                    index_type: IndexType::BTree,
                    estimated_benefit: self.calculate_estimated_benefit(&plan),
                    creation_cost: self.estimate_creation_cost(&pattern.table_name, &pattern.filter_columns).await?,
                }));
            }
        }
        
        Ok(None)
    }
    
    fn calculate_estimated_benefit(&self, plan: &QueryPlan) -> f64 {
        // Estimate performance improvement
        if plan.total_cost > 1000.0 {
            0.8 // High benefit for expensive queries
        } else if plan.total_cost > 100.0 {
            0.5 // Medium benefit
        } else {
            0.2 // Low benefit
        }
    }
    
    async fn estimate_creation_cost(&self, table_name: &str, columns: &[String]) -> Result<CreationCost> {
        // Get table size and row count
        let table_stats = sqlx::query!(
            r#"
            SELECT 
                pg_size_pretty(pg_total_relation_size($1)) as size,
                pg_total_relation_size($1) as size_bytes,
                reltuples::BIGINT as row_count
            FROM pg_class 
            WHERE relname = $1
            "#,
            table_name
        )
        .fetch_one(&*self.pool)
        .await?;
        
        let estimated_time = if table_stats.size_bytes > 1024 * 1024 * 1024 { // > 1GB
            Duration::from_secs(300) // 5 minutes
        } else if table_stats.size_bytes > 100 * 1024 * 1024 { // > 100MB
            Duration::from_secs(60) // 1 minute
        } else {
            Duration::from_secs(10) // 10 seconds
        };
        
        Ok(CreationCost {
            estimated_time,
            estimated_space: table_stats.size_bytes / 10, // Rough estimate
            blocks_writes: true,
        })
    }
    
    pub async fn create_index_concurrently(&self, suggestion: &IndexSuggestion) -> Result<()> {
        let index_name = format!(
            "idx_{}_{}_{}",
            suggestion.table_name,
            suggestion.columns.join("_"),
            chrono::Utc::now().timestamp()
        );
        
        let columns_sql = suggestion.columns.join(", ");
        let create_sql = format!(
            "CREATE INDEX CONCURRENTLY {} ON {} ({})",
            index_name,
            suggestion.table_name,
            columns_sql
        );
        
        log::info!("Creating index: {}", create_sql);
        
        sqlx::query(&create_sql)
            .execute(&*self.pool)
            .await?;
            
        log::info!("Index {} created successfully", index_name);
        
        Ok(())
    }
    
    pub async fn identify_unused_indexes(&self) -> Result<Vec<String>> {
        let unused_indexes = sqlx::query_scalar!(
            r#"
            SELECT indexname
            FROM pg_stat_user_indexes
            WHERE idx_scan = 0
            AND schemaname = 'public'
            AND indexname NOT LIKE '%_pkey'
            "#
        )
        .fetch_all(&*self.pool)
        .await?;
        
        Ok(unused_indexes)
    }
    
    pub async fn drop_unused_indexes(&self, dry_run: bool) -> Result<Vec<String>> {
        let unused_indexes = self.identify_unused_indexes().await?;
        let mut dropped = Vec::new();
        
        for index_name in unused_indexes {
            let drop_sql = format!("DROP INDEX CONCURRENTLY {}", index_name);
            
            if dry_run {
                log::info!("Would drop index: {}", index_name);
            } else {
                log::info!("Dropping unused index: {}", index_name);
                sqlx::query(&drop_sql)
                    .execute(&*self.pool)
                    .await?;
            }
            
            dropped.push(index_name);
        }
        
        Ok(dropped)
    }
}

#[derive(Debug)]
pub struct QueryPattern {
    pub table_name: String,
    pub query_template: String,
    pub filter_columns: Vec<String>,
    pub sort_columns: Vec<String>,
    pub frequency: u64,
}

#[derive(Debug)]
pub struct IndexSuggestion {
    pub table_name: String,
    pub columns: Vec<String>,
    pub index_type: IndexType,
    pub estimated_benefit: f64,
    pub creation_cost: CreationCost,
}

#[derive(Debug)]
pub struct CreationCost {
    pub estimated_time: Duration,
    pub estimated_space: i64,
    pub blocks_writes: bool,
}
```

## Connection Optimization

### Prepared Statement Cache
```rust
use sqlx::query::Query;
use std::sync::atomic::{AtomicU64, Ordering};

pub struct PreparedStatementCache {
    cache: Arc<RwLock<HashMap<String, CachedStatement>>>,
    metrics: Arc<CacheMetrics>,
    max_cache_size: usize,
}

#[derive(Clone)]
struct CachedStatement {
    statement_id: String,
    query: String,
    last_used: Instant,
    use_count: u64,
    param_types: Vec<String>,
}

struct CacheMetrics {
    hits: AtomicU64,
    misses: AtomicU64,
    evictions: AtomicU64,
}

impl PreparedStatementCache {
    pub fn new(max_cache_size: usize) -> Self {
        Self {
            cache: Arc::new(RwLock::new(HashMap::new())),
            metrics: Arc::new(CacheMetrics {
                hits: AtomicU64::new(0),
                misses: AtomicU64::new(0),
                evictions: AtomicU64::new(0),
            }),
            max_cache_size,
        }
    }
    
    pub async fn get_or_prepare(
        &self,
        pool: &PgPool,
        query: &str,
        param_types: Vec<String>,
    ) -> Result<String> {
        let query_hash = self.calculate_query_hash(query);
        
        // Check cache first
        {
            let mut cache = self.cache.write().await;
            if let Some(cached) = cache.get_mut(&query_hash) {
                cached.last_used = Instant::now();
                cached.use_count += 1;
                self.metrics.hits.fetch_add(1, Ordering::Relaxed);
                return Ok(cached.statement_id.clone());
            }
        }
        
        self.metrics.misses.fetch_add(1, Ordering::Relaxed);
        
        // Prepare the statement
        let statement_id = format!("stmt_{}", query_hash);
        let prepare_sql = format!("PREPARE {} AS {}", statement_id, query);
        
        sqlx::query(&prepare_sql)
            .execute(pool)
            .await?;
            
        // Add to cache
        let cached_statement = CachedStatement {
            statement_id: statement_id.clone(),
            query: query.to_string(),
            last_used: Instant::now(),
            use_count: 1,
            param_types,
        };
        
        {
            let mut cache = self.cache.write().await;
            
            // Evict if cache is full
            if cache.len() >= self.max_cache_size {
                self.evict_lru(&mut cache).await;
            }
            
            cache.insert(query_hash, cached_statement);
        }
        
        Ok(statement_id)
    }
    
    async fn evict_lru(&self, cache: &mut HashMap<String, CachedStatement>) {
        if let Some((lru_key, _)) = cache
            .iter()
            .min_by_key(|(_, stmt)| stmt.last_used)
            .map(|(k, v)| (k.clone(), v.clone()))
        {
            cache.remove(&lru_key);
            self.metrics.evictions.fetch_add(1, Ordering::Relaxed);
        }
    }
    
    pub async fn execute_prepared(
        &self,
        pool: &PgPool,
        statement_id: &str,
        params: &[QueryValue],
    ) -> Result<sqlx::postgres::PgQueryResult> {
        let param_placeholders: Vec<String> = params
            .iter()
            .enumerate()
            .map(|(i, _)| format!("${}", i + 1))
            .collect();
            
        let execute_sql = format!(
            "EXECUTE {} ({})",
            statement_id,
            param_placeholders.join(", ")
        );
        
        let mut query = sqlx::query(&execute_sql);
        for param in params {
            query = match param {
                QueryValue::String(s) => query.bind(s),
                QueryValue::Integer(i) => query.bind(i),
                QueryValue::Float(f) => query.bind(f),
                QueryValue::Boolean(b) => query.bind(b),
                _ => query, // Handle other types
            };
        }
        
        query.execute(pool).await.map_err(Into::into)
    }
    
    pub async fn get_cache_stats(&self) -> CacheStats {
        let hits = self.metrics.hits.load(Ordering::Relaxed);
        let misses = self.metrics.misses.load(Ordering::Relaxed);
        let evictions = self.metrics.evictions.load(Ordering::Relaxed);
        
        let cache_size = self.cache.read().await.len();
        
        CacheStats {
            hits,
            misses,
            evictions,
            hit_rate: if hits + misses > 0 {
                hits as f64 / (hits + misses) as f64
            } else {
                0.0
            },
            cache_size,
            max_cache_size: self.max_cache_size,
        }
    }
}

#[derive(Debug)]
pub struct CacheStats {
    pub hits: u64,
    pub misses: u64,
    pub evictions: u64,
    pub hit_rate: f64,
    pub cache_size: usize,
    pub max_cache_size: usize,
}
```

## Partitioning Strategies

### Table Partitioning Manager
```rust
pub struct PartitionManager {
    pool: Arc<PgPool>,
    partition_config: PartitionConfig,
}

#[derive(Clone)]
pub struct PartitionConfig {
    pub strategy: PartitionStrategy,
    pub maintenance_interval: Duration,
    pub retention_period: Duration,
    pub partition_size_threshold: i64,
}

#[derive(Clone)]
pub enum PartitionStrategy {
    Range(RangePartition),
    Hash(HashPartition),
    List(ListPartition),
}

#[derive(Clone)]
pub struct RangePartition {
    pub column: String,
    pub interval: PartitionInterval,
}

#[derive(Clone)]
pub enum PartitionInterval {
    Daily,
    Weekly,
    Monthly,
    Yearly,
}

impl PartitionManager {
    pub async fn create_partitioned_table(
        &self,
        table_name: &str,
        schema: &TableSchema,
        strategy: &PartitionStrategy,
    ) -> Result<()> {
        let partition_clause = match strategy {
            PartitionStrategy::Range(range) => {
                format!("PARTITION BY RANGE ({})", range.column)
            }
            PartitionStrategy::Hash(hash) => {
                format!("PARTITION BY HASH ({})", hash.column)
            }
            PartitionStrategy::List(list) => {
                format!("PARTITION BY LIST ({})", list.column)
            }
        };
        
        let create_sql = format!(
            r#"
            CREATE TABLE {} (
                {}
            ) {}
            "#,
            table_name,
            self.build_column_definitions(schema),
            partition_clause
        );
        
        sqlx::query(&create_sql)
            .execute(&*self.pool)
            .await?;
            
        log::info!("Created partitioned table: {}", table_name);
        
        Ok(())
    }
    
    pub async fn create_time_based_partitions(
        &self,
        table_name: &str,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
        interval: &PartitionInterval,
    ) -> Result<Vec<String>> {
        let mut partition_names = Vec::new();
        let mut current_date = start_date;
        
        while current_date < end_date {
            let (partition_name, next_date) = self.calculate_partition_bounds(
                table_name,
                current_date,
                interval,
            );
            
            let create_partition_sql = format!(
                r#"
                CREATE TABLE {} PARTITION OF {}
                FOR VALUES FROM ('{}') TO ('{}')
                "#,
                partition_name,
                table_name,
                current_date.format("%Y-%m-%d"),
                next_date.format("%Y-%m-%d")
            );
            
            sqlx::query(&create_partition_sql)
                .execute(&*self.pool)
                .await?;
                
            partition_names.push(partition_name);
            current_date = next_date;
        }
        
        log::info!("Created {} partitions for table {}", partition_names.len(), table_name);
        
        Ok(partition_names)
    }
    
    pub async fn auto_create_partitions(&self) -> Result<()> {
        let partitioned_tables = self.get_partitioned_tables().await?;
        
        for table in partitioned_tables {
            match table.strategy {
                PartitionStrategy::Range(ref range) if range.column.contains("date") => {
                    self.ensure_future_partitions(&table.name, &range.interval).await?;
                    self.cleanup_old_partitions(&table.name).await?;
                }
                _ => {}
            }
        }
        
        Ok(())
    }
    
    async fn ensure_future_partitions(
        &self,
        table_name: &str,
        interval: &PartitionInterval,
    ) -> Result<()> {
        let now = Utc::now();
        let future_date = match interval {
            PartitionInterval::Daily => now + Duration::days(7),     // 1 week ahead
            PartitionInterval::Weekly => now + Duration::weeks(4),   // 1 month ahead
            PartitionInterval::Monthly => now + Duration::days(90),  // 3 months ahead
            PartitionInterval::Yearly => now + Duration::days(365),  // 1 year ahead
        };
        
        // Check existing partitions
        let existing_partitions = self.get_existing_partitions(table_name).await?;
        let latest_partition_date = self.get_latest_partition_date(&existing_partitions)?;
        
        if latest_partition_date < future_date {
            self.create_time_based_partitions(
                table_name,
                latest_partition_date,
                future_date,
                interval,
            ).await?;
        }
        
        Ok(())
    }
    
    async fn cleanup_old_partitions(&self, table_name: &str) -> Result<()> {
        let cutoff_date = Utc::now() - self.partition_config.retention_period;
        
        let old_partitions = sqlx::query!(
            r#"
            SELECT schemaname, tablename
            FROM pg_tables
            WHERE tablename LIKE $1
            AND tablename < $2
            "#,
            format!("{}_%", table_name),
            format!("{}_{}", table_name, cutoff_date.format("%Y_%m_%d"))
        )
        .fetch_all(&*self.pool)
        .await?;
        
        for partition in old_partitions {
            let drop_sql = format!("DROP TABLE {}.{}", partition.schemaname, partition.tablename);
            
            sqlx::query(&drop_sql)
                .execute(&*self.pool)
                .await?;
                
            log::info!("Dropped old partition: {}", partition.tablename);
        }
        
        Ok(())
    }
    
    pub async fn analyze_partition_performance(&self, table_name: &str) -> Result<PartitionAnalysis> {
        let partitions = self.get_existing_partitions(table_name).await?;
        let mut partition_stats = Vec::new();
        
        for partition in partitions {
            let stats = sqlx::query!(
                r#"
                SELECT 
                    pg_size_pretty(pg_total_relation_size($1)) as size,
                    pg_total_relation_size($1) as size_bytes,
                    (SELECT reltuples FROM pg_class WHERE relname = $1) as row_count
                "#,
                partition
            )
            .fetch_one(&*self.pool)
            .await?;
            
            partition_stats.push(PartitionStats {
                name: partition,
                size_bytes: stats.size_bytes,
                row_count: stats.row_count.unwrap_or(0) as i64,
                last_vacuum: None, // Would need pg_stat_user_tables
                last_analyze: None,
            });
        }
        
        Ok(PartitionAnalysis {
            table_name: table_name.to_string(),
            partition_count: partition_stats.len(),
            total_size: partition_stats.iter().map(|p| p.size_bytes).sum(),
            total_rows: partition_stats.iter().map(|p| p.row_count).sum(),
            partitions: partition_stats,
            recommendations: self.generate_partition_recommendations(&partition_stats),
        })
    }
    
    fn generate_partition_recommendations(&self, stats: &[PartitionStats]) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        // Check for uneven partition sizes
        if let (Some(min_size), Some(max_size)) = (
            stats.iter().map(|p| p.size_bytes).min(),
            stats.iter().map(|p| p.size_bytes).max(),
        ) {
            if max_size > min_size * 10 {
                recommendations.push(
                    "Consider adjusting partition strategy - significant size imbalance detected".to_string()
                );
            }
        }
        
        // Check for very large partitions
        for partition in stats {
            if partition.size_bytes > self.partition_config.partition_size_threshold {
                recommendations.push(format!(
                    "Partition {} is very large ({}MB) - consider sub-partitioning",
                    partition.name,
                    partition.size_bytes / (1024 * 1024)
                ));
            }
        }
        
        recommendations
    }
}

#[derive(Debug)]
pub struct PartitionAnalysis {
    pub table_name: String,
    pub partition_count: usize,
    pub total_size: i64,
    pub total_rows: i64,
    pub partitions: Vec<PartitionStats>,
    pub recommendations: Vec<String>,
}

#[derive(Debug)]
pub struct PartitionStats {
    pub name: String,
    pub size_bytes: i64,
    pub row_count: i64,
    pub last_vacuum: Option<DateTime<Utc>>,
    pub last_analyze: Option<DateTime<Utc>>,
}
```

## Storage Optimization

### Compression and Storage
```rust
pub struct StorageOptimizer {
    pool: Arc<PgPool>,
}

impl StorageOptimizer {
    pub async fn analyze_table_bloat(&self) -> Result<Vec<TableBloatAnalysis>> {
        let bloat_query = r#"
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation,
                most_common_vals,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
            FROM pg_stats 
            JOIN pg_tables ON pg_stats.tablename = pg_tables.tablename
            WHERE schemaname = 'public'
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        "#;
        
        let results = sqlx::query!(bloat_query)
            .fetch_all(&*self.pool)
            .await?;
            
        let mut analyses = Vec::new();
        let mut current_table = None;
        let mut current_analysis = None;
        
        for row in results {
            let table_name = format!("{}.{}", row.schemaname, row.tablename);
            
            if current_table.as_ref() != Some(&table_name) {
                if let Some(analysis) = current_analysis.take() {
                    analyses.push(analysis);
                }
                
                current_table = Some(table_name.clone());
                current_analysis = Some(TableBloatAnalysis {
                    table_name: table_name.clone(),
                    size_bytes: row.size_bytes,
                    estimated_bloat: 0.0,
                    column_stats: Vec::new(),
                    recommendations: Vec::new(),
                });
            }
            
            if let Some(ref mut analysis) = current_analysis {
                analysis.column_stats.push(ColumnStats {
                    column_name: row.attname,
                    n_distinct: row.n_distinct.unwrap_or(0.0),
                    correlation: row.correlation.unwrap_or(0.0),
                });
            }
        }
        
        if let Some(analysis) = current_analysis {
            analyses.push(analysis);
        }
        
        // Add recommendations
        for analysis in &mut analyses {
            analysis.recommendations = self.generate_storage_recommendations(analysis);
        }
        
        Ok(analyses)
    }
    
    fn generate_storage_recommendations(&self, analysis: &TableBloatAnalysis) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        // Check table size
        if analysis.size_bytes > 1024 * 1024 * 1024 { // > 1GB
            recommendations.push("Consider partitioning this large table".to_string());
        }
        
        // Check for low correlation columns
        for column in &analysis.column_stats {
            if column.correlation.abs() < 0.1 && column.n_distinct > 100.0 {
                recommendations.push(format!(
                    "Column '{}' has low correlation - consider clustering or partitioning",
                    column.column_name
                ));
            }
        }
        
        // Check for high distinctness
        for column in &analysis.column_stats {
            if column.n_distinct > 10000.0 {
                recommendations.push(format!(
                    "Column '{}' has high cardinality - consider indexing strategies",
                    column.column_name
                ));
            }
        }
        
        recommendations
    }
    
    pub async fn optimize_table_storage(&self, table_name: &str) -> Result<StorageOptimizationResult> {
        let mut operations_performed = Vec::new();
        let start_size = self.get_table_size(table_name).await?;
        
        // Vacuum full to reclaim space
        log::info!("Running VACUUM FULL on {}", table_name);
        sqlx::query(&format!("VACUUM FULL {}", table_name))
            .execute(&*self.pool)
            .await?;
        operations_performed.push("VACUUM FULL".to_string());
        
        // Reindex to optimize indexes
        log::info!("Reindexing table {}", table_name);
        sqlx::query(&format!("REINDEX TABLE {}", table_name))
            .execute(&*self.pool)
            .await?;
        operations_performed.push("REINDEX".to_string());
        
        // Update statistics
        log::info!("Analyzing table {}", table_name);
        sqlx::query(&format!("ANALYZE {}", table_name))
            .execute(&*self.pool)
            .await?;
        operations_performed.push("ANALYZE".to_string());
        
        let end_size = self.get_table_size(table_name).await?;
        let space_saved = start_size - end_size;
        
        Ok(StorageOptimizationResult {
            table_name: table_name.to_string(),
            operations_performed,
            space_saved,
            size_before: start_size,
            size_after: end_size,
        })
    }
    
    async fn get_table_size(&self, table_name: &str) -> Result<i64> {
        let size = sqlx::query_scalar!(
            "SELECT pg_total_relation_size($1)",
            table_name
        )
        .fetch_one(&*self.pool)
        .await?;
        
        Ok(size.unwrap_or(0))
    }
}

#[derive(Debug)]
pub struct TableBloatAnalysis {
    pub table_name: String,
    pub size_bytes: i64,
    pub estimated_bloat: f64,
    pub column_stats: Vec<ColumnStats>,
    pub recommendations: Vec<String>,
}

#[derive(Debug)]
pub struct ColumnStats {
    pub column_name: String,
    pub n_distinct: f64,
    pub correlation: f64,
}

#[derive(Debug)]
pub struct StorageOptimizationResult {
    pub table_name: String,
    pub operations_performed: Vec<String>,
    pub space_saved: i64,
    pub size_before: i64,
    pub size_after: i64,
}
```