# Migration Strategies

## Schema Migration Framework

### Migration Manager
```rust
use sqlx::{PgPool, Postgres, Transaction};
use std::collections::HashMap;

pub struct MigrationManager {
    pool: Arc<PgPool>,
    migrations: Vec<Box<dyn Migration>>,
    config: MigrationConfig,
}

#[derive(Clone)]
pub struct MigrationConfig {
    pub migration_table: String,
    pub lock_timeout: Duration,
    pub batch_size: usize,
    pub verify_checksums: bool,
}

#[async_trait]
pub trait Migration: Send + Sync {
    fn version(&self) -> String;
    fn name(&self) -> &str;
    fn checksum(&self) -> String;
    async fn up(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()>;
    async fn down(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()>;
    fn is_reversible(&self) -> bool { true }
}

#[derive(Debug, sqlx::FromRow)]
struct MigrationRecord {
    version: String,
    name: String,
    checksum: String,
    applied_at: DateTime<Utc>,
    execution_time_ms: i64,
}

impl MigrationManager {
    pub async fn new(pool: Arc<PgPool>, config: MigrationConfig) -> Result<Self> {
        let manager = Self {
            pool,
            migrations: Vec::new(),
            config,
        };
        
        manager.ensure_migration_table().await?;
        Ok(manager)
    }
    
    pub fn add_migration(&mut self, migration: Box<dyn Migration>) {
        self.migrations.push(migration);
        // Sort by version
        self.migrations.sort_by(|a, b| a.version().cmp(&b.version()));
    }
    
    pub async fn migrate(&self) -> Result<MigrationResult> {
        let mut tx = self.pool.begin().await?;
        
        // Acquire migration lock
        self.acquire_migration_lock(&mut tx).await?;
        
        let applied_migrations = self.get_applied_migrations(&mut tx).await?;
        let pending_migrations = self.get_pending_migrations(&applied_migrations);
        
        let mut result = MigrationResult {
            applied_count: 0,
            skipped_count: 0,
            total_time: Duration::ZERO,
            migrations: Vec::new(),
        };
        
        for migration in pending_migrations {
            let start_time = Instant::now();
            
            log::info!("Applying migration: {} - {}", migration.version(), migration.name());
            
            // Verify checksum if configured
            if self.config.verify_checksums {
                if let Some(existing) = applied_migrations.get(&migration.version()) {
                    if existing.checksum != migration.checksum() {
                        return Err(anyhow!(
                            "Checksum mismatch for migration {}: expected {}, got {}",
                            migration.version(),
                            existing.checksum,
                            migration.checksum()
                        ));
                    }
                }
            }
            
            // Apply migration
            match migration.up(&mut tx).await {
                Ok(_) => {
                    let execution_time = start_time.elapsed();
                    
                    // Record successful migration
                    self.record_migration(&mut tx, migration, execution_time).await?;
                    
                    result.applied_count += 1;
                    result.total_time += execution_time;
                    result.migrations.push(MigrationExecutionResult {
                        version: migration.version(),
                        name: migration.name().to_string(),
                        execution_time,
                        success: true,
                        error: None,
                    });
                    
                    log::info!(
                        "Migration {} completed successfully in {:?}",
                        migration.version(),
                        execution_time
                    );
                }
                Err(e) => {
                    tx.rollback().await?;
                    return Err(anyhow!(
                        "Migration {} failed: {}",
                        migration.version(),
                        e
                    ));
                }
            }
        }
        
        tx.commit().await?;
        Ok(result)
    }
    
    async fn ensure_migration_table(&self) -> Result<()> {
        sqlx::query(&format!(
            r#"
            CREATE TABLE IF NOT EXISTS {} (
                version VARCHAR(255) PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                checksum VARCHAR(255) NOT NULL,
                applied_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                execution_time_ms BIGINT NOT NULL
            )
            "#,
            self.config.migration_table
        ))
        .execute(&*self.pool)
        .await?;
        
        Ok(())
    }
    
    async fn acquire_migration_lock(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        // Use PostgreSQL advisory locks
        sqlx::query("SELECT pg_advisory_xact_lock(12345)")
            .execute(&mut **tx)
            .await?;
            
        Ok(())
    }
    
    async fn get_applied_migrations(
        &self,
        tx: &mut Transaction<'_, Postgres>,
    ) -> Result<HashMap<String, MigrationRecord>> {
        let records = sqlx::query_as::<_, MigrationRecord>(&format!(
            "SELECT * FROM {} ORDER BY version",
            self.config.migration_table
        ))
        .fetch_all(&mut **tx)
        .await?;
        
        Ok(records.into_iter().map(|r| (r.version.clone(), r)).collect())
    }
    
    fn get_pending_migrations(
        &self,
        applied: &HashMap<String, MigrationRecord>,
    ) -> Vec<&Box<dyn Migration>> {
        self.migrations
            .iter()
            .filter(|m| !applied.contains_key(&m.version()))
            .collect()
    }
    
    async fn record_migration(
        &self,
        tx: &mut Transaction<'_, Postgres>,
        migration: &Box<dyn Migration>,
        execution_time: Duration,
    ) -> Result<()> {
        sqlx::query(&format!(
            r#"
            INSERT INTO {} (version, name, checksum, execution_time_ms)
            VALUES ($1, $2, $3, $4)
            "#,
            self.config.migration_table
        ))
        .bind(migration.version())
        .bind(migration.name())
        .bind(migration.checksum())
        .bind(execution_time.as_millis() as i64)
        .execute(&mut **tx)
        .await?;
        
        Ok(())
    }
}

#[derive(Debug)]
pub struct MigrationResult {
    pub applied_count: usize,
    pub skipped_count: usize,
    pub total_time: Duration,
    pub migrations: Vec<MigrationExecutionResult>,
}

#[derive(Debug)]
pub struct MigrationExecutionResult {
    pub version: String,
    pub name: String,
    pub execution_time: Duration,
    pub success: bool,
    pub error: Option<String>,
}
```

## SQL Migration Implementation

### File-Based Migrations
```rust
use std::path::{Path, PathBuf};
use sha2::{Digest, Sha256};

pub struct SqlMigration {
    version: String,
    name: String,
    up_sql: String,
    down_sql: Option<String>,
    checksum: String,
}

impl SqlMigration {
    pub fn from_file(file_path: &Path) -> Result<Self> {
        let filename = file_path
            .file_stem()
            .and_then(|s| s.to_str())
            .ok_or_else(|| anyhow!("Invalid filename"))?;
            
        // Parse filename: V001__create_users_table.sql
        let parts: Vec<&str> = filename.splitn(2, "__").collect();
        if parts.len() != 2 {
            return Err(anyhow!("Invalid migration filename format"));
        }
        
        let version = parts[0].to_string();
        let name = parts[1].replace('_', " ");
        
        let up_sql = std::fs::read_to_string(file_path)?;
        
        // Look for corresponding down migration
        let down_path = file_path
            .parent()
            .unwrap()
            .join(format!("{}__down.sql", parts[0]));
            
        let down_sql = if down_path.exists() {
            Some(std::fs::read_to_string(down_path)?)
        } else {
            None
        };
        
        // Calculate checksum
        let mut hasher = Sha256::new();
        hasher.update(up_sql.as_bytes());
        if let Some(ref down) = down_sql {
            hasher.update(down.as_bytes());
        }
        let checksum = format!("{:x}", hasher.finalize());
        
        Ok(Self {
            version,
            name,
            up_sql,
            down_sql,
            checksum,
        })
    }
    
    pub fn from_directory(dir_path: &Path) -> Result<Vec<Box<dyn Migration>>> {
        let mut migrations: Vec<Box<dyn Migration>> = Vec::new();
        
        for entry in std::fs::read_dir(dir_path)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.extension().and_then(|s| s.to_str()) == Some("sql") 
                && !path.file_name().unwrap().to_str().unwrap().contains("__down") {
                let migration = SqlMigration::from_file(&path)?;
                migrations.push(Box::new(migration));
            }
        }
        
        Ok(migrations)
    }
}

#[async_trait]
impl Migration for SqlMigration {
    fn version(&self) -> String {
        self.version.clone()
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn checksum(&self) -> String {
        self.checksum.clone()
    }
    
    async fn up(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        sqlx::query(&self.up_sql)
            .execute(&mut **tx)
            .await?;
        Ok(())
    }
    
    async fn down(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        if let Some(ref down_sql) = self.down_sql {
            sqlx::query(down_sql)
                .execute(&mut **tx)
                .await?;
            Ok(())
        } else {
            Err(anyhow!("No down migration available"))
        }
    }
    
    fn is_reversible(&self) -> bool {
        self.down_sql.is_some()
    }
}
```

## Online Schema Changes

### Zero-Downtime Column Addition
```rust
pub struct ColumnAdditionMigration {
    table_name: String,
    column_name: String,
    column_type: String,
    default_value: Option<String>,
    nullable: bool,
}

impl ColumnAdditionMigration {
    pub fn new(
        table_name: String,
        column_name: String,
        column_type: String,
    ) -> Self {
        Self {
            table_name,
            column_name,
            column_type,
            default_value: None,
            nullable: true,
        }
    }
    
    pub fn with_default(mut self, default_value: String) -> Self {
        self.default_value = Some(default_value);
        self
    }
    
    pub fn not_null(mut self) -> Self {
        self.nullable = false;
        self
    }
}

#[async_trait]
impl Migration for ColumnAdditionMigration {
    fn version(&self) -> String {
        format!("add_column_{}_{}", self.table_name, self.column_name)
    }
    
    fn name(&self) -> &str {
        "Add Column"
    }
    
    fn checksum(&self) -> String {
        format!("{:x}", 
            Sha256::digest(format!("{}{}{}", 
                self.table_name, 
                self.column_name, 
                self.column_type
            ).as_bytes())
        )
    }
    
    async fn up(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        // Step 1: Add column as nullable with default
        let mut sql = format!(
            "ALTER TABLE {} ADD COLUMN {} {}",
            self.table_name,
            self.column_name,
            self.column_type
        );
        
        if let Some(ref default) = self.default_value {
            sql.push_str(&format!(" DEFAULT {}", default));
        }
        
        sqlx::query(&sql).execute(&mut **tx).await?;
        
        // Step 2: If not nullable, update existing rows first
        if !self.nullable && self.default_value.is_some() {
            let update_sql = format!(
                "UPDATE {} SET {} = {} WHERE {} IS NULL",
                self.table_name,
                self.column_name,
                self.default_value.as_ref().unwrap(),
                self.column_name
            );
            
            sqlx::query(&update_sql).execute(&mut **tx).await?;
            
            // Step 3: Add NOT NULL constraint
            let constraint_sql = format!(
                "ALTER TABLE {} ALTER COLUMN {} SET NOT NULL",
                self.table_name,
                self.column_name
            );
            
            sqlx::query(&constraint_sql).execute(&mut **tx).await?;
        }
        
        Ok(())
    }
    
    async fn down(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        let sql = format!(
            "ALTER TABLE {} DROP COLUMN {}",
            self.table_name,
            self.column_name
        );
        
        sqlx::query(&sql).execute(&mut **tx).await?;
        Ok(())
    }
}
```

### Table Rename with Dual Write
```rust
pub struct TableRenameMigration {
    old_table: String,
    new_table: String,
    dual_write_period: Duration,
}

impl TableRenameMigration {
    pub fn new(old_table: String, new_table: String) -> Self {
        Self {
            old_table,
            new_table,
            dual_write_period: Duration::from_days(7), // 1 week
        }
    }
}

#[async_trait]
impl Migration for TableRenameMigration {
    fn version(&self) -> String {
        format!("rename_table_{}_{}", self.old_table, self.new_table)
    }
    
    fn name(&self) -> &str {
        "Rename Table with Dual Write"
    }
    
    fn checksum(&self) -> String {
        format!("{:x}", 
            Sha256::digest(format!("{}{}", self.old_table, self.new_table).as_bytes())
        )
    }
    
    async fn up(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        // Step 1: Create new table with same structure
        let create_sql = format!(
            "CREATE TABLE {} (LIKE {} INCLUDING ALL)",
            self.new_table,
            self.old_table
        );
        sqlx::query(&create_sql).execute(&mut **tx).await?;
        
        // Step 2: Copy existing data
        let copy_sql = format!(
            "INSERT INTO {} SELECT * FROM {}",
            self.new_table,
            self.old_table
        );
        sqlx::query(&copy_sql).execute(&mut **tx).await?;
        
        // Step 3: Create triggers for dual write
        self.create_dual_write_triggers(tx).await?;
        
        // Step 4: Schedule cleanup (in a real system, this would be external)
        log::info!(
            "Dual write period started. Remember to clean up old table {} after {:?}",
            self.old_table,
            self.dual_write_period
        );
        
        Ok(())
    }
    
    async fn down(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        // Remove triggers
        self.drop_dual_write_triggers(tx).await?;
        
        // Drop new table
        let drop_sql = format!("DROP TABLE IF EXISTS {}", self.new_table);
        sqlx::query(&drop_sql).execute(&mut **tx).await?;
        
        Ok(())
    }
    
    async fn create_dual_write_triggers(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        // Create trigger function
        let function_sql = format!(
            r#"
            CREATE OR REPLACE FUNCTION sync_{}()
            RETURNS TRIGGER AS $$
            BEGIN
                IF TG_OP = 'INSERT' THEN
                    INSERT INTO {} SELECT NEW.*;
                    RETURN NEW;
                ELSIF TG_OP = 'UPDATE' THEN
                    UPDATE {} SET * = NEW.* WHERE id = NEW.id;
                    RETURN NEW;
                ELSIF TG_OP = 'DELETE' THEN
                    DELETE FROM {} WHERE id = OLD.id;
                    RETURN OLD;
                END IF;
                RETURN NULL;
            END;
            $$ LANGUAGE plpgsql;
            "#,
            self.old_table,
            self.new_table,
            self.new_table,
            self.new_table
        );
        sqlx::query(&function_sql).execute(&mut **tx).await?;
        
        // Create triggers
        for operation in &["INSERT", "UPDATE", "DELETE"] {
            let trigger_sql = format!(
                r#"
                CREATE TRIGGER sync_{}_{}
                AFTER {} ON {}
                FOR EACH ROW EXECUTE FUNCTION sync_{}()
                "#,
                self.old_table,
                operation.to_lowercase(),
                operation,
                self.old_table,
                self.old_table
            );
            sqlx::query(&trigger_sql).execute(&mut **tx).await?;
        }
        
        Ok(())
    }
    
    async fn drop_dual_write_triggers(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        for operation in &["insert", "update", "delete"] {
            let drop_trigger_sql = format!(
                "DROP TRIGGER IF EXISTS sync_{}_{} ON {}",
                self.old_table,
                operation,
                self.old_table
            );
            sqlx::query(&drop_trigger_sql).execute(&mut **tx).await?;
        }
        
        let drop_function_sql = format!(
            "DROP FUNCTION IF EXISTS sync_{}()",
            self.old_table
        );
        sqlx::query(&drop_function_sql).execute(&mut **tx).await?;
        
        Ok(())
    }
}
```

## Data Migration Patterns

### Bulk Data Transformation
```rust
pub struct DataTransformationMigration {
    table_name: String,
    transform_fn: Box<dyn Fn(&serde_json::Value) -> Result<serde_json::Value> + Send + Sync>,
    batch_size: usize,
}

impl DataTransformationMigration {
    pub fn new<F>(table_name: String, transform_fn: F, batch_size: usize) -> Self
    where
        F: Fn(&serde_json::Value) -> Result<serde_json::Value> + Send + Sync + 'static,
    {
        Self {
            table_name,
            transform_fn: Box::new(transform_fn),
            batch_size,
        }
    }
}

#[async_trait]
impl Migration for DataTransformationMigration {
    fn version(&self) -> String {
        format!("transform_data_{}", self.table_name)
    }
    
    fn name(&self) -> &str {
        "Data Transformation"
    }
    
    fn checksum(&self) -> String {
        // In a real implementation, this would include the transformation logic hash
        format!("{:x}", Sha256::digest(self.table_name.as_bytes()))
    }
    
    async fn up(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        let mut offset = 0;
        
        loop {
            // Fetch batch of records
            let records = sqlx::query!(
                &format!(
                    "SELECT id, data FROM {} ORDER BY id LIMIT $1 OFFSET $2",
                    self.table_name
                ),
                self.batch_size as i64,
                offset
            )
            .fetch_all(&mut **tx)
            .await?;
            
            if records.is_empty() {
                break;
            }
            
            // Transform and update records
            for record in records {
                let original_data: serde_json::Value = record.data;
                let transformed_data = (self.transform_fn)(&original_data)?;
                
                sqlx::query!(
                    &format!("UPDATE {} SET data = $1 WHERE id = $2", self.table_name),
                    transformed_data,
                    record.id
                )
                .execute(&mut **tx)
                .await?;
            }
            
            offset += self.batch_size as i64;
            
            log::info!("Processed {} records", offset);
        }
        
        Ok(())
    }
    
    async fn down(&self, _tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        Err(anyhow!("Data transformation is not reversible"))
    }
    
    fn is_reversible(&self) -> bool {
        false
    }
}
```

### Incremental Migration
```rust
pub struct IncrementalMigration {
    name: String,
    version: String,
    batch_processor: Box<dyn BatchProcessor>,
    checkpoint_store: Arc<dyn CheckpointStore>,
}

#[async_trait]
pub trait BatchProcessor: Send + Sync {
    async fn process_batch(&self, tx: &mut Transaction<'_, Postgres>, offset: i64, limit: i64) -> Result<BatchResult>;
    fn batch_size(&self) -> usize;
}

#[async_trait]
pub trait CheckpointStore: Send + Sync {
    async fn save_checkpoint(&self, migration_id: &str, offset: i64) -> Result<()>;
    async fn load_checkpoint(&self, migration_id: &str) -> Result<Option<i64>>;
    async fn clear_checkpoint(&self, migration_id: &str) -> Result<()>;
}

#[derive(Debug)]
pub struct BatchResult {
    pub processed_count: usize,
    pub has_more: bool,
}

#[async_trait]
impl Migration for IncrementalMigration {
    fn version(&self) -> String {
        self.version.clone()
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn checksum(&self) -> String {
        format!("{:x}", Sha256::digest(self.version.as_bytes()))
    }
    
    async fn up(&self, tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        let migration_id = &self.version;
        let mut offset = self.checkpoint_store
            .load_checkpoint(migration_id)
            .await?
            .unwrap_or(0);
            
        loop {
            let result = self.batch_processor
                .process_batch(tx, offset, self.batch_processor.batch_size() as i64)
                .await?;
                
            offset += result.processed_count as i64;
            
            // Save checkpoint
            self.checkpoint_store
                .save_checkpoint(migration_id, offset)
                .await?;
                
            log::info!(
                "Migration {} processed {} records (total: {})",
                migration_id,
                result.processed_count,
                offset
            );
            
            if !result.has_more {
                break;
            }
        }
        
        // Clear checkpoint after completion
        self.checkpoint_store
            .clear_checkpoint(migration_id)
            .await?;
            
        Ok(())
    }
    
    async fn down(&self, _tx: &mut Transaction<'_, Postgres>) -> Result<()> {
        // Clear any existing checkpoint
        self.checkpoint_store
            .clear_checkpoint(&self.version)
            .await?;
            
        Ok(())
    }
}
```

## Migration Testing

### Migration Test Framework
```rust
pub struct MigrationTester {
    test_pool: Arc<PgPool>,
    migration_manager: MigrationManager,
}

impl MigrationTester {
    pub async fn new() -> Result<Self> {
        // Create test database
        let test_db_url = std::env::var("TEST_DATABASE_URL")
            .expect("TEST_DATABASE_URL must be set");
            
        let pool = PgPool::connect(&test_db_url).await?;
        
        let config = MigrationConfig {
            migration_table: "test_migrations".to_string(),
            lock_timeout: Duration::from_secs(30),
            batch_size: 1000,
            verify_checksums: true,
        };
        
        let migration_manager = MigrationManager::new(pool.clone().into(), config).await?;
        
        Ok(Self {
            test_pool: pool.into(),
            migration_manager,
        })
    }
    
    pub async fn test_migration_up_down(&self, migration: Box<dyn Migration>) -> Result<()> {
        // Create test data
        self.setup_test_data().await?;
        
        // Take snapshot before migration
        let before_snapshot = self.take_snapshot().await?;
        
        // Apply migration
        let mut tx = self.test_pool.begin().await?;
        migration.up(&mut tx).await?;
        let after_up_snapshot = self.take_snapshot().await?;
        tx.commit().await?;
        
        // Verify migration effects
        self.verify_migration_effects(&before_snapshot, &after_up_snapshot)?;
        
        // Reverse migration (if reversible)
        if migration.is_reversible() {
            let mut tx = self.test_pool.begin().await?;
            migration.down(&mut tx).await?;
            tx.commit().await?;
            
            let after_down_snapshot = self.take_snapshot().await?;
            
            // Verify rollback
            self.verify_rollback(&before_snapshot, &after_down_snapshot)?;
        }
        
        Ok(())
    }
    
    pub async fn test_migration_idempotency(&self, migration: Box<dyn Migration>) -> Result<()> {
        // Apply migration twice
        let mut tx = self.test_pool.begin().await?;
        migration.up(&mut tx).await?;
        tx.commit().await?;
        
        let first_snapshot = self.take_snapshot().await?;
        
        let mut tx = self.test_pool.begin().await?;
        let result = migration.up(&mut tx).await;
        
        match result {
            Ok(_) => {
                tx.commit().await?;
                let second_snapshot = self.take_snapshot().await?;
                
                // Verify snapshots are identical
                if first_snapshot != second_snapshot {
                    return Err(anyhow!("Migration is not idempotent"));
                }
            }
            Err(_) => {
                // Expected for non-idempotent migrations
                tx.rollback().await?;
            }
        }
        
        Ok(())
    }
    
    async fn setup_test_data(&self) -> Result<()> {
        // Create test tables and insert sample data
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS test_users (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255),
                email VARCHAR(255),
                created_at TIMESTAMP DEFAULT NOW()
            )
            "#
        )
        .execute(&*self.test_pool)
        .await?;
        
        sqlx::query(
            "INSERT INTO test_users (name, email) VALUES ('John Doe', '<EMAIL>')"
        )
        .execute(&*self.test_pool)
        .await?;
        
        Ok(())
    }
    
    async fn take_snapshot(&self) -> Result<DatabaseSnapshot> {
        let tables = self.get_table_schemas().await?;
        let data = self.get_table_data().await?;
        
        Ok(DatabaseSnapshot { tables, data })
    }
    
    async fn get_table_schemas(&self) -> Result<HashMap<String, TableSchema>> {
        let rows = sqlx::query!(
            r#"
            SELECT 
                table_name,
                column_name,
                data_type,
                is_nullable,
                column_default
            FROM information_schema.columns
            WHERE table_schema = 'public'
            ORDER BY table_name, ordinal_position
            "#
        )
        .fetch_all(&*self.test_pool)
        .await?;
        
        let mut schemas = HashMap::new();
        
        for row in rows {
            let table_name = row.table_name.unwrap();
            let schema = schemas
                .entry(table_name)
                .or_insert_with(|| TableSchema {
                    columns: Vec::new(),
                });
                
            schema.columns.push(ColumnSchema {
                name: row.column_name.unwrap(),
                data_type: row.data_type.unwrap(),
                is_nullable: row.is_nullable.unwrap() == "YES",
                default_value: row.column_default,
            });
        }
        
        Ok(schemas)
    }
    
    async fn get_table_data(&self) -> Result<HashMap<String, Vec<serde_json::Value>>> {
        // Simplified: just get row counts
        let tables = sqlx::query!(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
        )
        .fetch_all(&*self.test_pool)
        .await?;
        
        let mut data = HashMap::new();
        
        for table in tables {
            let table_name = table.table_name.unwrap();
            let count = sqlx::query_scalar::<_, i64>(&format!(
                "SELECT COUNT(*) FROM {}",
                table_name
            ))
            .fetch_one(&*self.test_pool)
            .await?;
            
            data.insert(table_name, vec![json!({"count": count})]);
        }
        
        Ok(data)
    }
}

#[derive(Debug, PartialEq)]
struct DatabaseSnapshot {
    tables: HashMap<String, TableSchema>,
    data: HashMap<String, Vec<serde_json::Value>>,
}

#[derive(Debug, PartialEq)]
struct TableSchema {
    columns: Vec<ColumnSchema>,
}

#[derive(Debug, PartialEq)]
struct ColumnSchema {
    name: String,
    data_type: String,
    is_nullable: bool,
    default_value: Option<String>,
}
```

## Migration Best Practices

### Safe Migration Guidelines
```rust
pub struct MigrationValidator;

impl MigrationValidator {
    pub fn validate_migration(migration: &dyn Migration) -> Vec<MigrationWarning> {
        let mut warnings = Vec::new();
        
        // Check for potentially dangerous operations
        if Self::contains_dangerous_operations(migration) {
            warnings.push(MigrationWarning::DangerousOperation);
        }
        
        // Check for large table operations
        if Self::affects_large_tables(migration) {
            warnings.push(MigrationWarning::LargeTableOperation);
        }
        
        // Check for non-atomic operations
        if Self::contains_non_atomic_operations(migration) {
            warnings.push(MigrationWarning::NonAtomicOperation);
        }
        
        warnings
    }
    
    fn contains_dangerous_operations(migration: &dyn Migration) -> bool {
        // Check migration content for dangerous patterns
        // This would analyze the SQL or migration logic
        false // Simplified
    }
    
    fn affects_large_tables(migration: &dyn Migration) -> bool {
        // Check if migration affects tables with many rows
        false // Simplified
    }
    
    fn contains_non_atomic_operations(migration: &dyn Migration) -> bool {
        // Check for operations that can't be rolled back
        !migration.is_reversible()
    }
}

#[derive(Debug)]
pub enum MigrationWarning {
    DangerousOperation,
    LargeTableOperation,
    NonAtomicOperation,
    PerformanceImpact,
    DataLoss,
}

pub struct SafeMigrationBuilder;

impl SafeMigrationBuilder {
    pub fn add_column_safely(
        table: &str,
        column: &str,
        data_type: &str,
    ) -> ColumnAdditionMigration {
        ColumnAdditionMigration::new(
            table.to_string(),
            column.to_string(),
            data_type.to_string(),
        )
        .with_default("NULL".to_string()) // Always start nullable
    }
    
    pub fn rename_column_safely(
        table: &str,
        old_name: &str,
        new_name: &str,
    ) -> Vec<Box<dyn Migration>> {
        vec![
            // Step 1: Add new column
            Box::new(ColumnAdditionMigration::new(
                table.to_string(),
                new_name.to_string(),
                "TEXT".to_string(), // Will be typed correctly later
            )),
            // Step 2: Copy data
            // Step 3: Update application to use new column
            // Step 4: Drop old column (separate deployment)
        ]
    }
}
```