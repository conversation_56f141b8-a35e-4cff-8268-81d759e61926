# Logging Patterns

## Structured Logging Setup

### Base Logger Configuration
```rust
use serde::{Serialize, Deserialize};
use tracing::{info, warn, error, debug, trace, span, Level};
use tracing_subscriber::{
    fmt::{self, format::FmtSpan},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Registry,
};
use tracing_bunyan_formatter::{BunyanFormattingLayer, JsonStorageLayer};

pub fn init_logging(service_name: &str, environment: &str) -> Result<()> {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new("info"));
    
    let formatting_layer = BunyanFormattingLayer::new(
        service_name.to_string(),
        std::io::stdout,
    );
    
    let subscriber = Registry::default()
        .with(env_filter)
        .with(JsonStorageLayer)
        .with(formatting_layer);
    
    tracing::subscriber::set_global_default(subscriber)?;
    
    // Add global fields
    tracing::subscriber::with_default(
        subscriber,
        || {
            info!(
                environment = environment,
                version = env!("CARGO_PKG_VERSION"),
                "Logger initialized"
            );
        },
    );
    
    Ok(())
}
```

### Structured Log Events
```rust
#[derive(Serialize, Deserialize)]
pub struct LogEvent {
    pub timestamp: DateTime<Utc>,
    pub level: String,
    pub message: String,
    pub service: String,
    pub environment: String,
    pub trace_id: Option<String>,
    pub span_id: Option<String>,
    pub correlation_id: Option<String>,
    #[serde(flatten)]
    pub fields: HashMap<String, serde_json::Value>,
}

#[derive(Clone)]
pub struct StructuredLogger {
    service: String,
    environment: String,
    default_fields: Arc<RwLock<HashMap<String, serde_json::Value>>>,
}

impl StructuredLogger {
    pub fn new(service: &str, environment: &str) -> Self {
        Self {
            service: service.to_string(),
            environment: environment.to_string(),
            default_fields: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    pub fn with_context(&self, context: HashMap<String, serde_json::Value>) -> Self {
        let mut logger = self.clone();
        *logger.default_fields.write().unwrap() = context;
        logger
    }
    
    pub fn info(&self, message: &str, fields: Option<HashMap<String, serde_json::Value>>) {
        let event = self.create_event("INFO", message, fields);
        self.emit(event);
    }
    
    pub fn error(&self, message: &str, error: &dyn std::error::Error, fields: Option<HashMap<String, serde_json::Value>>) {
        let mut event_fields = fields.unwrap_or_default();
        event_fields.insert("error".to_string(), json!({
            "message": error.to_string(),
            "type": std::any::type_name_of_val(error),
            "backtrace": format!("{:?}", error),
        }));
        
        let event = self.create_event("ERROR", message, Some(event_fields));
        self.emit(event);
    }
    
    fn create_event(
        &self,
        level: &str,
        message: &str,
        fields: Option<HashMap<String, serde_json::Value>>,
    ) -> LogEvent {
        let mut event_fields = self.default_fields.read().unwrap().clone();
        if let Some(fields) = fields {
            event_fields.extend(fields);
        }
        
        LogEvent {
            timestamp: Utc::now(),
            level: level.to_string(),
            message: message.to_string(),
            service: self.service.clone(),
            environment: self.environment.clone(),
            trace_id: Self::get_trace_id(),
            span_id: Self::get_span_id(),
            correlation_id: Self::get_correlation_id(),
            fields: event_fields,
        }
    }
    
    fn emit(&self, event: LogEvent) {
        println!("{}", serde_json::to_string(&event).unwrap());
    }
}
```

## Context Propagation

### Request Context Logger
```rust
use uuid::Uuid;

#[derive(Clone)]
pub struct RequestContext {
    pub request_id: String,
    pub user_id: Option<String>,
    pub session_id: Option<String>,
    pub correlation_id: String,
    pub trace_id: Option<String>,
    pub span_id: Option<String>,
}

impl RequestContext {
    pub fn new() -> Self {
        Self {
            request_id: Uuid::new_v4().to_string(),
            user_id: None,
            session_id: None,
            correlation_id: Uuid::new_v4().to_string(),
            trace_id: None,
            span_id: None,
        }
    }
    
    pub fn to_fields(&self) -> HashMap<String, serde_json::Value> {
        let mut fields = HashMap::new();
        
        fields.insert("request_id".to_string(), json!(self.request_id));
        fields.insert("correlation_id".to_string(), json!(self.correlation_id));
        
        if let Some(user_id) = &self.user_id {
            fields.insert("user_id".to_string(), json!(user_id));
        }
        
        if let Some(session_id) = &self.session_id {
            fields.insert("session_id".to_string(), json!(session_id));
        }
        
        if let Some(trace_id) = &self.trace_id {
            fields.insert("trace_id".to_string(), json!(trace_id));
        }
        
        if let Some(span_id) = &self.span_id {
            fields.insert("span_id".to_string(), json!(span_id));
        }
        
        fields
    }
}

// Thread-local storage for context
thread_local! {
    static REQUEST_CONTEXT: RefCell<Option<RequestContext>> = RefCell::new(None);
}

pub fn with_request_context<F, R>(context: RequestContext, f: F) -> R
where
    F: FnOnce() -> R,
{
    REQUEST_CONTEXT.with(|ctx| {
        *ctx.borrow_mut() = Some(context);
        let result = f();
        *ctx.borrow_mut() = None;
        result
    })
}

pub fn get_request_context() -> Option<RequestContext> {
    REQUEST_CONTEXT.with(|ctx| ctx.borrow().clone())
}
```

### Async Context Propagation
```rust
use tokio::task_local;

task_local! {
    static ASYNC_CONTEXT: RequestContext;
}

pub async fn with_async_context<F, R>(context: RequestContext, f: F) -> R
where
    F: Future<Output = R>,
{
    ASYNC_CONTEXT.scope(context, f).await
}

pub fn get_async_context() -> Option<RequestContext> {
    ASYNC_CONTEXT.try_with(|ctx| ctx.clone()).ok()
}

// Middleware for automatic context propagation
pub async fn context_middleware<B>(
    req: Request<B>,
    next: Next<B>,
) -> Result<Response, StatusCode> {
    let context = RequestContext {
        request_id: Uuid::new_v4().to_string(),
        correlation_id: req
            .headers()
            .get("x-correlation-id")
            .and_then(|v| v.to_str().ok())
            .map(|s| s.to_string())
            .unwrap_or_else(|| Uuid::new_v4().to_string()),
        trace_id: req
            .headers()
            .get("x-trace-id")
            .and_then(|v| v.to_str().ok())
            .map(|s| s.to_string()),
        span_id: None,
        user_id: None,
        session_id: None,
    };
    
    with_async_context(context, next.run(req)).await
}
```

## Log Aggregation Patterns

### Log Buffer and Batch Sending
```rust
use crossbeam_channel::{bounded, Receiver, Sender};

pub struct LogBuffer {
    sender: Sender<LogEvent>,
    worker: Option<JoinHandle<()>>,
}

impl LogBuffer {
    pub fn new(
        batch_size: usize,
        flush_interval: Duration,
        sink: Box<dyn LogSink>,
    ) -> Self {
        let (sender, receiver) = bounded(10000);
        
        let worker = std::thread::spawn(move || {
            Self::worker_loop(receiver, batch_size, flush_interval, sink);
        });
        
        Self {
            sender,
            worker: Some(worker),
        }
    }
    
    pub fn log(&self, event: LogEvent) {
        if let Err(e) = self.sender.try_send(event) {
            eprintln!("Failed to buffer log: {}", e);
        }
    }
    
    fn worker_loop(
        receiver: Receiver<LogEvent>,
        batch_size: usize,
        flush_interval: Duration,
        mut sink: Box<dyn LogSink>,
    ) {
        let mut batch = Vec::with_capacity(batch_size);
        let mut last_flush = Instant::now();
        
        loop {
            match receiver.recv_timeout(Duration::from_millis(100)) {
                Ok(event) => {
                    batch.push(event);
                    
                    if batch.len() >= batch_size {
                        sink.send_batch(&batch);
                        batch.clear();
                        last_flush = Instant::now();
                    }
                }
                Err(_) => {
                    // Check if we should flush based on time
                    if !batch.is_empty() && last_flush.elapsed() >= flush_interval {
                        sink.send_batch(&batch);
                        batch.clear();
                        last_flush = Instant::now();
                    }
                }
            }
        }
    }
}

pub trait LogSink: Send {
    fn send_batch(&mut self, events: &[LogEvent]);
}
```

### Sampling and Filtering
```rust
pub struct LogSampler {
    sample_rate: f64,
    always_log_errors: bool,
    always_log_patterns: Vec<regex::Regex>,
}

impl LogSampler {
    pub fn should_log(&self, event: &LogEvent) -> bool {
        // Always log errors if configured
        if self.always_log_errors && event.level == "ERROR" {
            return true;
        }
        
        // Check always-log patterns
        for pattern in &self.always_log_patterns {
            if pattern.is_match(&event.message) {
                return true;
            }
        }
        
        // Random sampling
        rand::random::<f64>() < self.sample_rate
    }
}

pub struct AdaptiveSampler {
    base_rate: f64,
    current_rate: Arc<AtomicU64>,
    adjustment_interval: Duration,
    target_throughput: usize,
}

impl AdaptiveSampler {
    pub async fn adjust_sample_rate(&self, current_throughput: usize) {
        let current = f64::from_bits(self.current_rate.load(Ordering::Relaxed));
        
        let adjustment = if current_throughput > self.target_throughput {
            // Reduce sampling
            0.9
        } else if current_throughput < self.target_throughput * 8 / 10 {
            // Increase sampling
            1.1
        } else {
            1.0
        };
        
        let new_rate = (current * adjustment).clamp(0.001, 1.0);
        self.current_rate.store(new_rate.to_bits(), Ordering::Relaxed);
    }
}
```

## Agent-Specific Logging

### Agent Activity Logger
```rust
#[derive(Serialize)]
pub struct AgentActivity {
    pub agent_id: String,
    pub agent_type: String,
    pub activity_type: String,
    pub task_id: Option<String>,
    pub duration_ms: Option<u64>,
    pub success: bool,
    pub metadata: HashMap<String, serde_json::Value>,
}

pub struct AgentLogger {
    base_logger: StructuredLogger,
    agent_id: String,
    agent_type: String,
}

impl AgentLogger {
    pub fn task_started(&self, task_id: &str, task_type: &str) {
        self.base_logger.info(
            "Task started",
            Some(hashmap! {
                "event_type".to_string() => json!("task_started"),
                "agent_id".to_string() => json!(self.agent_id),
                "agent_type".to_string() => json!(self.agent_type),
                "task_id".to_string() => json!(task_id),
                "task_type".to_string() => json!(task_type),
            }),
        );
    }
    
    pub fn task_completed(
        &self,
        task_id: &str,
        duration: Duration,
        result: &Result<serde_json::Value, String>,
    ) {
        let (success, result_data) = match result {
            Ok(data) => (true, Some(data.clone())),
            Err(e) => (false, Some(json!({"error": e}))),
        };
        
        self.base_logger.info(
            "Task completed",
            Some(hashmap! {
                "event_type".to_string() => json!("task_completed"),
                "agent_id".to_string() => json!(self.agent_id),
                "agent_type".to_string() => json!(self.agent_type),
                "task_id".to_string() => json!(task_id),
                "duration_ms".to_string() => json!(duration.as_millis()),
                "success".to_string() => json!(success),
                "result".to_string() => result_data.unwrap_or(json!(null)),
            }),
        );
    }
}
```

## Security and Compliance Logging

### Audit Logger
```rust
#[derive(Serialize)]
pub struct AuditEvent {
    pub timestamp: DateTime<Utc>,
    pub event_type: String,
    pub actor: Actor,
    pub resource: Resource,
    pub action: String,
    pub result: String,
    pub metadata: HashMap<String, serde_json::Value>,
}

#[derive(Serialize)]
pub struct Actor {
    pub id: String,
    pub actor_type: String,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
}

#[derive(Serialize)]
pub struct Resource {
    pub id: String,
    pub resource_type: String,
    pub parent_id: Option<String>,
}

pub struct AuditLogger {
    sink: Arc<dyn AuditSink>,
    encryption_key: Option<Vec<u8>>,
}

impl AuditLogger {
    pub async fn log_access(
        &self,
        actor: Actor,
        resource: Resource,
        action: &str,
        success: bool,
    ) {
        let event = AuditEvent {
            timestamp: Utc::now(),
            event_type: "access".to_string(),
            actor,
            resource,
            action: action.to_string(),
            result: if success { "success" } else { "failure" }.to_string(),
            metadata: HashMap::new(),
        };
        
        self.log_event(event).await;
    }
    
    async fn log_event(&self, mut event: AuditEvent) {
        // Encrypt sensitive fields if configured
        if let Some(key) = &self.encryption_key {
            event = self.encrypt_sensitive_fields(event, key);
        }
        
        // Send to audit sink
        self.sink.write(event).await;
    }
}

pub trait AuditSink: Send + Sync {
    async fn write(&self, event: AuditEvent);
}
```

### PII Redaction
```rust
use regex::Regex;

pub struct PIIRedactor {
    patterns: Vec<(Regex, String)>,
}

impl PIIRedactor {
    pub fn new() -> Self {
        Self {
            patterns: vec![
                // Email addresses
                (
                    Regex::new(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b").unwrap(),
                    "[REDACTED_EMAIL]".to_string(),
                ),
                // Credit card numbers
                (
                    Regex::new(r"\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b").unwrap(),
                    "[REDACTED_CC]".to_string(),
                ),
                // SSN
                (
                    Regex::new(r"\b\d{3}-\d{2}-\d{4}\b").unwrap(),
                    "[REDACTED_SSN]".to_string(),
                ),
                // Phone numbers
                (
                    Regex::new(r"\b\+?[1-9]\d{1,14}\b").unwrap(),
                    "[REDACTED_PHONE]".to_string(),
                ),
            ],
        }
    }
    
    pub fn redact(&self, text: &str) -> String {
        let mut result = text.to_string();
        
        for (pattern, replacement) in &self.patterns {
            result = pattern.replace_all(&result, replacement).to_string();
        }
        
        result
    }
    
    pub fn redact_json(&self, value: &mut serde_json::Value) {
        match value {
            serde_json::Value::String(s) => {
                *s = self.redact(s);
            }
            serde_json::Value::Object(map) => {
                for (_, v) in map.iter_mut() {
                    self.redact_json(v);
                }
            }
            serde_json::Value::Array(arr) => {
                for v in arr.iter_mut() {
                    self.redact_json(v);
                }
            }
            _ => {}
        }
    }
}
```

## Performance Logging

### Slow Query Logger
```rust
pub struct SlowQueryLogger {
    threshold: Duration,
    logger: StructuredLogger,
}

impl SlowQueryLogger {
    pub fn log_if_slow<T>(
        &self,
        query_type: &str,
        query: &str,
        duration: Duration,
        result: &Result<T, &dyn std::error::Error>,
    ) {
        if duration >= self.threshold {
            let mut fields = hashmap! {
                "query_type".to_string() => json!(query_type),
                "query".to_string() => json!(self.truncate_query(query)),
                "duration_ms".to_string() => json!(duration.as_millis()),
                "threshold_ms".to_string() => json!(self.threshold.as_millis()),
            };
            
            if let Err(e) = result {
                fields.insert("error".to_string(), json!(e.to_string()));
            }
            
            self.logger.warn("Slow query detected", Some(fields));
        }
    }
    
    fn truncate_query(&self, query: &str) -> String {
        const MAX_LENGTH: usize = 1000;
        
        if query.len() <= MAX_LENGTH {
            query.to_string()
        } else {
            format!("{}... [truncated]", &query[..MAX_LENGTH])
        }
    }
}
```

### Resource Usage Logger
```rust
pub struct ResourceLogger {
    logger: StructuredLogger,
    interval: Duration,
}

impl ResourceLogger {
    pub async fn start_monitoring(self) {
        let mut interval = tokio::time::interval(self.interval);
        
        loop {
            interval.tick().await;
            
            let usage = self.collect_resource_usage();
            
            self.logger.info(
                "Resource usage snapshot",
                Some(hashmap! {
                    "cpu_percent".to_string() => json!(usage.cpu_percent),
                    "memory_used_bytes".to_string() => json!(usage.memory_bytes),
                    "memory_percent".to_string() => json!(usage.memory_percent),
                    "open_files".to_string() => json!(usage.open_files),
                    "thread_count".to_string() => json!(usage.thread_count),
                    "connection_count".to_string() => json!(usage.connection_count),
                }),
            );
        }
    }
    
    fn collect_resource_usage(&self) -> ResourceUsage {
        // Implementation to collect system resource usage
        ResourceUsage {
            cpu_percent: 45.2,
            memory_bytes: 1024 * 1024 * 512, // 512MB
            memory_percent: 25.5,
            open_files: 42,
            thread_count: 8,
            connection_count: 15,
        }
    }
}
```

## Log Routing and Filtering

### Dynamic Log Router
```rust
pub struct LogRouter {
    routes: Vec<LogRoute>,
    default_sink: Box<dyn LogSink>,
}

struct LogRoute {
    predicate: Box<dyn Fn(&LogEvent) -> bool + Send + Sync>,
    sink: Box<dyn LogSink>,
}

impl LogRouter {
    pub fn route(&mut self, event: LogEvent) {
        let mut routed = false;
        
        for route in &mut self.routes {
            if (route.predicate)(&event) {
                route.sink.send_batch(&[event.clone()]);
                routed = true;
            }
        }
        
        if !routed {
            self.default_sink.send_batch(&[event]);
        }
    }
    
    pub fn add_route<P, S>(&mut self, predicate: P, sink: S)
    where
        P: Fn(&LogEvent) -> bool + Send + Sync + 'static,
        S: LogSink + 'static,
    {
        self.routes.push(LogRoute {
            predicate: Box::new(predicate),
            sink: Box::new(sink),
        });
    }
}

// Example route predicates
pub fn error_logs(event: &LogEvent) -> bool {
    event.level == "ERROR" || event.level == "FATAL"
}

pub fn security_logs(event: &LogEvent) -> bool {
    event.fields.contains_key("security_event")
}

pub fn performance_logs(event: &LogEvent) -> bool {
    event.fields.contains_key("duration_ms") || 
    event.fields.contains_key("query_time")
}
```