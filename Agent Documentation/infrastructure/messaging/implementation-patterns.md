# Messaging Implementation Patterns

## Message Handler Architecture

### Base Message Handler Trait
```rust
use async_trait::async_trait;
use serde::{Deserialize, Serialize};

#[async_trait]
pub trait MessageHandler: Send + Sync {
    type Message: DeserializeOwned + Send;
    type Response: Serialize + Send;
    type Error: std::error::Error + Send;
    
    async fn handle(
        &self,
        message: Self::Message,
        context: MessageContext,
    ) -> Result<Self::Response, Self::Error>;
    
    fn subject_pattern(&self) -> &str;
    
    fn queue_group(&self) -> Option<&str> {
        None
    }
}

pub struct MessageContext {
    pub message_id: String,
    pub correlation_id: Option<String>,
    pub reply_to: Option<String>,
    pub headers: HashMap<String, String>,
    pub received_at: Instant,
}
```

### Typed Message Router
```rust
pub struct MessageRouter {
    handlers: HashMap<String, Box<dyn MessageHandler>>,
    connection: Arc<Connection>,
    metrics: Arc<MessagingMetrics>,
}

impl MessageRouter {
    pub async fn route_message(&self, msg: Message) -> Result<()> {
        let subject = msg.subject.clone();
        let start = Instant::now();
        
        // Find matching handler
        let handler = self.handlers.iter()
            .find(|(pattern, _)| subject_matches(pattern, &subject))
            .map(|(_, h)| h);
            
        if let Some(handler) = handler {
            let context = MessageContext::from_nats_message(&msg);
            
            match handler.handle_raw(msg.data.clone(), context).await {
                Ok(response) => {
                    if let Some(reply) = msg.reply {
                        self.connection.publish(&reply, response).await?;
                    }
                    self.metrics.record_success(&subject, start.elapsed());
                }
                Err(e) => {
                    self.metrics.record_error(&subject, &e);
                    self.handle_error(msg, e).await?;
                }
            }
        } else {
            self.metrics.record_unhandled(&subject);
        }
        
        Ok(())
    }
}
```

## Agent Communication Patterns

### Agent Message Bus
```rust
pub struct AgentMessageBus {
    connection: Arc<Connection>,
    jetstream: Arc<jetstream::Context>,
    handlers: Arc<RwLock<HashMap<String, AgentHandler>>>,
}

impl AgentMessageBus {
    pub async fn send_command<C: Command>(
        &self,
        agent_id: &str,
        command: C,
    ) -> Result<C::Response> {
        let subject = format!("agents.{}.commands", agent_id);
        let payload = command.encode()?;
        
        let response = self.connection
            .request_timeout(&subject, payload, Duration::from_secs(30))
            .await?;
            
        C::Response::decode(&response.data)
    }
    
    pub async fn publish_event<E: Event>(
        &self,
        agent_id: &str,
        event: E,
    ) -> Result<()> {
        let subject = format!("agents.{}.events.{}", agent_id, E::event_type());
        let payload = event.encode()?;
        
        // Publish to JetStream for durability
        self.jetstream
            .publish(&subject, payload)
            .await?
            .await?; // Wait for ack
            
        Ok(())
    }
    
    pub async fn subscribe_to_events<E: Event>(
        &self,
        agent_pattern: &str,
        handler: impl Fn(E) -> BoxFuture<'static, Result<()>> + Send + Sync + 'static,
    ) -> Result<()> {
        let subject = format!("agents.{}.events.{}", agent_pattern, E::event_type());
        
        let subscription = self.connection
            .subscribe(&subject)
            .await?;
            
        tokio::spawn(async move {
            while let Some(msg) = subscription.next().await {
                if let Ok(event) = E::decode(&msg.data) {
                    if let Err(e) = handler(event).await {
                        log::error!("Event handler error: {}", e);
                    }
                }
            }
        });
        
        Ok(())
    }
}
```

### Task Distribution System
```rust
pub struct TaskDistributor {
    jetstream: Arc<jetstream::Context>,
    worker_pools: HashMap<String, WorkerPool>,
}

pub struct WorkerPool {
    task_type: String,
    workers: Vec<Worker>,
    load_balancer: Arc<LoadBalancer>,
}

impl TaskDistributor {
    pub async fn submit_task(
        &self,
        task: Task,
        priority: TaskPriority,
    ) -> Result<TaskHandle> {
        let subject = format!("tasks.{}.{}", priority.as_str(), task.task_type);
        
        // Add task metadata
        let mut headers = HeaderMap::new();
        headers.insert("task-id", task.id.clone());
        headers.insert("submitted-at", Utc::now().to_rfc3339());
        headers.insert("priority", priority.as_str());
        
        // Publish with deduplication
        let ack = self.jetstream
            .publish_with_headers(&subject, headers, task.encode()?)
            .await?
            .await?;
            
        Ok(TaskHandle {
            task_id: task.id,
            stream: ack.stream,
            sequence: ack.sequence,
        })
    }
    
    pub async fn create_worker_pool(
        &mut self,
        task_type: String,
        config: WorkerPoolConfig,
    ) -> Result<()> {
        let consumer = self.jetstream
            .pull_subscribe_with_options(
                &format!("tasks.*.{}", task_type),
                &ConsumerConfig {
                    durable_name: Some(format!("{}-workers", task_type)),
                    ack_policy: AckPolicy::Explicit,
                    max_deliver: config.max_retries,
                    ack_wait: Duration::from_secs(config.ack_timeout),
                    ..Default::default()
                },
            )
            .await?;
            
        let pool = WorkerPool::new(task_type.clone(), config, consumer);
        self.worker_pools.insert(task_type, pool);
        
        Ok(())
    }
}
```

## Retry and Circuit Breaker Patterns

### Exponential Backoff Retry
```rust
pub struct RetryConfig {
    pub max_attempts: u32,
    pub initial_delay: Duration,
    pub max_delay: Duration,
    pub multiplier: f64,
}

pub async fn with_retry<F, T, E>(
    config: RetryConfig,
    operation: F,
) -> Result<T, E>
where
    F: Fn() -> BoxFuture<'static, Result<T, E>>,
    E: std::error::Error,
{
    let mut attempt = 0;
    let mut delay = config.initial_delay;
    
    loop {
        match operation().await {
            Ok(result) => return Ok(result),
            Err(e) if attempt >= config.max_attempts - 1 => return Err(e),
            Err(e) => {
                log::warn!(
                    "Operation failed (attempt {}/{}): {}",
                    attempt + 1,
                    config.max_attempts,
                    e
                );
                
                tokio::time::sleep(delay).await;
                
                // Calculate next delay
                delay = Duration::from_secs_f64(
                    (delay.as_secs_f64() * config.multiplier)
                        .min(config.max_delay.as_secs_f64())
                );
                
                attempt += 1;
            }
        }
    }
}
```

### Circuit Breaker for Message Handlers
```rust
use std::sync::atomic::{AtomicU32, AtomicU64, Ordering};

pub struct CircuitBreaker {
    failure_threshold: u32,
    success_threshold: u32,
    timeout: Duration,
    
    failures: AtomicU32,
    successes: AtomicU32,
    state: Arc<RwLock<CircuitState>>,
    last_failure_time: AtomicU64,
}

#[derive(Clone, Copy)]
enum CircuitState {
    Closed,
    Open,
    HalfOpen,
}

impl CircuitBreaker {
    pub async fn call<F, T, E>(&self, f: F) -> Result<T, CircuitBreakerError<E>>
    where
        F: Future<Output = Result<T, E>>,
        E: std::error::Error,
    {
        let state = *self.state.read().await;
        
        match state {
            CircuitState::Open => {
                let last_failure = Duration::from_millis(
                    self.last_failure_time.load(Ordering::Relaxed)
                );
                
                if Instant::now().duration_since(UNIX_EPOCH) - last_failure > self.timeout {
                    *self.state.write().await = CircuitState::HalfOpen;
                } else {
                    return Err(CircuitBreakerError::Open);
                }
            }
            _ => {}
        }
        
        match f.await {
            Ok(result) => {
                self.on_success().await;
                Ok(result)
            }
            Err(e) => {
                self.on_failure().await;
                Err(CircuitBreakerError::CallFailed(e))
            }
        }
    }
    
    async fn on_success(&self) {
        self.failures.store(0, Ordering::Relaxed);
        let successes = self.successes.fetch_add(1, Ordering::Relaxed) + 1;
        
        let mut state = self.state.write().await;
        if *state == CircuitState::HalfOpen && successes >= self.success_threshold {
            *state = CircuitState::Closed;
            self.successes.store(0, Ordering::Relaxed);
        }
    }
    
    async fn on_failure(&self) {
        let failures = self.failures.fetch_add(1, Ordering::Relaxed) + 1;
        self.last_failure_time.store(
            Instant::now().duration_since(UNIX_EPOCH).as_millis() as u64,
            Ordering::Relaxed,
        );
        
        let mut state = self.state.write().await;
        if failures >= self.failure_threshold {
            *state = CircuitState::Open;
        }
    }
}
```

## Dead Letter Queue Pattern

### DLQ Implementation
```rust
pub struct DeadLetterQueue {
    jetstream: Arc<jetstream::Context>,
    max_retries: u32,
}

impl DeadLetterQueue {
    pub async fn setup(&self) -> Result<()> {
        // Create DLQ stream
        self.jetstream.add_stream(&StreamConfig {
            name: "DLQ".to_string(),
            subjects: vec!["dlq.>".to_string()],
            retention: RetentionPolicy::Limits,
            max_msgs: 100_000,
            max_age: Duration::from_secs(7 * 24 * 60 * 60), // 7 days
            storage: StorageType::File,
            ..Default::default()
        }).await?;
        
        Ok(())
    }
    
    pub async fn send_to_dlq(
        &self,
        original_subject: &str,
        message: &[u8],
        error: &str,
        attempt: u32,
    ) -> Result<()> {
        let dlq_subject = format!("dlq.{}", original_subject.replace('.', "_"));
        
        let mut headers = HeaderMap::new();
        headers.insert("original-subject", original_subject);
        headers.insert("error-message", error);
        headers.insert("failed-at", Utc::now().to_rfc3339());
        headers.insert("attempts", attempt.to_string());
        
        self.jetstream
            .publish_with_headers(&dlq_subject, headers, message.to_vec())
            .await?
            .await?;
            
        Ok(())
    }
    
    pub async fn process_dlq_messages(
        &self,
        handler: impl Fn(DLQMessage) -> BoxFuture<'static, Result<()>>,
    ) -> Result<()> {
        let consumer = self.jetstream
            .pull_subscribe("dlq.>")
            .await?;
            
        loop {
            let messages = consumer
                .fetch_with_timeout(10, Duration::from_secs(1))
                .await?;
                
            for msg in messages {
                let dlq_msg = DLQMessage::from_jetstream_message(msg)?;
                
                match handler(dlq_msg).await {
                    Ok(_) => msg.ack().await?,
                    Err(e) => {
                        log::error!("DLQ handler error: {}", e);
                        msg.nak().await?;
                    }
                }
            }
        }
    }
}
```

## Message Batching and Aggregation

### Batch Processor
```rust
pub struct BatchProcessor<T> {
    batch_size: usize,
    batch_timeout: Duration,
    buffer: Arc<Mutex<Vec<T>>>,
    processor: Arc<dyn Fn(Vec<T>) -> BoxFuture<'static, Result<()>> + Send + Sync>,
}

impl<T: Send + 'static> BatchProcessor<T> {
    pub fn new(
        batch_size: usize,
        batch_timeout: Duration,
        processor: impl Fn(Vec<T>) -> BoxFuture<'static, Result<()>> + Send + Sync + 'static,
    ) -> Self {
        let buffer = Arc::new(Mutex::new(Vec::with_capacity(batch_size)));
        let processor = Arc::new(processor);
        
        let batch_processor = Self {
            batch_size,
            batch_timeout,
            buffer: buffer.clone(),
            processor: processor.clone(),
        };
        
        // Start timeout checker
        let buffer_clone = buffer.clone();
        let processor_clone = processor.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(batch_timeout);
            
            loop {
                interval.tick().await;
                
                let mut batch = buffer_clone.lock().await;
                if !batch.is_empty() {
                    let items = std::mem::take(&mut *batch);
                    drop(batch);
                    
                    if let Err(e) = processor_clone(items).await {
                        log::error!("Batch processing error: {}", e);
                    }
                }
            }
        });
        
        batch_processor
    }
    
    pub async fn add(&self, item: T) -> Result<()> {
        let mut batch = self.buffer.lock().await;
        batch.push(item);
        
        if batch.len() >= self.batch_size {
            let items = std::mem::take(&mut *batch);
            drop(batch);
            
            (self.processor)(items).await?;
        }
        
        Ok(())
    }
}
```

### Message Aggregator
```rust
pub struct MessageAggregator {
    aggregations: Arc<RwLock<HashMap<String, AggregationState>>>,
    timeout: Duration,
}

struct AggregationState {
    messages: Vec<Message>,
    started_at: Instant,
    correlation_id: String,
}

impl MessageAggregator {
    pub async fn aggregate(
        &self,
        key: String,
        message: Message,
        expected_count: usize,
    ) -> Option<Vec<Message>> {
        let mut aggregations = self.aggregations.write().await;
        
        let state = aggregations
            .entry(key.clone())
            .or_insert_with(|| AggregationState {
                messages: Vec::new(),
                started_at: Instant::now(),
                correlation_id: Uuid::new_v4().to_string(),
            });
            
        state.messages.push(message);
        
        // Check if aggregation is complete
        if state.messages.len() >= expected_count {
            let state = aggregations.remove(&key).unwrap();
            return Some(state.messages);
        }
        
        // Check timeout
        if state.started_at.elapsed() > self.timeout {
            let state = aggregations.remove(&key).unwrap();
            log::warn!(
                "Aggregation timeout for key {}: got {} of {} messages",
                key,
                state.messages.len(),
                expected_count
            );
            return Some(state.messages);
        }
        
        None
    }
}
```

## Request-Reply Patterns

### Scatter-Gather
```rust
pub struct ScatterGather {
    connection: Arc<Connection>,
    timeout: Duration,
}

impl ScatterGather {
    pub async fn scatter<T, R>(
        &self,
        subject: &str,
        request: &T,
        min_responses: usize,
        max_responses: usize,
    ) -> Result<Vec<R>>
    where
        T: Serialize,
        R: DeserializeOwned,
    {
        let inbox = self.connection.new_inbox();
        let sub = self.connection.subscribe(&inbox).await?;
        
        // Publish request with reply-to
        let mut msg = Message::new(subject);
        msg.reply = Some(inbox.clone());
        msg.data = serde_json::to_vec(request)?;
        
        self.connection.publish_message(msg).await?;
        
        // Collect responses
        let mut responses = Vec::new();
        let deadline = Instant::now() + self.timeout;
        
        while responses.len() < max_responses && Instant::now() < deadline {
            match timeout_at(deadline, sub.next()).await {
                Ok(Some(msg)) => {
                    if let Ok(response) = serde_json::from_slice(&msg.data) {
                        responses.push(response);
                        
                        if responses.len() >= min_responses {
                            // We have enough responses, but continue collecting
                            // until timeout or max_responses
                        }
                    }
                }
                _ => break,
            }
        }
        
        if responses.len() < min_responses {
            return Err(anyhow!("Insufficient responses: got {}, need {}", 
                responses.len(), min_responses));
        }
        
        Ok(responses)
    }
}
```

## Stream Processing Patterns

### Event Sourcing
```rust
pub struct EventStore {
    jetstream: Arc<jetstream::Context>,
    stream_name: String,
}

impl EventStore {
    pub async fn append_event<E: Event>(
        &self,
        aggregate_id: &str,
        event: E,
        expected_version: Option<u64>,
    ) -> Result<u64> {
        let subject = format!("events.{}.{}", E::aggregate_type(), aggregate_id);
        
        let mut headers = HeaderMap::new();
        headers.insert("event-type", E::event_type());
        headers.insert("aggregate-id", aggregate_id);
        headers.insert("occurred-at", Utc::now().to_rfc3339());
        
        if let Some(version) = expected_version {
            headers.insert("expected-version", version.to_string());
        }
        
        let ack = self.jetstream
            .publish_with_headers(&subject, headers, event.encode()?)
            .await?
            .await?;
            
        Ok(ack.sequence)
    }
    
    pub async fn read_events<E: Event>(
        &self,
        aggregate_id: &str,
        from_version: u64,
    ) -> Result<Vec<(u64, E)>> {
        let subject = format!("events.{}.{}", E::aggregate_type(), aggregate_id);
        
        let consumer = self.jetstream
            .stream_info(&self.stream_name)
            .await?
            .config
            .subjects
            .iter()
            .find(|s| subject_matches(s, &subject))
            .ok_or_else(|| anyhow!("Subject not found in stream"))?;
            
        let messages = self.jetstream
            .stream_by_subject(&self.stream_name, &subject)
            .await?;
            
        let mut events = Vec::new();
        
        for msg in messages {
            if msg.sequence >= from_version {
                if let Ok(event) = E::decode(&msg.data) {
                    events.push((msg.sequence, event));
                }
            }
        }
        
        Ok(events)
    }
}
```

## Performance Optimizations

### Connection Pooling with Health Checks
```rust
pub struct HealthCheckedPool {
    connections: Vec<PooledConnection>,
    health_check_interval: Duration,
}

struct PooledConnection {
    connection: Arc<Connection>,
    healthy: Arc<AtomicBool>,
    last_used: Arc<Mutex<Instant>>,
}

impl HealthCheckedPool {
    pub async fn new(config: PoolConfig) -> Result<Self> {
        let mut connections = Vec::new();
        
        for i in 0..config.size {
            let conn = create_connection(&config, &format!("pool-{}", i)).await?;
            connections.push(PooledConnection {
                connection: Arc::new(conn),
                healthy: Arc::new(AtomicBool::new(true)),
                last_used: Arc::new(Mutex::new(Instant::now())),
            });
        }
        
        let pool = Self {
            connections,
            health_check_interval: config.health_check_interval,
        };
        
        // Start health checker
        pool.start_health_checks();
        
        Ok(pool)
    }
    
    fn start_health_checks(&self) {
        for conn in &self.connections {
            let connection = conn.connection.clone();
            let healthy = conn.healthy.clone();
            let interval = self.health_check_interval;
            
            tokio::spawn(async move {
                let mut ticker = tokio::time::interval(interval);
                
                loop {
                    ticker.tick().await;
                    
                    // Simple ping health check
                    match timeout(Duration::from_secs(5), connection.flush()).await {
                        Ok(Ok(_)) => healthy.store(true, Ordering::Relaxed),
                        _ => healthy.store(false, Ordering::Relaxed),
                    }
                }
            });
        }
    }
    
    pub fn get_healthy_connection(&self) -> Option<Arc<Connection>> {
        self.connections
            .iter()
            .filter(|c| c.healthy.load(Ordering::Relaxed))
            .min_by_key(|c| c.last_used.lock().unwrap().elapsed())
            .map(|c| {
                *c.last_used.lock().unwrap() = Instant::now();
                c.connection.clone()
            })
    }
}
```