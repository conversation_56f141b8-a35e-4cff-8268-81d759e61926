# Messaging Configuration Examples

## NATS Connection Configurations

### Basic Connection
```rust
use nats::aio::Connection;

pub async fn create_basic_connection() -> Result<Connection> {
    nats::aio::connect_with_options(
        "nats://localhost:4222",
        nats::Options::new()
            .name("agent-coordinator")
            .reconnect_buffer_size(64 * 1024 * 1024) // 64MB buffer
            .max_reconnects(10)
            .reconnect_delay_callback(|attempts| {
                std::time::Duration::from_millis(
                    std::cmp::min(attempts * 100, 5000)
                )
            })
    ).await
}
```

### Clustered Connection
```rust
pub struct ClusterConfig {
    pub servers: Vec<String>,
    pub auth_token: Option<String>,
    pub tls_config: Option<TlsConfig>,
}

impl ClusterConfig {
    pub async fn connect(&self) -> Result<Connection> {
        let mut opts = nats::Options::new();
        
        // Add all servers
        for server in &self.servers {
            opts = opts.server(server);
        }
        
        // Authentication
        if let Some(token) = &self.auth_token {
            opts = opts.token(token);
        }
        
        // TLS configuration
        if let Some(tls) = &self.tls_config {
            opts = opts
                .add_root_certificate(&tls.ca_cert)
                .client_cert(&tls.client_cert, &tls.client_key);
        }
        
        opts = opts
            .connection_timeout(Duration::from_secs(10))
            .ping_interval(Duration::from_secs(30))
            .flush_timeout(Duration::from_secs(5));
            
        nats::aio::connect_with_options("", opts).await
    }
}
```

### JetStream Configuration
```rust
pub struct JetStreamConfig {
    pub domain: Option<String>,
    pub api_prefix: Option<String>,
    pub timeout: Duration,
}

pub async fn create_jetstream_context(
    nc: Connection,
    config: JetStreamConfig,
) -> Result<jetstream::Context> {
    let mut js = jetstream::new(nc);
    
    if let Some(domain) = config.domain {
        js = js.domain(&domain);
    }
    
    if let Some(prefix) = config.api_prefix {
        js = js.api_prefix(&prefix);
    }
    
    js = js.timeout(config.timeout);
    
    Ok(js)
}
```

## Stream Configurations

### Agent Event Stream
```rust
pub async fn create_agent_event_stream(js: &jetstream::Context) -> Result<()> {
    js.add_stream(&StreamConfig {
        name: "AGENT_EVENTS".to_string(),
        subjects: vec![
            "agents.*.events.>".to_string(),
            "agents.*.status".to_string(),
        ],
        retention: RetentionPolicy::Limits,
        max_msgs: 1_000_000,
        max_bytes: 10 * 1024 * 1024 * 1024, // 10GB
        max_age: Duration::from_secs(24 * 60 * 60), // 24 hours
        max_msg_size: 1024 * 1024, // 1MB
        storage: StorageType::File,
        num_replicas: 3,
        ..Default::default()
    }).await?;
    
    Ok(())
}
```

### Task Queue Stream
```rust
pub async fn create_task_queue_stream(js: &jetstream::Context) -> Result<()> {
    js.add_stream(&StreamConfig {
        name: "TASK_QUEUE".to_string(),
        subjects: vec!["tasks.>".to_string()],
        retention: RetentionPolicy::WorkQueue,
        max_msgs: 50_000,
        max_age: Duration::from_secs(60 * 60), // 1 hour
        max_msg_size: 5 * 1024 * 1024, // 5MB
        storage: StorageType::Memory,
        num_replicas: 3,
        duplicate_window: Duration::from_secs(120), // 2 min dedup
        ..Default::default()
    }).await?;
    
    Ok(())
}
```

### Metrics Stream
```rust
pub async fn create_metrics_stream(js: &jetstream::Context) -> Result<()> {
    js.add_stream(&StreamConfig {
        name: "METRICS".to_string(),
        subjects: vec!["metrics.>".to_string()],
        retention: RetentionPolicy::Interest,
        max_msgs_per_subject: 1000,
        max_age: Duration::from_secs(7 * 24 * 60 * 60), // 7 days
        compression: StoreCompression::S2,
        first_seq: 1,
        ..Default::default()
    }).await?;
    
    Ok(())
}
```

## Consumer Configurations

### Durable Push Consumer
```rust
pub async fn create_task_processor_consumer(
    js: &jetstream::Context,
    deliver_subject: &str,
) -> Result<()> {
    js.add_consumer(
        "TASK_QUEUE",
        &ConsumerConfig {
            durable_name: Some("task-processor".to_string()),
            deliver_subject: Some(deliver_subject.to_string()),
            ack_policy: AckPolicy::Explicit,
            ack_wait: Duration::from_secs(30),
            max_deliver: 3,
            filter_subject: "tasks.high.*".to_string(),
            rate_limit: 1000, // 1000 msgs/sec
            ..Default::default()
        },
    ).await?;
    
    Ok(())
}
```

### Pull Consumer with Batching
```rust
pub async fn create_batch_processor_consumer(js: &jetstream::Context) -> Result<()> {
    js.add_consumer(
        "AGENT_EVENTS",
        &ConsumerConfig {
            durable_name: Some("event-processor".to_string()),
            ack_policy: AckPolicy::Explicit,
            ack_wait: Duration::from_secs(60),
            max_batch: 100,
            max_expires: Duration::from_secs(10),
            inactive_threshold: Duration::from_secs(300),
            ..Default::default()
        },
    ).await?;
    
    Ok(())
}
```

## Subject Hierarchy Examples

### Agent Communication Subjects
```rust
pub struct AgentSubjects {
    pub agent_type: String,
    pub agent_id: String,
}

impl AgentSubjects {
    pub fn command(&self) -> String {
        format!("agents.{}.{}.commands", self.agent_type, self.agent_id)
    }
    
    pub fn events(&self) -> String {
        format!("agents.{}.{}.events.*", self.agent_type, self.agent_id)
    }
    
    pub fn status(&self) -> String {
        format!("agents.{}.{}.status", self.agent_type, self.agent_id)
    }
    
    pub fn metrics(&self) -> String {
        format!("metrics.agents.{}.{}", self.agent_type, self.agent_id)
    }
}
```

### Task Distribution Subjects
```rust
pub enum TaskPriority {
    High,
    Medium,
    Low,
}

pub struct TaskSubjects;

impl TaskSubjects {
    pub fn submit(priority: TaskPriority, task_type: &str) -> String {
        let priority_str = match priority {
            TaskPriority::High => "high",
            TaskPriority::Medium => "medium",
            TaskPriority::Low => "low",
        };
        format!("tasks.{}.{}", priority_str, task_type)
    }
    
    pub fn result(task_id: &str) -> String {
        format!("results.{}", task_id)
    }
    
    pub fn progress(task_id: &str) -> String {
        format!("progress.{}", task_id)
    }
}
```

## Message Handler Configurations

### Subscription with Queue Group
```rust
pub struct QueueGroupConfig {
    pub name: String,
    pub concurrency: usize,
    pub buffer_size: usize,
}

pub async fn subscribe_with_queue_group(
    nc: &Connection,
    subject: &str,
    config: QueueGroupConfig,
) -> Result<()> {
    let sub = nc.queue_subscribe(subject, &config.name).await?;
    
    // Process messages with concurrency control
    let semaphore = Arc::new(Semaphore::new(config.concurrency));
    let (tx, mut rx) = mpsc::channel(config.buffer_size);
    
    // Message receiver task
    tokio::spawn(async move {
        while let Some(msg) = sub.next().await {
            if tx.send(msg).await.is_err() {
                break;
            }
        }
    });
    
    // Message processor tasks
    for _ in 0..config.concurrency {
        let sem = semaphore.clone();
        let mut rx = rx.clone();
        
        tokio::spawn(async move {
            while let Some(msg) = rx.recv().await {
                let _permit = sem.acquire().await.unwrap();
                process_message(msg).await;
            }
        });
    }
    
    Ok(())
}
```

### Request-Reply Pattern
```rust
pub struct RequestReplyConfig {
    pub timeout: Duration,
    pub max_retries: u32,
}

pub async fn request_with_retry<T, R>(
    nc: &Connection,
    subject: &str,
    payload: &T,
    config: RequestReplyConfig,
) -> Result<R>
where
    T: Serialize,
    R: DeserializeOwned,
{
    let data = serde_json::to_vec(payload)?;
    let mut attempts = 0;
    
    loop {
        match nc.request_timeout(subject, data.clone(), config.timeout).await {
            Ok(msg) => {
                return Ok(serde_json::from_slice(&msg.data)?);
            }
            Err(e) if attempts < config.max_retries => {
                attempts += 1;
                let delay = Duration::from_millis(100 * 2_u64.pow(attempts));
                tokio::time::sleep(delay).await;
            }
            Err(e) => return Err(e.into()),
        }
    }
}
```

## Connection Pool Configuration

### Multi-Purpose Connection Pool
```rust
pub struct ConnectionPoolConfig {
    pub core_size: usize,
    pub max_size: usize,
    pub idle_timeout: Duration,
}

pub struct ConnectionPool {
    connections: Vec<Arc<Connection>>,
    config: ConnectionPoolConfig,
}

impl ConnectionPool {
    pub async fn new(
        servers: Vec<String>,
        config: ConnectionPoolConfig,
    ) -> Result<Self> {
        let mut connections = Vec::new();
        
        for i in 0..config.core_size {
            let nc = create_connection(&servers, &format!("pool-{}", i)).await?;
            connections.push(Arc::new(nc));
        }
        
        Ok(Self { connections, config })
    }
    
    pub fn get_connection(&self) -> Arc<Connection> {
        // Simple round-robin selection
        let idx = rand::random::<usize>() % self.connections.len();
        self.connections[idx].clone()
    }
}
```

## Monitoring Configuration

### Message Metrics Collection
```rust
pub struct MessagingMetrics {
    messages_sent: IntCounterVec,
    messages_received: IntCounterVec,
    message_latency: HistogramVec,
    connection_errors: IntCounter,
}

impl MessagingMetrics {
    pub fn new() -> Self {
        Self {
            messages_sent: register_int_counter_vec!(
                "nats_messages_sent_total",
                "Total messages sent",
                &["subject", "status"]
            ).unwrap(),
            messages_received: register_int_counter_vec!(
                "nats_messages_received_total",
                "Total messages received",
                &["subject", "handler"]
            ).unwrap(),
            message_latency: register_histogram_vec!(
                "nats_message_latency_seconds",
                "Message processing latency",
                &["subject", "handler"],
                vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0]
            ).unwrap(),
            connection_errors: register_int_counter!(
                "nats_connection_errors_total",
                "Total connection errors"
            ).unwrap(),
        }
    }
}
```