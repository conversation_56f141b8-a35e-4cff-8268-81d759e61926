# Event Handling Systems

## Overview

The Event Handling System provides comprehensive event-driven architecture for the RUST-SS communication framework. This system enables asynchronous, decoupled communication between agents through publish-subscribe patterns, event streaming, and complex event processing.

## Architecture

### Core Event Framework

```rust
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, mpsc, broadcast};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use futures::future::BoxFuture;

pub struct EventBus {
    /// Topic-based subscribers
    subscribers: Arc<RwLock<HashMap<String, Vec<Subscriber>>>>,
    
    /// Event store for persistence and replay
    event_store: Arc<dyn EventStore>,
    
    /// Event processor for complex event processing
    event_processor: Arc<EventProcessor>,
    
    /// Metrics collection
    metrics: Arc<EventMetrics>,
    
    /// Security manager for event authorization
    security: Arc<SecurityManager>,
    
    /// Dead letter queue for failed events
    dead_letter_queue: Arc<DeadLetterQueue>,
    
    /// Event routing engine
    routing_engine: Arc<RoutingEngine>,
}

/// Core event structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Event {
    /// Unique event identifier
    pub id: EventId,
    
    /// Event type classification
    pub event_type: EventType,
    
    /// Event source information
    pub source: EventSource,
    
    /// Event payload data
    pub data: serde_json::Value,
    
    /// When the event occurred
    pub timestamp: DateTime<Utc>,
    
    /// Event version for schema evolution
    pub version: EventVersion,
    
    /// Event metadata
    pub metadata: EventMetadata,
    
    /// Correlation information
    pub correlation: EventCorrelation,
    
    /// Event routing information
    pub routing: EventRouting,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventEnvelope {
    /// The wrapped event
    pub event: Event,
    
    /// Topic this event was published to
    pub topic: String,
    
    /// Delivery attempt count
    pub delivery_attempts: u32,
    
    /// Processing deadline
    pub deadline: Option<DateTime<Utc>>,
    
    /// Routing path taken
    pub routing_path: Vec<String>,
    
    /// Processing state
    pub processing_state: ProcessingState,
}
```

### Event Types and Classification

```rust
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum EventType {
    // System events
    SystemStarted,
    SystemShutdown,
    SystemError,
    SystemConfigurationChanged,
    
    // Agent lifecycle events
    AgentSpawned,
    AgentTerminated,
    AgentStatusChanged,
    AgentError,
    AgentHeartbeat,
    
    // Task events
    TaskCreated,
    TaskAssigned,
    TaskStarted,
    TaskCompleted,
    TaskFailed,
    TaskCancelled,
    TaskProgressUpdated,
    
    // Swarm events
    SwarmFormed,
    SwarmDisbanded,
    SwarmStrategyChanged,
    SwarmAgentJoined,
    SwarmAgentLeft,
    SwarmMetricsUpdated,
    
    // Resource events
    ResourceAllocated,
    ResourceReleased,
    ResourceUtilizationChanged,
    ResourceError,
    
    // Communication events
    MessageSent,
    MessageReceived,
    MessageFailed,
    ConnectionEstablished,
    ConnectionLost,
    
    // Security events
    AuthenticationSuccess,
    AuthenticationFailure,
    AuthorizationDenied,
    SecurityViolation,
    
    // Performance events
    PerformanceThresholdExceeded,
    PerformanceImproved,
    BottleneckDetected,
    
    // Custom events
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventSource {
    /// Source identifier
    pub id: String,
    
    /// Source type
    pub source_type: SourceType,
    
    /// Source location
    pub location: Option<SourceLocation>,
    
    /// Source context
    pub context: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SourceType {
    Agent(AgentId),
    System(String),
    External(String),
    Swarm(SwarmId),
    Tool(String),
}
```

### Event Bus Implementation

```rust
impl EventBus {
    pub fn new(
        event_store: Arc<dyn EventStore>,
        security: Arc<SecurityManager>,
    ) -> Self {
        Self {
            subscribers: Arc::new(RwLock::new(HashMap::new())),
            event_store,
            event_processor: Arc::new(EventProcessor::new()),
            metrics: Arc::new(EventMetrics::new()),
            security,
            dead_letter_queue: Arc::new(DeadLetterQueue::new()),
            routing_engine: Arc::new(RoutingEngine::new()),
        }
    }
    
    /// Publish an event to a topic
    pub async fn publish<T>(&self, topic: &str, event_data: T) -> Result<EventId, EventError>
    where
        T: Serialize + Send + Sync + 'static,
    {
        // Create event
        let event = Event {
            id: EventId::new(),
            event_type: T::event_type(),
            source: self.get_current_source()?,
            data: serde_json::to_value(event_data)?,
            timestamp: Utc::now(),
            version: EventVersion::current(),
            metadata: EventMetadata::default(),
            correlation: EventCorrelation::new(),
            routing: EventRouting::new(),
        };
        
        // Security check
        self.security.authorize_publish(&event, topic).await?;
        
        // Store event
        self.event_store.store(&event).await?;
        
        // Create envelope
        let envelope = EventEnvelope {
            event: event.clone(),
            topic: topic.to_string(),
            delivery_attempts: 0,
            deadline: None,
            routing_path: vec![topic.to_string()],
            processing_state: ProcessingState::Pending,
        };
        
        // Route to subscribers
        self.route_event(envelope).await?;
        
        // Update metrics
        self.metrics.record_event_published(&event, topic).await;
        
        Ok(event.id)
    }
    
    /// Subscribe to events on a topic pattern
    pub async fn subscribe<F>(
        &self,
        pattern: &str,
        handler: F,
    ) -> Result<SubscriptionId, EventError>
    where
        F: Fn(EventEnvelope) -> BoxFuture<'_, Result<(), EventError>> + Send + Sync + 'static,
    {
        let subscription = Subscription {
            id: SubscriptionId::new(),
            pattern: pattern.to_string(),
            handler: Arc::new(handler),
            filter: EventFilter::default(),
            options: SubscriptionOptions::default(),
            created_at: Utc::now(),
            stats: SubscriptionStats::default(),
        };
        
        // Security check
        self.security.authorize_subscribe(&subscription).await?;
        
        // Add to subscribers
        let mut subscribers = self.subscribers.write().await;
        subscribers
            .entry(pattern.to_string())
            .or_default()
            .push(Subscriber::new(subscription.clone()));
        
        // Update metrics
        self.metrics.record_subscription_added(pattern).await;
        
        Ok(subscription.id)
    }
    
    /// Unsubscribe from events
    pub async fn unsubscribe(&self, subscription_id: SubscriptionId) -> Result<(), EventError> {
        let mut subscribers = self.subscribers.write().await;
        
        for (pattern, pattern_subscribers) in subscribers.iter_mut() {
            pattern_subscribers.retain(|sub| sub.subscription.id != subscription_id);
            
            if pattern_subscribers.is_empty() {
                self.metrics.record_subscription_removed(pattern).await;
            }
        }
        
        Ok(())
    }
    
    async fn route_event(&self, envelope: EventEnvelope) -> Result<(), EventError> {
        // Calculate routes
        let routes = self.routing_engine.calculate_routes(&envelope).await?;
        
        if routes.is_empty() {
            self.metrics.record_event_no_subscribers(&envelope.event).await;
            return Ok(());
        }
        
        // Deliver to each route
        for route in routes {
            self.deliver_to_route(envelope.clone(), route).await?;
        }
        
        Ok(())
    }
    
    async fn deliver_to_route(
        &self,
        mut envelope: EventEnvelope,
        route: DeliveryRoute,
    ) -> Result<(), EventError> {
        envelope.delivery_attempts += 1;
        envelope.processing_state = ProcessingState::InProgress;
        
        match route.delivery_method {
            DeliveryMethod::Direct(subscriber) => {
                self.deliver_direct(envelope, subscriber).await
            },
            DeliveryMethod::Buffered(buffer_config) => {
                self.deliver_buffered(envelope, buffer_config).await
            },
            DeliveryMethod::Streaming(stream_config) => {
                self.deliver_streaming(envelope, stream_config).await
            },
        }
    }
    
    async fn deliver_direct(
        &self,
        envelope: EventEnvelope,
        subscriber: Subscriber,
    ) -> Result<(), EventError> {
        let start_time = std::time::Instant::now();
        
        // Apply filters
        if !subscriber.filter.matches(&envelope.event) {
            return Ok(());
        }
        
        // Call handler
        let result = match tokio::time::timeout(
            subscriber.options.timeout,
            (subscriber.handler)(envelope.clone()),
        ).await {
            Ok(Ok(_)) => {
                self.metrics.record_event_delivered(&envelope.event, &subscriber).await;
                Ok(())
            },
            Ok(Err(error)) => {
                self.metrics.record_event_delivery_failed(&envelope.event, &subscriber, &error).await;
                Err(error)
            },
            Err(_timeout) => {
                let error = EventError::DeliveryTimeout(subscriber.subscription.id);
                self.metrics.record_event_delivery_failed(&envelope.event, &subscriber, &error).await;
                Err(error)
            },
        };
        
        let duration = start_time.elapsed();
        self.metrics.record_delivery_latency(&subscriber, duration).await;
        
        // Handle delivery failure
        if let Err(error) = result {
            self.handle_delivery_failure(envelope, subscriber, error).await?;
        }
        
        Ok(())
    }
    
    async fn handle_delivery_failure(
        &self,
        envelope: EventEnvelope,
        subscriber: Subscriber,
        error: EventError,
    ) -> Result<(), EventError> {
        let max_retries = subscriber.options.retry_policy.max_retries;
        
        if envelope.delivery_attempts < max_retries {
            // Schedule retry
            let retry_delay = subscriber.options.retry_policy
                .calculate_delay(envelope.delivery_attempts);
            
            tokio::spawn({
                let event_bus = self.clone();
                let envelope = envelope.clone();
                let subscriber = subscriber.clone();
                
                async move {
                    tokio::time::sleep(retry_delay).await;
                    let _ = event_bus.deliver_direct(envelope, subscriber).await;
                }
            });
        } else {
            // Send to dead letter queue
            self.dead_letter_queue.add(envelope, error).await?;
        }
        
        Ok(())
    }
}
```

### Event Processing Pipeline

```rust
pub struct EventProcessor {
    processors: Vec<Box<dyn EventProcessorStage>>,
    parallelism: usize,
    buffer_size: usize,
}

#[async_trait]
pub trait EventProcessorStage: Send + Sync {
    async fn process(&self, envelope: EventEnvelope) -> Result<EventEnvelope, EventError>;
    fn name(&self) -> &str;
    fn order(&self) -> u32;
}

/// Enrichment stage - adds context to events
pub struct EventEnrichmentProcessor {
    context_providers: Vec<Box<dyn ContextProvider>>,
}

#[async_trait]
impl EventProcessorStage for EventEnrichmentProcessor {
    async fn process(&self, mut envelope: EventEnvelope) -> Result<EventEnvelope, EventError> {
        for provider in &self.context_providers {
            let context = provider.get_context(&envelope.event).await?;
            envelope.event.metadata.context.extend(context);
        }
        
        envelope.event.metadata.enriched_at = Some(Utc::now());
        Ok(envelope)
    }
    
    fn name(&self) -> &str {
        "enrichment"
    }
    
    fn order(&self) -> u32 {
        1
    }
}

/// Transformation stage - transforms event data
pub struct EventTransformationProcessor {
    transformers: HashMap<EventType, Box<dyn EventTransformer>>,
}

#[async_trait]
impl EventProcessorStage for EventTransformationProcessor {
    async fn process(&self, mut envelope: EventEnvelope) -> Result<EventEnvelope, EventError> {
        if let Some(transformer) = self.transformers.get(&envelope.event.event_type) {
            envelope.event.data = transformer.transform(envelope.event.data).await?;
            envelope.event.metadata.transformed_at = Some(Utc::now());
        }
        
        Ok(envelope)
    }
    
    fn name(&self) -> &str {
        "transformation"
    }
    
    fn order(&self) -> u32 {
        2
    }
}

/// Aggregation stage - aggregates related events
pub struct EventAggregationProcessor {
    aggregators: HashMap<String, Box<dyn EventAggregator>>,
    window_manager: WindowManager,
}

#[async_trait]
impl EventProcessorStage for EventAggregationProcessor {
    async fn process(&self, envelope: EventEnvelope) -> Result<EventEnvelope, EventError> {
        // Check if this event should be aggregated
        let aggregation_key = self.calculate_aggregation_key(&envelope.event);
        
        if let Some(aggregator) = self.aggregators.get(&aggregation_key) {
            // Add to aggregation window
            self.window_manager.add_event(&aggregation_key, envelope.clone()).await?;
            
            // Check if window should be processed
            if self.window_manager.should_process(&aggregation_key).await? {
                let window_events = self.window_manager.get_window(&aggregation_key).await?;
                let aggregated_event = aggregator.aggregate(window_events).await?;
                
                // Create new envelope for aggregated event
                return Ok(EventEnvelope {
                    event: aggregated_event,
                    topic: envelope.topic,
                    delivery_attempts: 0,
                    deadline: envelope.deadline,
                    routing_path: envelope.routing_path,
                    processing_state: ProcessingState::Aggregated,
                });
            }
        }
        
        Ok(envelope)
    }
    
    fn name(&self) -> &str {
        "aggregation"
    }
    
    fn order(&self) -> u32 {
        3
    }
}
```

### Complex Event Processing

```rust
pub struct ComplexEventProcessor {
    patterns: Vec<EventPattern>,
    state_machine: StateMachine,
    temporal_engine: TemporalEngine,
}

#[derive(Debug, Clone)]
pub struct EventPattern {
    pub id: PatternId,
    pub name: String,
    pub description: String,
    pub conditions: Vec<EventCondition>,
    pub temporal_constraints: Vec<TemporalConstraint>,
    pub action: PatternAction,
}

#[derive(Debug, Clone)]
pub enum EventCondition {
    EventType(EventType),
    FieldEquals(String, serde_json::Value),
    FieldGreaterThan(String, f64),
    FieldLessThan(String, f64),
    FieldMatches(String, String), // field, regex
    And(Vec<EventCondition>),
    Or(Vec<EventCondition>),
    Not(Box<EventCondition>),
}

#[derive(Debug, Clone)]
pub enum TemporalConstraint {
    Within(Duration),                    // Events must occur within duration
    Sequence(Vec<EventType>),           // Events must occur in sequence
    Before(EventType, EventType),       // Event A must occur before Event B
    After(EventType, EventType),        // Event A must occur after Event B
    Concurrent(Vec<EventType>),         // Events must occur concurrently
}

impl ComplexEventProcessor {
    pub async fn process_event(&self, envelope: EventEnvelope) -> Result<Vec<EventEnvelope>, EventError> {
        let mut generated_events = Vec::new();
        
        // Update temporal state
        self.temporal_engine.add_event(&envelope.event).await?;
        
        // Check each pattern
        for pattern in &self.patterns {
            if self.matches_pattern(&envelope.event, pattern).await? {
                // Generate derived event
                let derived_event = self.execute_pattern_action(&envelope.event, pattern).await?;
                
                let derived_envelope = EventEnvelope {
                    event: derived_event,
                    topic: format!("derived.{}", pattern.name),
                    delivery_attempts: 0,
                    deadline: None,
                    routing_path: vec![],
                    processing_state: ProcessingState::Derived,
                };
                
                generated_events.push(derived_envelope);
            }
        }
        
        Ok(generated_events)
    }
    
    async fn matches_pattern(&self, event: &Event, pattern: &EventPattern) -> Result<bool, EventError> {
        // Check event conditions
        for condition in &pattern.conditions {
            if !self.evaluate_condition(event, condition).await? {
                return Ok(false);
            }
        }
        
        // Check temporal constraints
        for constraint in &pattern.temporal_constraints {
            if !self.temporal_engine.check_constraint(event, constraint).await? {
                return Ok(false);
            }
        }
        
        Ok(true)
    }
    
    async fn evaluate_condition(&self, event: &Event, condition: &EventCondition) -> Result<bool, EventError> {
        match condition {
            EventCondition::EventType(expected_type) => {
                Ok(event.event_type == *expected_type)
            },
            EventCondition::FieldEquals(field, expected_value) => {
                let actual_value = self.extract_field_value(&event.data, field)?;
                Ok(actual_value == *expected_value)
            },
            EventCondition::FieldGreaterThan(field, threshold) => {
                let value = self.extract_numeric_value(&event.data, field)?;
                Ok(value > *threshold)
            },
            EventCondition::FieldMatches(field, regex_pattern) => {
                let value = self.extract_string_value(&event.data, field)?;
                let regex = regex::Regex::new(regex_pattern)?;
                Ok(regex.is_match(&value))
            },
            EventCondition::And(conditions) => {
                for condition in conditions {
                    if !self.evaluate_condition(event, condition).await? {
                        return Ok(false);
                    }
                }
                Ok(true)
            },
            EventCondition::Or(conditions) => {
                for condition in conditions {
                    if self.evaluate_condition(event, condition).await? {
                        return Ok(true);
                    }
                }
                Ok(false)
            },
            EventCondition::Not(condition) => {
                Ok(!self.evaluate_condition(event, condition).await?)
            },
        }
    }
}
```

### Event Streaming

```rust
pub struct EventStream {
    topic: String,
    receiver: broadcast::Receiver<EventEnvelope>,
    buffer_size: usize,
    filter: Option<EventFilter>,
}

impl EventStream {
    pub async fn next(&mut self) -> Option<EventEnvelope> {
        loop {
            match self.receiver.recv().await {
                Ok(envelope) => {
                    // Apply filter if present
                    if let Some(ref filter) = self.filter {
                        if !filter.matches(&envelope.event) {
                            continue;
                        }
                    }
                    return Some(envelope);
                },
                Err(broadcast::error::RecvError::Lagged(skipped)) => {
                    tracing::warn!("Event stream lagged, skipped {} events", skipped);
                    continue;
                },
                Err(broadcast::error::RecvError::Closed) => {
                    return None;
                },
            }
        }
    }
    
    pub fn filter(mut self, filter: EventFilter) -> Self {
        self.filter = Some(filter);
        self
    }
    
    pub async fn collect_batch(&mut self, batch_size: usize) -> Vec<EventEnvelope> {
        let mut batch = Vec::with_capacity(batch_size);
        
        while batch.len() < batch_size {
            if let Some(envelope) = self.next().await {
                batch.push(envelope);
            } else {
                break;
            }
        }
        
        batch
    }
}

pub struct EventFilter {
    event_types: Option<Vec<EventType>>,
    source_types: Option<Vec<SourceType>>,
    field_filters: Vec<FieldFilter>,
    time_range: Option<TimeRange>,
}

impl EventFilter {
    pub fn matches(&self, event: &Event) -> bool {
        // Check event type filter
        if let Some(ref types) = self.event_types {
            if !types.contains(&event.event_type) {
                return false;
            }
        }
        
        // Check source type filter
        if let Some(ref source_types) = self.source_types {
            if !source_types.contains(&event.source.source_type) {
                return false;
            }
        }
        
        // Check field filters
        for filter in &self.field_filters {
            if !filter.matches(&event.data) {
                return false;
            }
        }
        
        // Check time range
        if let Some(ref time_range) = self.time_range {
            if !time_range.contains(event.timestamp) {
                return false;
            }
        }
        
        true
    }
}
```

### Event Store Implementation

```rust
#[async_trait]
pub trait EventStore: Send + Sync {
    async fn store(&self, event: &Event) -> Result<(), EventStoreError>;
    async fn get(&self, event_id: &EventId) -> Result<Option<Event>, EventStoreError>;
    async fn get_events_by_type(&self, event_type: EventType, limit: usize) -> Result<Vec<Event>, EventStoreError>;
    async fn get_events_by_source(&self, source: &EventSource, limit: usize) -> Result<Vec<Event>, EventStoreError>;
    async fn get_events_in_range(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> Result<Vec<Event>, EventStoreError>;
    async fn replay_events(&self, from: DateTime<Utc>) -> Result<EventStream, EventStoreError>;
}

pub struct PostgresEventStore {
    pool: sqlx::PgPool,
    table_name: String,
}

#[async_trait]
impl EventStore for PostgresEventStore {
    async fn store(&self, event: &Event) -> Result<(), EventStoreError> {
        let query = format!(
            "INSERT INTO {} (id, event_type, source, data, timestamp, version, metadata) VALUES ($1, $2, $3, $4, $5, $6, $7)",
            self.table_name
        );
        
        sqlx::query(&query)
            .bind(&event.id.to_string())
            .bind(&event.event_type.to_string())
            .bind(serde_json::to_value(&event.source)?)
            .bind(&event.data)
            .bind(event.timestamp)
            .bind(&event.version.to_string())
            .bind(serde_json::to_value(&event.metadata)?)
            .execute(&self.pool)
            .await?;
        
        Ok(())
    }
    
    async fn get(&self, event_id: &EventId) -> Result<Option<Event>, EventStoreError> {
        let query = format!("SELECT * FROM {} WHERE id = $1", self.table_name);
        
        let row = sqlx::query(&query)
            .bind(event_id.to_string())
            .fetch_optional(&self.pool)
            .await?;
        
        if let Some(row) = row {
            let event = Event {
                id: EventId::from_string(row.get("id")),
                event_type: serde_json::from_str(&row.get::<String, _>("event_type"))?,
                source: serde_json::from_value(row.get("source"))?,
                data: row.get("data"),
                timestamp: row.get("timestamp"),
                version: EventVersion::from_string(row.get("version")),
                metadata: serde_json::from_value(row.get("metadata"))?,
                correlation: EventCorrelation::default(),
                routing: EventRouting::default(),
            };
            
            Ok(Some(event))
        } else {
            Ok(None)
        }
    }
    
    async fn replay_events(&self, from: DateTime<Utc>) -> Result<EventStream, EventStoreError> {
        let query = format!(
            "SELECT * FROM {} WHERE timestamp >= $1 ORDER BY timestamp ASC",
            self.table_name
        );
        
        let mut rows = sqlx::query(&query)
            .bind(from)
            .fetch(&self.pool);
        
        let (sender, receiver) = broadcast::channel(1000);
        
        tokio::spawn(async move {
            while let Some(row) = rows.next().await {
                if let Ok(row) = row {
                    if let Ok(event) = Self::row_to_event(row) {
                        let envelope = EventEnvelope {
                            event,
                            topic: "replay".to_string(),
                            delivery_attempts: 0,
                            deadline: None,
                            routing_path: vec![],
                            processing_state: ProcessingState::Replayed,
                        };
                        
                        if sender.send(envelope).is_err() {
                            break; // No more receivers
                        }
                    }
                }
            }
        });
        
        Ok(EventStream {
            topic: "replay".to_string(),
            receiver,
            buffer_size: 1000,
            filter: None,
        })
    }
}
```

### Event Metrics and Monitoring

```rust
pub struct EventMetrics {
    registry: prometheus::Registry,
    events_published: prometheus::CounterVec,
    events_delivered: prometheus::CounterVec,
    delivery_latency: prometheus::HistogramVec,
    active_subscriptions: prometheus::GaugeVec,
    processing_duration: prometheus::HistogramVec,
    errors: prometheus::CounterVec,
}

impl EventMetrics {
    pub fn new() -> Self {
        let registry = prometheus::Registry::new();
        
        let events_published = prometheus::CounterVec::new(
            prometheus::Opts::new("events_published_total", "Total number of events published"),
            &["topic", "event_type"],
        ).unwrap();
        
        let events_delivered = prometheus::CounterVec::new(
            prometheus::Opts::new("events_delivered_total", "Total number of events delivered"),
            &["topic", "event_type", "subscriber"],
        ).unwrap();
        
        let delivery_latency = prometheus::HistogramVec::new(
            prometheus::HistogramOpts::new("event_delivery_duration_seconds", "Event delivery latency"),
            &["topic", "subscriber"],
        ).unwrap();
        
        registry.register(Box::new(events_published.clone())).unwrap();
        registry.register(Box::new(events_delivered.clone())).unwrap();
        registry.register(Box::new(delivery_latency.clone())).unwrap();
        
        Self {
            registry,
            events_published,
            events_delivered,
            delivery_latency,
            active_subscriptions: prometheus::GaugeVec::new(
                prometheus::Opts::new("active_subscriptions", "Number of active subscriptions"),
                &["topic"],
            ).unwrap(),
            processing_duration: prometheus::HistogramVec::new(
                prometheus::HistogramOpts::new("event_processing_duration_seconds", "Event processing duration"),
                &["stage"],
            ).unwrap(),
            errors: prometheus::CounterVec::new(
                prometheus::Opts::new("event_errors_total", "Total number of event errors"),
                &["error_type", "stage"],
            ).unwrap(),
        }
    }
    
    pub async fn record_event_published(&self, event: &Event, topic: &str) {
        self.events_published
            .with_label_values(&[topic, &event.event_type.to_string()])
            .inc();
    }
    
    pub async fn record_event_delivered(&self, event: &Event, subscriber: &Subscriber) {
        self.events_delivered
            .with_label_values(&[
                &subscriber.subscription.pattern,
                &event.event_type.to_string(),
                &subscriber.subscription.id.to_string(),
            ])
            .inc();
    }
    
    pub async fn record_delivery_latency(&self, subscriber: &Subscriber, duration: std::time::Duration) {
        self.delivery_latency
            .with_label_values(&[
                &subscriber.subscription.pattern,
                &subscriber.subscription.id.to_string(),
            ])
            .observe(duration.as_secs_f64());
    }
}
```

## Integration Examples

### Agent Event Integration

```rust
impl Agent for ResearchAgent {
    async fn initialize(&mut self) -> Result<(), AgentError> {
        // Subscribe to relevant events
        self.event_bus.subscribe("task.assigned.*", |envelope| {
            Box::pin(async move {
                if let Ok(task) = TaskAssignmentEvent::try_from(envelope.event) {
                    self.handle_task_assignment(task).await?;
                }
                Ok(())
            })
        }).await?;
        
        self.event_bus.subscribe("swarm.strategy.changed", |envelope| {
            Box::pin(async move {
                if let Ok(strategy) = SwarmStrategyEvent::try_from(envelope.event) {
                    self.update_strategy(strategy).await?;
                }
                Ok(())
            })
        }).await?;
        
        Ok(())
    }
    
    async fn complete_task(&mut self, task_id: TaskId, result: TaskResult) -> Result<(), AgentError> {
        // Publish task completion event
        self.event_bus.publish("task.completed", TaskCompletedEvent {
            task_id,
            agent_id: self.id.clone(),
            result,
            completion_time: Utc::now(),
            metrics: self.get_task_metrics(),
        }).await?;
        
        Ok(())
    }
}
```

This event handling system provides comprehensive, scalable, and reliable event-driven communication for the RUST-SS ecosystem.