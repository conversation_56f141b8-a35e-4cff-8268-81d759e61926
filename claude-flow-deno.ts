#!/usr/bin/env -S deno run --allow-all
/**
 * Deno wrapper for claude-flow
 * This script provides a Deno-compatible interface to the npm-installed claude-flow
 */

async function main() {
  const args = Deno.args;
  
  // Create the command to run claude-flow with all arguments
  const cmd = new Deno.Command("claude-flow", {
    args: args,
    stdout: "inherit",
    stderr: "inherit",
    stdin: "inherit",
  });

  try {
    const { code } = await cmd.output();
    Deno.exit(code);
  } catch (error) {
    console.error("Error running claude-flow:", error.message);
    
    // If claude-flow is not found, suggest installation
    if (error.message.includes("No such file or directory") || error.message.includes("command not found")) {
      console.error("\nClaude-flow not found. Please install it first:");
      console.error("npm install -g claude-flow");
    }
    
    Deno.exit(1);
  }
}

if (import.meta.main) {
  await main();
}
